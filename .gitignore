# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache
.pub/
.idea/
*.iml
.ios/Pods
.vscode/
*.dart.js
*.dart.js.map
*.info.json
*.js
*.js.map
*.node
#ios
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/ServiceDefinitions.json
ios/Flutter/Generated.xcconfig
ios/Flutter/Generated.xcconfig.dSYM
.ios/.swiftpm
# Web #
web/manifest.json
web/.dart_tool/

# macOS #
macos/Flutter/ephemeral/

# Linux #
linux/flutter/ephemeral/

# Windows #
windows/flutter/ephemeral/

# Node artifact files
build/
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

