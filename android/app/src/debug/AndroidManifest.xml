<manifest xmlns:android="http://schemas.android.com/apk/res/android"  xmlns:tools="http://schemas.android.com/tools"  package="vn.vnpt.camau.ioffice"> 
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
       <application
        android:label="VNPT iOffice Cà Mau"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
            <activity
                            android:name="net.openid.appauth.RedirectUriReceiverActivity"
                            android:theme="@style/Theme.AppCompat.Translucent.NoTitleBar"
                            android:exported="true"
                            tools:node="replace">
                        <intent-filter>
                            <action android:name="android.intent.action.VIEW"/>
                            <category android:name="android.intent.category.DEFAULT"/>
                            <category android:name="android.intent.category.BROWSABLE"/>
                            <data android:scheme="ioffice"
                                    android:host="com.vnpt.camau.ioffice"/>
                        </intent-filter>
            </activity>
       </application>
   
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    
</manifest>
