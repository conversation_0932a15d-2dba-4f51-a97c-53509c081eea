# Hướng dẫn áp dụng cỡ chữ động cho toàn bộ project

## Tổng quan
Hệ thống cỡ chữ động đã được tích hợp vào project, cho phép người dùng thay đổi cỡ chữ từ menu "<PERSON><PERSON><PERSON> hình" trong drawer.

## Các file đã được cập nhật

### 1. Core System
- ✅ `lib/modules/controllers/settings/settings_controller.dart` - Controller quản lý cỡ chữ
- ✅ `lib/modules/views/settings/settings_screen.dart` - Màn hình cấu hình
- ✅ `lib/core/utils/font_size_helper.dart` - Helper class cho cỡ chữ
- ✅ `lib/core/utils/global_font_updater.dart` - Utility cho migration
- ✅ `lib/routers/app_routers.dart` & `lib/routers/app_pages.dart` - Routes
- ✅ `lib/modules/controllers/common/setup_binding.dart` - Global binding

### 2. UI Components
- ✅ `lib/global_widget/navigation_drawer.dart` - Menu drawer
- ✅ `lib/modules/views/home/<USER>
- ✅ `lib/modules/views/hotro/hotro_screen.dart` - Hỗ trợ người dùng (một phần)

## Cách áp dụng cho các file còn lại

### Bước 1: Thêm import
```dart
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
```

### Bước 2: Thay thế GoogleFonts
**Trước:**
```dart
Text(
  'Hello World',
  style: GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Colors.black,
  ),
)
```

**Sau:**
```dart
Obx(() => Text(
  'Hello World',
  style: FontSizeHelper.getTextStyle(
    fontWeight: FontWeight.w500,
    color: Colors.black,
  ),
))
```

### Bước 3: Sử dụng các helper methods

#### Cho tiêu đề:
```dart
Obx(() => Text(
  'Tiêu đề',
  style: FontSizeHelper.getTitleStyle(color: Colors.black),
))
```

#### Cho subtitle:
```dart
Obx(() => Text(
  'Phụ đề',
  style: FontSizeHelper.getSubtitleStyle(color: Colors.grey),
))
```

#### Cho caption:
```dart
Obx(() => Text(
  'Chú thích',
  style: FontSizeHelper.getCaptionStyle(color: Colors.grey[600]),
))
```

#### Cho button:
```dart
Obx(() => Text(
  'Button Text',
  style: FontSizeHelper.getButtonStyle(color: Colors.white),
))
```

## Danh sách file cần cập nhật

### Screens chính:
- [ ] `lib/modules/views/login/login.dart`
- [ ] `lib/modules/views/vbde/` - Tất cả file văn bản đến
- [ ] `lib/modules/views/vbdi/` - Tất cả file văn bản đi
- [ ] `lib/modules/views/lichcongtac/` - Lịch công tác
- [ ] `lib/modules/views/tracuu/` - Tra cứu văn bản
- [ ] `lib/modules/views/user/` - Thông tin cá nhân

### Widgets chung:
- [ ] `lib/global_widget/header_appbar.dart`
- [ ] `lib/global_widget/custom_tree.dart`
- [ ] Các dialog và popup

### Form và Input:
- [ ] Tất cả TextField, TextFormField
- [ ] Dropdown, Button labels
- [ ] Validation messages

## Pattern thay thế tự động

### 1. GoogleFonts.inter() → FontSizeHelper.getTextStyle()
```bash
# Tìm kiếm pattern:
GoogleFonts\.inter\(([^)]*)\)

# Thay thế bằng:
FontSizeHelper.getTextStyle($1)
```

### 2. GoogleFonts.roboto() → FontSizeHelper.getTextStyle()
```bash
# Tìm kiếm pattern:
GoogleFonts\.roboto\(([^)]*)\)

# Thay thế bằng:
FontSizeHelper.getTextStyle($1)
```

### 3. Wrap Text với Obx()
```bash
# Tìm kiếm pattern:
Text\(([^,]+),\s*style:\s*FontSizeHelper

# Thay thế bằng:
Obx(() => Text($1, style: FontSizeHelper
```

## Lưu ý quan trọng

### 1. Performance
- Chỉ wrap Text widget với Obx() khi cần thiết
- Tránh nested Obx() không cần thiết

### 2. Consistency
- Sử dụng getTitleStyle() cho tiêu đề
- Sử dụng getSubtitleStyle() cho phụ đề
- Sử dụng getCaptionStyle() cho text nhỏ

### 3. Testing
- Test trên các cỡ chữ khác nhau
- Kiểm tra layout không bị vỡ
- Verify accessibility

## Ví dụ migration hoàn chỉnh

**File cũ:**
```dart
class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'My Screen',
          style: GoogleFonts.inter(fontSize: 18, fontWeight: FontWeight.w600),
        ),
      ),
      body: Column(
        children: [
          Text(
            'Content',
            style: GoogleFonts.inter(fontSize: 14),
          ),
          Text(
            'Caption',
            style: GoogleFonts.inter(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
```

**File mới:**
```dart
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';

class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(
          'My Screen',
          style: FontSizeHelper.getTitleStyle(fontWeight: FontWeight.w600),
        )),
      ),
      body: Column(
        children: [
          Obx(() => Text(
            'Content',
            style: FontSizeHelper.getTextStyle(),
          )),
          Obx(() => Text(
            'Caption',
            style: FontSizeHelper.getCaptionStyle(color: Colors.grey),
          )),
        ],
      ),
    );
  }
}
```

## Checklist hoàn thành
- [x] Core system setup
- [x] Settings screen
- [x] Navigation drawer
- [x] Home page (partial)
- [x] HoTro screen (partial)
- [ ] Login screen
- [ ] All VBDe screens
- [ ] All VBDi screens
- [ ] All other screens
- [ ] All widgets
- [ ] All dialogs
- [ ] Testing complete
