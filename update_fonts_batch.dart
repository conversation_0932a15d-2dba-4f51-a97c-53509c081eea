// Script để cập nhật fonts cho nhiều file cùng lúc
// Chạy: dart update_fonts_batch.dart

import 'dart:io';

void main() {
  print('🚀 Bắt đầu cập nhật fonts cho toàn bộ project...');

  // Danh sách các thư mục cần cập nhật
  final directories = [
    'lib/modules/views/vbde',
    'lib/modules/views/vbdi',
    'lib/modules/views/lichcongtac',
    'lib/modules/views/tracuu',
    'lib/modules/views/user',
    'lib/modules/views/hotro',
    'lib/modules/views/ttdh',
    'lib/global_widget',
  ];

  int totalFiles = 0;
  int updatedFiles = 0;

  for (String dirPath in directories) {
    final dir = Directory(dirPath);
    if (dir.existsSync()) {
      print('📁 Đang xử lý thư mục: $dirPath');

      final dartFiles = dir
          .listSync(recursive: true)
          .where((file) => file.path.endsWith('.dart'))
          .cast<File>();

      for (File file in dartFiles) {
        totalFiles++;
        if (updateFontInFile(file)) {
          updatedFiles++;
          print('  ✅ Đã cập nhật: ${file.path}');
        }
      }
    }
  }

  print('\n🎉 Hoàn thành!');
  print('📊 Tổng số file: $totalFiles');
  print('✨ Đã cập nhật: $updatedFiles file');
}

bool updateFontInFile(File file) {
  try {
    String content = file.readAsStringSync();
    String originalContent = content;
    bool hasChanges = false;

    // 1. Thêm import FontSizeHelper nếu chưa có
    if (!content.contains('font_size_helper.dart') &&
        (content.contains('GoogleFonts.') || content.contains('TextStyle('))) {
      // Tìm vị trí để thêm import
      final importMatch = RegExp(r"import 'package:vnpt_ioffice_camau/core/")
          .firstMatch(content);
      if (importMatch != null) {
        final insertPos = importMatch.start;
        content = content.substring(0, insertPos) +
            "import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';\n" +
            content.substring(insertPos);
        hasChanges = true;
      }
    }

    // 2. Thay thế GoogleFonts.inter()
    content = content.replaceAllMapped(
        RegExp(r'GoogleFonts\.inter\s*\(\s*([^)]*)\s*\)'), (match) {
      hasChanges = true;
      String params = match.group(1) ?? '';
      return 'FontSizeHelper.getTextStyle($params)';
    });

    // 3. Thay thế GoogleFonts.roboto()
    content = content.replaceAllMapped(
        RegExp(r'GoogleFonts\.roboto\s*\(\s*([^)]*)\s*\)'), (match) {
      hasChanges = true;
      String params = match.group(1) ?? '';
      return 'FontSizeHelper.getTextStyle($params)';
    });

    // 4. Wrap Text widgets với Obx() nếu cần
    content = content.replaceAllMapped(
        RegExp(
            r'Text\s*\(\s*([^,]+),\s*style:\s*FontSizeHelper\.([^)]+\([^)]*\))\s*\)'),
        (match) {
      hasChanges = true;
      String textContent = match.group(1) ?? '';
      String styleMethod = match.group(2) ?? '';
      return 'Obx(() => Text($textContent, style: FontSizeHelper.$styleMethod))';
    });

    // 5. Xử lý các TextStyle cơ bản
    content = content.replaceAllMapped(
        RegExp(
            r'TextStyle\s*\(\s*fontSize:\s*(\d+(?:\.\d+)?)\s*,?\s*([^)]*)\s*\)'),
        (match) {
      hasChanges = true;
      String otherParams = match.group(2) ?? '';
      if (otherParams.isNotEmpty && !otherParams.endsWith(',')) {
        otherParams = ', $otherParams';
      }
      return 'FontSizeHelper.getTextStyle($otherParams)';
    });

    // Chỉ ghi file nếu có thay đổi
    if (hasChanges && content != originalContent) {
      file.writeAsStringSync(content);
      return true;
    }

    return false;
  } catch (e) {
    print('  ❌ Lỗi khi xử lý ${file.path}: $e');
    return false;
  }
}
