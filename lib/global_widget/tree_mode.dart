import 'package:get/get.dart';

class TreeNodes {
  final String title;
  final List<TreeNodes> children;
  final String? id;
  final String? parentId;
  String? hoVaTen;
  String? diDong;
  String? tenChuVu;
  final RxBool expanded;
  final RxBool hidenRoot;

  TreeNodes(
      {required this.title,
      this.children = const [],
      this.id,
      this.parentId,
      this.hoVaTen,
      this.diDong,
      this.tenChuVu,
      bool expanded = false,
      bool hidenRoot = false})
      : expanded = expanded.obs,
        hidenRoot = hidenRoot.obs;
}
