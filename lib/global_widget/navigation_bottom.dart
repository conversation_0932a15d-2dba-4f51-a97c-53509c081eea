import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class NavigationBottom extends GetView {
  final HomeController homeController = Get.find<HomeController>();
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColor.blueAccentColor,
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, bottom: 5, top: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              icon: const Icon(Icons.home, color: AppColor.whiteColor),
              onPressed: () {
                navigator("home");
              },
            ),
            IconButton(
              icon: const Icon(Icons.arrow_circle_down_outlined,
                  color: AppColor.whiteColor),
              onPressed: () {
                if (homeController.isLanhDao.value == 1) {
                  navigator('duyetVbde');
                } else {
                  navigator('xulyVbde');
                }
              },
            ),
            IconButton(
              icon: const Icon(Icons.arrow_circle_up_outlined,
                  color: AppColor.whiteColor),
              onPressed: () {
                if (homeController.isLanhDao.value == 1) {
                  navigator('duyetVbdi');
                } else {
                  navigator('xulyVbdi');
                }
              },
            ),
            IconButton(
              icon: const Icon(
                Icons.checklist_outlined,
                color: AppColor.whiteColor,
              ),
              onPressed: () {
                navigator("ttdh");
              },
            ),
            IconButton(
              icon: const Icon(Icons.sync_alt_outlined,
                  color: AppColor.whiteColor),
              onPressed: () {
                navigator("vbnb");
              },
            ),
            IconButton(
              icon:
                  const Icon(Icons.calendar_month, color: AppColor.whiteColor),
              onPressed: () {
                navigator("lct");
              },
            ),
          ],
        ),
      ),
    );
  }

  navigator(String maScreen) {
    switch (maScreen) {
      case 'home':
        // gọi lấy nhắc việc mới khi redirect về home
        homeController.loadDSnv();
        Get.offNamed(Routers.HOME);
        break;
      case 'duyetVbde':
        Get.offNamed(Routers.VBDEN);
        break;
      case 'duyetVbdi':
        Get.offNamed(Routers.DUYETVBDI);
        break;
      case 'xulyVbde':
        Get.offNamed(Routers.XULYCVVBDE);
        break;
      case 'xulyVbdi':
        Get.offNamed(Routers.DSCVVBDI);
        break;
      case 'vbnb':
        Get.offNamed(Routers.VBNB);
        break;
      case 'ttdh':
        Get.offNamed(Routers.TTDH);
        break;
      case 'lct':
        Get.offNamed(Routers.LICHCONGTAC);
        break;
      default:
        break;
    }
  }
}
