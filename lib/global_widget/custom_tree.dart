import 'package:flutter/material.dart';

class TreeNode {
  final String label;
  final List<TreeNode> children;
  final String? id;
  final String? parentId;
  bool isCheckedxlc;
  bool isCheckedph;
  bool isCheckedxdb;
  bool isExpanded;
  final int level;

  TreeNode(
      {required this.label,
      required this.children,
      this.parentId,
      this.id,
      this.isCheckedxlc = false,
      this.isCheckedph = false,
      this.isCheckedxdb = false,
      this.isExpanded = true,
      this.level = 3});
}

class CustomTree extends StatefulWidget {
  final TreeNode rootNode;

  CustomTree({required this.rootNode});

  @override
  _CustomTreeState createState() => _CustomTreeState();
}

class _CustomTreeState extends State<CustomTree> {
  Widget _buildTree(TreeNode node) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              node.isExpanded = !node.isExpanded;
            });
          },
          child: Row(
            children: [
              if (node.isExpanded && node.children.isNotEmpty)
                const Expanded(
                    flex: 0,
                    child: Icon(
                      Icons.indeterminate_check_box,
                      color: Colors.grey,
                    ))
              else if (node.children.isEmpty)
                Expanded(flex: 0, child: Container())
              else
                const Expanded(
                    flex: 0,
                    child: Icon(
                      Icons.add_box,
                      color: Colors.grey,
                    )),
              Expanded(flex: 1, child: Text(node.label)),
              Expanded(
                flex: 0,
                child: Row(
                  children: [
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        side: const BorderSide(color: Colors.red),
                        value: node.isCheckedxlc,
                        onChanged: (value) {
                          setState(() {
                            node.isCheckedxlc = value ?? false;
                          });
                        },
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        value: node.isCheckedph,
                        side: const BorderSide(color: Colors.blue),
                        onChanged: (value) {
                          setState(() {
                            node.isCheckedph = value ?? false;
                          });
                        },
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        value: node.isCheckedxdb,
                        onChanged: (value) {
                          setState(() {
                            node.isCheckedxdb = value ?? false;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        if (node.isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: node.children.length,
              itemBuilder: (context, index) => _buildTree(node.children[index]),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: _buildTree(widget.rootNode),
    );
  }
}
