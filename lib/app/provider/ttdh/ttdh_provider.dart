import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_detail.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_model.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_xuly.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chuyenldk_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/ttdh_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class TtdhProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  // Get danh sach
  Future<MdThongTinDieuHanhNhan> auTtdhDaNhan(
      int page, int size, String tieuDe) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        'co_tep_tin': -1,
        'ma_can_bo': maCanBo,
        'ma_loai_ttdh': 0,
        'nam': 0,
        'nhan_tu_ngay': tuNgay,
        'nhan_den_ngay': denNgay,
        'page': page,
        'size': size,
        'tieu_de': tieuDe,
        'trang_thai_ttdh_gui': "-1"
      });
      final reponse = await dio.post(TtdhApi.auTTDHdanhan, data: data);
      return MdThongTinDieuHanhNhan.fromJson(reponse.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // get danh sach thông tin điều hành đã gửi
  Future<MdThongTinDieuHanhNhan> auThongTinDHdaGui(
      int page, int size, String tieuDe, String noiDung) async {
    try {
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCanBo = _store.read(GetStorageKey.maCanBo);
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/2000";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        'ma_can_bo': maCanBo,
        'ma_loai_ttdh': 0,
        'tieu_de': tieuDe,
        'noi_dung': noiDung,
        'trang_thai_ttdh': 1,
        'nam': 0,
        'gui_tu_ngay': tuNgay,
        'gui_den_ngay': denNgay,
        'co_tep_tin': -1,
        'total_row': 0,
        'page': page,
        'size': size,
      });
      final reponse = await dio.post(TtdhApi.auTTDHdaGui, data: data);
      return MdThongTinDieuHanhNhan.fromJson(reponse.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
  // thông tin chi tiết thông tin điều hành

  Future<DsTttdhDetail> auTDTTDHChiTiet(
      int maTtdhguiKc, int maCtcbGui, int maCtcbNhan) async {
    try {
      var data = FormData.fromMap({
        'ma_ttdh': maTtdhguiKc,
        'ma_ctcb_nhan': maCtcbNhan,
        'ma_ctcb_gui': maCtcbGui
      });
      final reponse = await dio.get(TtdhApi.auChiTietTTDHGui, data: data);
      return DsTttdhDetail.fromJson(reponse.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
  // lấy chi tiêt thông tin điều hành nhận

  Future<DsTttdhDetail> auTTTTDHChiTietNhan(
      int maTTDHGuiKc, int maCanBo) async {
    try {
      var data = FormData.fromMap(
          {'ma_ttdh_gui_kc': maTTDHGuiKc, 'ma_can_bo': maCanBo});
      final response = await dio.get(TtdhApi.auChiTietTTDHNhan, data: data);
      return DsTttdhDetail.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh sách cán bộ nhận
  Future<MdDsCanBoTtdh> auTTDHdsCanBoNhan(int maTtdhKc) async {
    try {
      var data = FormData.fromMap({'id': maTtdhKc});
      final response = await dio.get(TtdhApi.auTTDHdsCbNhan, data: data);
      return MdDsCanBoTtdh.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh sách loại thông điệp
  Future<MdLoaiThongDiep> auTTDHlayDsLoaiThongDiep() async {
    try {
      final response = await dio.get(TtdhApi.auTTDHLdsLtD);
      return MdLoaiThongDiep.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // gửi phản hồi thông tin điều hành
  Future<ResponseCommon> auTTDHGuiThongDiep(
      String chuoiMaCtcbNhan,
      String? chuoiMaVblq,
      String? chuoiSdtNhan,
      int maCtcbTao,
      int? maLoaiTtdh,
      int? maTtdhGoc,
      String? noiDung,
      String? srcVanbanLienQuan,
      String? srcFileTtdh,
      String tieuDe,
      int traLoiChoTtdh,
      int? maTtdhkc,
      int? chuyenTiepTuTTDH) async {
    try {
      var data = FormData.fromMap({
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
        "chuoi_ma_vblq": chuoiMaVblq,
        "chuoi_sdt_nhan": chuoiSdtNhan,
        "ma_ctcb_tao": maCtcbTao,
        "ma_loai_ttdh": maLoaiTtdh,
        "ma_ttdh_goc": maTtdhGoc,
        "noi_dung": noiDung,
        "tieu_de": tieuDe,
        "src_file_ttdh": srcFileTtdh,
        "src_van_ban_lien_quan": srcVanbanLienQuan,
        "tra_loi_cho_ttdh": traLoiChoTtdh,
        "ma_ttdh_kc": maTtdhkc,
        "chuyen_tiep_tu_ttdh": chuyenTiepTuTTDH
      });
      final response = await dio.post(TtdhApi.auTTDHGuiThongDiep, data: data);
      return ResponseCommon.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Thêm mới thông điệp
  Future<ResponseCommon> auTTDHluuThongDiep(
      int maTtdhKc,
      int maLoaiTtdh,
      int maCtcbTao,
      String tieuDe,
      String noiDung,
      String ngayGui,
      String trangThaiTtdh,
      int traLoiChoTtdh,
      int chuyenTiepTtdh,
      String chuoiMaCtcbNhan,
      int sms,
      int maTtdhGoc) async {
    try {
      var data = FormData.fromMap({
        "ma_ttdh_kc": maTtdhKc,
        "ma_loai_ttdh": maLoaiTtdh,
        "ma_ctcb_tao": maCtcbTao,
        "tieu_de": tieuDe,
        "noi_dung": noiDung,
        "ngay_gui": ngayGui,
        "trang_thai_ttdh": trangThaiTtdh,
        "tra_loi_cho_ttdh": traLoiChoTtdh,
        "chuyen_tiep_tu_ttdh": chuyenTiepTtdh,
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
        "sms": sms,
        "ma_ttdh_goc": maTtdhGoc
      });
      final response = await dio.post(TtdhApi.auTTDHLuuThongDiep, data: data);
      return ResponseCommon.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
  // Danh sách cán bộ nhận

  Future<TreeCbVaiTro> auTTDHDSCBN() async {
    try {
      var maDonVi = _store.read(GetStorageKey.maDonVi);
      var maCanBo = _store.read(GetStorageKey.maCanBo);
      var data = FormData.fromMap({"ma_don_vi": maDonVi, "ma_can_bo": maCanBo});
      final response = await dio.get(TtdhApi.auTTDHDSCBN, data: data);
      return TreeCbVaiTro.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Danh sách cán nhóm cán bộ nhận
  Future<TreeCbVaiTro> auTTDHDSNCBN() async {
    try {
      var maCanBo = _store.read(GetStorageKey.maCanBo);
      var data = FormData.fromMap({"ma_can_bo": maCanBo});
      final response = await dio.get(TtdhApi.auTTDHDSNCBN, data: data);
      return TreeCbVaiTro.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
