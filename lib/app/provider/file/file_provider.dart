import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/file/upload_file_model.dart';
import 'package:vnpt_ioffice_camau/app/model/kyso/FileSauKySo_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chuyenldk_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/file_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class FileProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();
  Future<dynamic> auUploadFileCntttlB64(int maCtcb, String chucNang,
      String tenFile, String base64, int maVanBanDi) async {
    try {
      var data = FormData.fromMap({
        "ma_ctcb": maCtcb,
        "chuc_nang": chucNang,
        "ten_file": tenFile,
        "base64": base64,
        "ma_van_ban_di": maVanBanDi
      });
      final response =
          await dio.post(FileApi.auUploadFileCntttltB64, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auFileCNLF(String chuoiXoa, String chuoiConLai,
      int maVanBanDi, int maCtcb, String trichYeu, String tenCanBoXoa) async {
    try {
      var data = FormData.fromMap({
        "chuoi_xoa": chuoiXoa,
        "chuoi_con_lai": chuoiConLai,
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb": maCtcb,
        "trich_yeu": trichYeu,
        "ten_can_bo_xoa": tenCanBoXoa
      });
      final response = await dio.post(FileApi.auFileCnlf, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auFileCdTTWP(String pathFile) async {
    try {
      var data = FormData.fromMap({'path': pathFile});
      var url = _store.read(GetStorageKey.domainFile) + FileApi.auFileCdTTWP;
      final response = await dio.get(url, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

// check dung lượng file
  Future<MdCheckDuLuongFile> auFileCDKF(File res, String chucNang) async {
    try {
      var year = DateTime.now().year;
      var maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      var data = FormData.fromMap({
        'dung_luong': res.length(),
        'ma_don_vi_quan_tri': maDonViQuanTri,
        'nam': year,
        'ten_chuc_nang': chucNang
      });
      final response = await dio.post(FileApi.auFileCDLF, data: data);
      return MdCheckDuLuongFile.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // file file lên
  Future<dynamic> auFileTL(File res, String chucNang, String fileName) async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        'files': await MultipartFile.fromFile(res.path, filename: fileName),
        'ma_ctcb': maCtcb,
        'chuc_nang': chucNang
      });
      final response = await dio.post(FileApi.auFileTL, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // uploadfile avatar
  Future<UploadFileModel> uploadFile(
      File res, String chucNang, String fileName) async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        'files': await MultipartFile.fromFile(res.path, filename: fileName),
        'ma_ctcb': maCtcb,
        'chuc_nang': chucNang
      });
      final response = await dio.post(
          '${FileApi.auFileUpload}/${MethodUntils.b64EncodeUnicode(_store.read(GetStorageKey.refreshToken))}',
          data: data);
      return UploadFileModel.fromJson(jsonDecode(response.data));
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
