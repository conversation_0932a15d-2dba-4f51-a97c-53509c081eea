import 'dart:ffi';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_chititet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_danhsach_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_qtxl_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/vbnb_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class VbnbProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  // lấy danh sach văn bản nội bộ
  Future<VbnbDanhSachNhanCv> auVbnbDanhSachDaNhan(
      String soHieu, String trichYeu, int page, int size, int xem) async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        'so_hieu': soHieu,
        'trich_yeu': trichYeu,
        'page': page,
        'size': size,
        'ma_ctcb': maCtcb,
        'xem': xem,
        'trang_thai': 3,
        'ma_cap_do': -1
      });
      final reponse = await dio.post(VbnbApi.auVbnbDsVbNbDn, data: data);
      return VbnbDanhSachNhanCv.fromJson(reponse.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh sách văn bản nội bộ đã phát hành
  Future<VbnbDanhSachNhanCv> auVbnbDaGuiDaPhatHanh(
    String soHieu,
    String trichYeu,
    int page,
    int size,
  ) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "so_hieu": soHieu,
        "trich_yeu": trichYeu,
        "page": page,
        "size": size,
        "ma_ctcb": maCtcbKc,
        "ma_linh_vuc": 0,
        "ma_loai_van_ban": 0,
        "ma_cap_do": -1
      });
      final response =
          await dio.post(VbnbApi.auVbnbDaGuiDaPhatHanh, data: data);
      return VbnbDanhSachNhanCv.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Hàm lấy chi tiết văn bản nội bộ đã nhận
  Future<VbnbChiTietVanBanNoi> auVbnbChiTietDaNhan(int maVbnbGui) async {
    try {
      var data = FormData.fromMap({"ma_vbnb_gui": maVbnbGui});
      final response = await dio.get(VbnbApi.auVbnbChiTietDaNhan, data: data);
      return VbnbChiTietVanBanNoi.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //Ham lấy chi tiết văn bản nội đã gui
  Future<VbnbChiTietVanBanNoi> auVbnbChiTietDaGui(int maVbnbGui) async {
    try {
      var data = FormData.fromMap({"ma_vbnb_gui": maVbnbGui});
      final response = await dio.get(VbnbApi.auVbnbChiTietDaGui, data: data);
      return VbnbChiTietVanBanNoi.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Ham lấy quá trình xử lý văn bản nôi bộ
  Future<VbnbQtxlVanBanNoiBo> auVbnbQtxlVanBanNoiBo(int maVbnbKc) async {
    try {
      var data = FormData.fromMap({"ma_vbnb": maVbnbKc});
      final response = await dio.get(VbnbApi.auVbnbCQtxlVbnb, data: data);
      return VbnbQtxlVanBanNoiBo.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auVbnbXemVanBanNoiBo(int maVbnbGui, int trangThaiXuLy) async {
    try {
      var data = FormData.fromMap(
          {"ma_vbnb_gui": maVbnbGui, "trang_thai_xu_ly": trangThaiXuLy});
      final response = await dio.post(VbnbApi.auVbnbCnvbnbvccs, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy đanh sách cây cán bộ văn bản nội bộ
  Future<TreeCbVaiTro> auDsCbNVnbn() async {
    try {
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      final response = await dio
          .get("${VbnbApi.auVbnbDsCbNvBNB}?ma_don_vi_quan_tri=$maDonViQuanTri");
      return TreeCbVaiTro.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auVbnbCvBnb(
      int maVbnbKc,
      int maCtcbGui,
      String chuoiMaCtcbNhan,
      String yKienXuLy,
      int trangThaiXuLy,
      int sms,
      int maVbnbGuiCha) async {
    try {
      var data = FormData.fromMap({
        "ma_vbnb_kc": maVbnbKc,
        "ma_ctcb_gui": maCtcbGui,
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
        "y_kien_xu_ly": yKienXuLy,
        "trang_thai_xu_ly": trangThaiXuLy,
        "sms": sms,
        "ma_vbnb_gui_cha": maVbnbGuiCha
      });
      final response = await dio.post(VbnbApi.auVbnbCVbnb, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
