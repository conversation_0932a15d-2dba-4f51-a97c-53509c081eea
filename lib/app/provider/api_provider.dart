import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/access_token.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/login_api.dart';
import 'package:vnpt_ioffice_camau/core/utils/modal_expiredTime.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ApiRoot {
  final Dio dio = Dio();

  ApiRoot() {
    dio.interceptors
        .add(InterceptorsWrapper(onRequest: (options, handler) async {
      final _prefs = await SharedPreferences.getInstance();
      final GetStorage storage = GetStorage();
      var domainAPi = _prefs.getString('apiDomain');
      if (!options.path.contains('https')) {
        options.path = domainAPi! + options.path;
      }

      String? accessToken = _prefs.getString("accessToken");
      String? refreshToken = _prefs.getString("refreshToken");
      int? expiredTime = _prefs.getInt("expiredTime");
      if (accessToken == null || expiredTime == null || refreshToken == null) {
        return handler.next(options);
      }
      String? dateTimeLogin = _prefs.getString("dateLogin");
      var timer = DateTime.now();
      if (dateTimeLogin != null) {
        timer = DateTime.parse(dateTimeLogin);
      }

      final now = DateTime.now();
      final later = timer.add(Duration(minutes: expiredTime));
      bool isExpired = now.isAfter(later);
      if (isExpired) {
        //String url = domainAPi! + LoginApi.auACCESSTOKEN;
        ModalExpiredTime.showModal();
        // final reponsitory = await dio.get('$url?refresh_token=$refreshToken');
        // var result = AccessToken.fromJson(reponsitory.data);
        // if (result.data.accessToken.isNotEmpty) {
        //   options.headers['Authorization'] =
        //       "Bearer ${result.data.accessToken}";

        //   await _prefs.setInt('expiredTime', result.data.expire);
        //   await _prefs.setString('accessToken', result.data.accessToken);
        // }
        return handler.next(options);
      } else {
        options.headers['Authorization'] = "Bearer $accessToken";
        return handler.next(options);
      }
    }, onError: (DioError error, handler) async {
      return handler.next(error);
    }));
  }
}
