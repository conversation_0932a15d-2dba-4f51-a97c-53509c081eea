import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/kyso/DsMauCks_model.dart';
import 'package:vnpt_ioffice_camau/app/model/kyso/FileSauKySo_model.dart';
import 'package:vnpt_ioffice_camau/app/model/kyso/kyso_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/kyso_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/values/app_config.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class KysoProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  Future<GetFilekySoSimApp> auKysoGFDK(int maVanBanDi, int maCtcbKs) async {
    try {
      var data = FormData.fromMap(
          {"ma_van_ban_di": maVanBanDi, "ma_ctcb_ky_so": maCtcbKs});
      final response = await dio.post(KySoApi.auvbdikSgFDK, data: data);
      return GetFilekySoSimApp.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Danh sách mẫu chữ ký
  Future<DsCks> auDsCKS() async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({"ma_ctcb": maCtcbKc});
      final response = await dio.get(KySoApi.auKySoDsCKS, data: data);
      return DsCks.fromJson(response.data);
      // ignore: deprecated_member_use
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // ký số Sim
  Future<dynamic> auKySoKSS(MKySoSim mKySoSim) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "filePath": mKySoSim.file,
        "imagePath": mKySoSim.fileHinhAnh,
        "textSearch": mKySoSim.tenChucDanh,
        "mssp": mKySoSim.msspProvider,
        "sdt": mKySoSim.soDienThoai,
        "pageSign": mKySoSim.trangKy ?? 0,
        "side": AppConfig.defaultUsMobilePkiSide,
        "width": mKySoSim.tenChucDanh != null
            ? AppConfig.defaultUsMobilePkiWidthRec
            : 0,
        "height": mKySoSim.tenChucDanh != null
            ? AppConfig.defaultUsMobilePkiHeightRec
            : 0,
        "llx": mKySoSim.tenChucDanh != null ? 0 : mKySoSim.llx!.round(),
        "lly": mKySoSim.tenChucDanh != null ? 0 : mKySoSim.lly!.round(),
        "urx": mKySoSim.tenChucDanh != null ? 0 : mKySoSim.urx!.round(),
        "ury": mKySoSim.tenChucDanh != null ? 0 : mKySoSim.ury!.round(),
        "ma_ctcb": maCtcbKc.toInt()
      });
      final response = await dio.post(KySoApi.auKySoKsPKI, data: data);
      return response.data;
      // ignore: deprecated_member_use
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auKySoVtCnFkPh(int maVanBanDi, String chuoiFileMoi) async {
    try {
      var data = FormData.fromMap(
          {"ma_van_ban_di": maVanBanDi, "chuoi_file_moi": chuoiFileMoi});
      var response = await dio.get(KySoApi.auKySoVtCnFkPh, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auKySoLFKSVbnb(
      int maVbnbKc, int maVbnbGuiKc, String fileDinhKem) async {
    try {
      var data = FormData.fromMap({
        'ma_vbnb_kc': maVbnbKc,
        'ma_vbnb_gui_kc': maVbnbGuiKc,
        'file_dinh_kem': fileDinhKem
      });
      final response = await dio.post(KySoApi.aukYSoLfksVbnb, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auKySoLfKs(
      int maVanBanDi, int maXuLyDi, String fileKySo) async {
    try {
      var data = FormData.fromMap({
        "ma_ctcb": _store.read(GetStorageKey.maCtcbKc),
        "ma_van_ban_di": maVanBanDi,
        "ma_xu_ly_di": maXuLyDi,
        "file_ky_so": fileKySo,
        "loai": "THEM"
      });
      final response = await dio.post(KySoApi.auKySoLfKs, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auKySoUfSkk(
      int maVanBanDi, int maCtcbKySo, String fileKySinhRa) async {
    try {
      var data = FormData.fromMap({
        "ma_van_ban_di": maVanBanDi,
        "ma_ctcb_ky_so": maCtcbKySo,
        "file_ky_sinh_ra": fileKySinhRa
      });
      final response = await dio.post(KySoApi.auKySoUfSKK, data: data);
      return response.data;
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // ky so smartCa
  Future<dynamic> auKySoGBFP(String path) async {
    try {
      var data = FormData.fromMap({"path": path});
      final response = await dio.post(KySoApi.auKySoGbFp, data: data);
      return response.data;
    } on DioError catch (error) {
      final erorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(erorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auKySoSCSHios(mKySoSmartCA kysmartCa, String key) async {
    try {
      var data = FormData.fromMap({
        "key": key,
        "comments": "W10=",
        "fontColor": kysmartCa.FontColor,
        "fontName": kysmartCa.FontName,
        "fontSize": kysmartCa.FontSize,
        "fontStyle": kysmartCa.FontStyle,
        "signatures": kysmartCa.Signatures,
        "visibleType": kysmartCa.VisibleType,
        "ma_ctcb": kysmartCa.maCtCb,
        "path": kysmartCa.filePath,
        "accessToken": kysmartCa.accessToken,
        "fileBase64": kysmartCa.DataBase64,
        "imgBase64": kysmartCa.Image,
      });
      final response = await dio.post(KySoApi.auKySoSCSHios, data: data);
      return response.data;
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<MFileSauCks> auKySoSCGSF(String key) async {
    try {
      var data = FormData.fromMap(
          {"key": key, "ma_ctcb": _store.read(GetStorageKey.maCtcbKc)});
      final response = await dio.post(KySoApi.auKySoSCGSF, data: data);
      return MFileSauCks.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<AccessTokenSmartCa> auKySoSmartCaGATD() async {
    try {
      var data =
          FormData.fromMap({"ma_ctcb": _store.read(GetStorageKey.maCtcbKc)});
      final response = await dio.post(KySoApi.auKySoSmartCaGATD, data: data);
      return AccessTokenSmartCa.fromJson(response.data);
    } on DioError catch (error) {
      final errorMessage = dioError.DioException.fromDioError(error).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
