import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/mode_lct_xct_ld.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_coquan.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_cot_ld.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_detail_dv.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_don_vi.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_dscb.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/lct_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;

class LctProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  Future<ModelLCT> LayDachDonVi() async {
    try {
      final response = await dio.get(
          '${LCTApi.lctDanhSachDv}?ma_ctcb=${_store.read(GetStorageKey.maCtcbKc)}');
      return ModelLCT.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ModelLctDsCb> getLctDsCb() async {
    try {
      final response = await dio.get(
          '${LCTApi.lctDanhSachCb}?ma_ctcb=${_store.read(GetStorageKey.maCtcbKc)}');
      return ModelLctDsCb.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<lctDsLichCongTacDonVi> getDsLctDonVi(int maDonVi, int nam) async {
    try {
      final response =
          await dio.get('${LCTApi.lctDslctdonvi}?ma_don_vi=$maDonVi&nam=$nam');
      return lctDsLichCongTacDonVi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ModelDetailsLctDonvi> getDetailsLctDonVi(int maLctKc) async {
    try {
      final response = await dio
          .get('${LCTApi.lctDetaillctDonVi}?ma_lich_cong_tac=$maLctKc');
      return ModelDetailsLctDonvi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<lctDsLichCongTacDonVi> getDetailsLctCaNhan(
      int maCtcbKc, int nam) async {
    try {
      final response = await dio
          .get('${LCTApi.lctChiTietCaNhan}?ma_ctcb=$maCtcbKc&nam=$nam');
      return lctDsLichCongTacDonVi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ModeldsCoQuan> getDsCoQuanLct() async {
    try {
      var maDonvi = _store.read(GetStorageKey.maDonViQuanTri);
      final response =
          await dio.get('${LCTApi.lctDsCoQuan}?ma_don_vi=$maDonvi');
      return ModeldsCoQuan.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<lctDsLichCongTacDonVi> getDsLctDonViQuanTri(
      int maDonViQuanTri, int nam) async {
    try {
      final response = await dio.get(
          '${LCTApi.lctDsDonViQuanTri}?ma_don_vi_quan_tri=$maDonViQuanTri&nam=$nam');
      return lctDsLichCongTacDonVi.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<LctLanhDaoByCot> getLctLanhDaoByCot(int maDonViQuanTri) async {
    try {
      final response =
          await dio.get('${LCTApi.lctLanhDaoByCot}?ma_dvqt=$maDonViQuanTri');
      return LctLanhDaoByCot.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<LctXemLichCtLanhDao> lctXemLichCongTacLd(
      int nam, int tuan, int maDonViQuanTri) async {
    try {
      final response = await dio.get(
          '${LCTApi.lctXemLichCongTacLd}?ma_dvqt=$maDonViQuanTri&nam=$nam&tuan=$tuan');
      return LctXemLichCtLanhDao.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
