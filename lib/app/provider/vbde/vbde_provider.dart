import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_butpheld_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chitietvt_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_choduyet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chuyenldk_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_dschualuuvt_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_themvbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xulycv_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/file_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/vbde_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_default.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class VbdeProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  // Lấy danh sách văn bản đến chờ duyệt lãnh đạo
  Future<DsLanhDaoDuyet> getDsChoDuyet(int page, int size,
      [String? trichYeu]) async {
    DateTime now = DateTime.now();
    String denNgay = DateFormat('dd/MM/yyyy').format(now);
    String tuNgay = "";
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));
    int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var data = FormData.fromMap({
        "trich_yeu": trichYeu,
        "den_ngay": denNgay,
        "ma_cap_do_khan": 0,
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "page": page,
        "size": size,
        "trang_thai_phan_hoi": 0,
        "tu_ngay": tuNgay
      });
      final reponse = await dio.post(VbdeApi.auChoDuyet, data: data);
      return DsLanhDaoDuyet.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy chi tiết văn bản đến
  Future<ChiTietVanBanDen> getChiTietVanBanDen(
      int maXuLyDen, int maVanBanDen, int maCtcbKc) async {
    try {
      var data = FormData.fromMap({
        "ma_xu_ly_den": maXuLyDen,
        "ma_van_ban_den": maVanBanDen,
        "ma_ctcb": maCtcbKc
      });
      final reponse = await dio.post(VbdeApi.auDetailVbde, data: data);
      return ChiTietVanBanDen.fromJson(reponse.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy bút phê văn bản đến lãnh đạo
  Future<List<DsButPheLanhDao>> getButPheLanhDao(
      int maVanBanDen, int maXuLyDen) async {
    try {
      final reponse = await dio.get(
          '${VbdeApi.auVbdeButPheLD}?ma_xu_ly_den=$maXuLyDen&ma_van_ban_den=$maVanBanDen');
      List<DsButPheLanhDao> listButPhe = [];
      for (var item in reponse.data) {
        var butPhe = DsButPheLanhDao.fromJson(item);
        listButPhe.add(butPhe);
      }
      return listButPhe;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // hoan tất vbde lãnh đạo
  Future<ResponeHtVbde> ldHoanTatVbde(
      int maCtcbDuyet, int maVanBanDen, int maXuLyDen, String? yKien) async {
    try {
      var data = FormData.fromMap({
        "ma_xu_ly_den_kc": maXuLyDen,
        "ma_van_ban_den": maVanBanDen,
        "ma_ctcb_duyet": maCtcbDuyet,
        "y_kien_xu_ly_hoan_tat": yKien
      });
      final reponse = await dio.post(VbdeApi.auVbdeLDHTVBD, data: data);
      return ResponeHtVbde.fromJson(reponse.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Lấy cây đơn vị
  Future<TreeCbVaiTro> getTreeCbTheoDv(int maDonVi) async {
    try {
      final response =
          await dio.get("${VbdeApi.auTreeCbTheoDv}?ma_don_vi=$maDonVi");
      return TreeCbVaiTro.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy nhóm đơn vị nhận
  Future<TreeCbVaiTro> getNhomCanBoNhan(int maCanBo) async {
    try {
      final response =
          await dio.get("${VbdeApi.auVbdeDsncbnvbdldd}?ma_can_bo=$maCanBo");

      return TreeCbVaiTro.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy cây đơn vị trực tiếp
  Future<TreeCbVaiTro> getDonViCDTT(int maDonViQuanTri) async {
    try {
      var data = FormData.fromMap({"ma_don_vi": maDonViQuanTri});
      final response = await dio.get(VbdeApi.auVbdeDsCbTdVtts, data: data);
      return TreeCbVaiTro.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Lấy danh sách lãnh đạo khác
  Future<ChuyenLdKhac> getDanhSachLanhDaoKhac(int maDonVi, int maCtcb) async {
    try {
      final response = await dio.get(
          "${VbdeApi.auVbdeDanhSachLDK}?ma_don_vi=$maDonVi&ma_ctcb=$maCtcb");
      return ChuyenLdKhac.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //Chuyển lãnh đạo khác
  Future<ResponseCommon> chuyenLdkVbde(
      int maXulyDenCha,
      int maVanBanDen,
      int maCtcbGui,
      int maCtcbNhan,
      String noiDungNhan,
      int sms,
      int email) async {
    try {
      var data = FormData.fromMap({
        "ma_xu_ly_den_cha": maXulyDenCha,
        "ma_van_ban_den": maVanBanDen,
        "ma_ctcb_gui": maCtcbGui,
        "ma_ctcb_nhan": maCtcbNhan,
        "noi_dung_chuyen": noiDungNhan,
        "sms": sms,
        "email": email
      });
      final response = await dio.post(VbdeApi.auVbdeChuyenLDK, data: data);

      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // gửi tin nhắn
  Future<bool> SendSMS(
      String noiDung, String chuoiDiDong, int maDonViQuanTri) async {
    try {
      String noiDungGui = "";
      if (_store.read(GetStorageKey.tsTnMauGuiSms) == "0") {
        noiDungGui =
            "<${_store.read(GetStorageKey.tsTienToTinNhan)}> Người gửi: <${_store.read(GetStorageKey.hoVaTen)}(${_store.read(GetStorageKey.diDongCanBo)})> $noiDung";
      } else {
        if (_store.read(GetStorageKey.tsChuVuCbTinNhan) != "0") {
          noiDungGui =
              "[${_store.read(GetStorageKey.hoVaTen)} - ${_store.read(GetStorageKey.chucVu)} - ${_store.read(GetStorageKey.tenDonViQuanTri)}] $noiDung";
        } else {
          noiDungGui =
              "<${_store.read(GetStorageKey.tsTienToTinNhan)}> $noiDung";
        }
      }
      // xoá dấu tin nhắn
      if (_store.read(GetStorageKey.tslocDauTinNhan) != 0) {
        noiDungGui = MethodUntils.xoaDau(noiDungGui);
      }
      var data = FormData.fromMap({
        "madonviquantri": maDonViQuanTri,
        "noidung": noiDungGui,
        "somay": chuoiDiDong
      });
      final response = await dio.post(ApiDefault.auSmsGTN, data: data);

      return true;
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lãnh đạo duyệt văn bản đến

  Future<ResponseLdXuLy> ldDuyetVbde(
      int maXuLyDenKc,
      int maVanBanDen,
      int lanhDaoChuyenTiep,
      int maCtcbDuyet,
      int maCtcbGui,
      String noiDungChuyen,
      String hanXuLyChung,
      List<Map<String, dynamic>> chuoiThongTinGuiSMS) async {
    try {
      var data = FormData.fromMap({
        "chon_truong_don_vi": false,
        "chuoi_thong_tin_gui_sms": json.encode(chuoiThongTinGuiSMS),
        "file_dinh_kem": "",
        "han_xu_ly_chung": hanXuLyChung == 'dd/mm/yyyy' ? "" : hanXuLyChung,
        "lanh_dao_chuyen_tiep": lanhDaoChuyenTiep,
        "ma_ctcb_gui": maCtcbGui,
        "ma_ctcb_duyet": maCtcbDuyet,
        "ma_van_ban_den": maVanBanDen,
        "ma_xu_ly_den_kc": maXuLyDenKc,
        "noi_dung_chuyen": noiDungChuyen,
        "yeu_cau": 1
      });
      final response = await dio.post(VbdeApi.auVbdeLddvbd, data: data);
      return ResponseLdXuLy.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //Lãnh đạo đợi phúc đáp

  Future<ResponseCommon> ldDoiPhucDap(int maVanBanDen) async {
    try {
      final response =
          await dio.get("${VbdeApi.auVbdeLddpd}?ma_van_ban_den=$maVanBanDen");
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách lãnh đạo đã chuyển thực hiện
  Future<DsLanhDaoDuyet> getDsDaChuyen(int page, int size,
      [String? trichYeu]) async {
    DateTime now = DateTime.now();
    String denNgay = DateFormat('dd/MM/yyyy').format(now);
    String tuNgay = "";
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));
    int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var data = FormData.fromMap({
        "trich_yeu": trichYeu,
        "den_ngay": denNgay,
        "ma_cap_do_khan": 0,
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "page": page,
        "size": size,
        "trang_thai_phan_hoi": 0,
        "tu_ngay": tuNgay
      });
      final reponse = await dio.post(VbdeApi.auDaDuyet, data: data);
      return DsLanhDaoDuyet.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách lãnh đạo đã uỷ quyền duyệt
  Future<DsLanhDaoDuyet> getDsLdUyQuyenDuyet(int page, int size,
      [String? trichYeu]) async {
    DateTime now = DateTime.now();
    String denNgay = DateFormat('dd/MM/yyyy').format(now);
    String tuNgay = "";
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));
    int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var data = FormData.fromMap({
        "trich_yeu": trichYeu,
        "den_ngay": denNgay,
        "ma_cap_do_khan": 0,
        "ma_ctcb_duyet": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "nam": 0,
        "page": page,
        "size": size,
        "trang_thai_phan_hoi": 0,
        "tu_ngay": tuNgay
      });
      final reponse = await dio.post(VbdeApi.auUyQuyen, data: data);
      return DsLanhDaoDuyet.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Chuyên viên
  /// danh sách chuyên viên theo trạng thái
  Future<DsChuyenVienXuLy> getDsCvTheoTrangThai(
      int page, int size, int trangThaiXuLy, String? keySearch,
      [int? maYeuCau]) async {
    DateTime now = DateTime.now();
    String denNgay = DateFormat('dd/MM/yyyy').format(now);
    String tuNgay = "";
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCanBo = _store.read(GetStorageKey.maCanBo);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));
    int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var data = FormData.fromMap({
        "co_tep_tin": -1,
        "ma_can_bo": maCanBo,
        "ma_ctcb_nhan": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_loai_ttdh": 0,
        "ma_yeu_cau": maYeuCau,
        "trich_yeu": keySearch,
        "nam": 0,
        "nhan_den_ngay": denNgay,
        "nhan_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trang_thai_ttdh_gui": -1,
        "trang_thai_xu_ly": trangThaiXuLy
      });
      final reponse = await dio.post(VbdeApi.auVbdeTttccv, data: data);
      return DsChuyenVienXuLy.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Xem  văn bản của chuyên viên
  Future<ResponseCommon> cvDaXemVBD(String chuoiMaXuLyDen) async {
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    try {
      var data = FormData.fromMap(
          {"chuoi_ma_xu_ly_den": chuoiMaXuLyDen, "ma_ctcb": maCtcbKc});
      final reponse = await dio.post(VbdeApi.auVbdeCvXbD, data: data);
      return ResponseCommon.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //Hoàn thành văn bản của chuyên viên
  Future<ResponseCommon> cvHoanThanhVanBan(String chuoiMaXuLyDen) async {
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    try {
      var data = FormData.fromMap({
        "ma_ctcb": maCtcbKc,
        "chuoi_ma_xu_ly_den": int.parse(chuoiMaXuLyDen)
      });
      final reponse = await dio.post(VbdeApi.auVbdeCvXlNvBd, data: data);
      return ResponseCommon.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Chuyên viên xử lý văn bản đên
  Future<ResponseCommon> cvDangXuLyVbd(
      int maXuLyDen, String? noiDungXuly, int trangThaiXuLy) async {
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    try {
      var data = FormData.fromMap({
        "ma_ctcb": maCtcbKc,
        "ma_xu_ly_den": maXuLyDen,
        "noi_dung_xu_ly": noiDungXuly,
        "trang_thai_xu_ly": trangThaiXuLy
      });
      final reponse = await dio.post(VbdeApi.auVbdeCvXlVbd, data: data);
      return ResponseCommon.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // cây cán bộ chuyên viên
  Future<TreeCbVaiTro> getDsCbChuyenVbde() async {
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maDonVi = _store.read(GetStorageKey.maDonVi);
    try {
      final reponse = await dio.get(
          "${VbdeApi.auVbdeDsCbChuyenVbde}?ma_don_vi=$maDonVi&ma_ctcb=$maCtcbKc");
      return TreeCbVaiTro.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //
  Future<ResponseCommon> auVbdeCvCTH(
      int maVanBanDen,
      int maXuLyDenCha,
      int maCtCbGui,
      List<Map<String, dynamic>> chuoiMaCanBoNhan,
      String hanXuLy,
      int maYeuCau,
      int sms,
      String noiDungYeuCau) async {
    try {
      var data = FormData.fromMap({
        "ma_xu_ly_den_out": 0,
        "ma_van_ban_den": maVanBanDen,
        "ma_xu_ly_den_cha": maXuLyDenCha,
        "ma_ctcb_gui": maCtCbGui,
        "chuoi_ma_ctcb_nhan": json.encode(chuoiMaCanBoNhan),
        "han_xu_ly": hanXuLy,
        "ma_yeu_cau": maYeuCau,
        "sms": sms,
        "noi_dung_yeu_cau": noiDungYeuCau,
      });
      final response = await dio.post(VbdeApi.auVbdeCvTh, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdeCvCnYKienXL(int maXuLyDen, yKienXuLy) async {
    try {
      var data = FormData.fromMap(
          {"ma_xu_ly_den": maXuLyDen, "y_kien_xu_ly": yKienXuLy});
      final response = await dio.post(VbdeApi.auVbdeCvCnYkXl, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách văn bản đến gọp tab
  Future<VbDenDienTuChuaLuu> auVbdeDienTuChuaLuu(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": "01/01/$nam",
        "den_ngay": "31/12/$nam",
        "ngay_duyet_tu_ngay": "01/01/$nam",
        "ngay_duyet_den_ngay": "31/12/$nam",
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(
          vbdeGopVbDienTu == 1
              ? VbdeApi.auVbdeDsDtLuu
              : VbdeApi.auVbdeDsVbdtCvt,
          data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách đã chuyển của văn thư
  Future<VbDenDienTuChuaLuu> auVbdeDaChuyen(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsDaChuyenvt, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách đã huỷ của văn thư
  Future<VbDenDienTuChuaLuu> auVbdeDaHuyCuaVt(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsDaHuy, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách lưu tạm của văn thư
  Future<VbDenDienTuChuaLuu> auVbdeDsLuuTamVt(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsLuuTam, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách vb lãnh đạo trả lại văn thư
  Future<VbDenDienTuChuaLuu> auVbdeDsLdTraLai(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsLdTraLai, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách vb chờ duyệt
  Future<VbDenDienTuChuaLuu> auVbdeLdChoDuyetVt(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsLdChoDuyetVt, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách vb chờ chuyển của văn thư
  Future<VbDenDienTuChuaLuu> auVbdeDsChoChuyeVt(int page, int size,
      [String? trichYeu]) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      int vbdeGopVbDienTu =
          int.parse(_store.read(GetStorageKey.vbdeGopChungVbDienTu));
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      DateTime now = DateTime.now();
      String denNgay = DateFormat('dd/MM/yyyy').format(now);
      String tuNgay = "";
      String htThoiGianNhacViec =
          _store.read(GetStorageKey.tsHtChonTgianNhacViec);
      int month = int.parse(htThoiGianNhacViec.substring(0, 1));
      int nam = now.year;
      if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
        tuNgay = "01/01/$nam";
      } else {
        DateTime endDate = MethodUntils.subtractMonths(now, month);
        tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
      }
      var data = FormData.fromMap({
        "page": page,
        "size": size,
        "nam": 0,
        "tu_ngay": tuNgay,
        "den_ngay": denNgay,
        "ngay_duyet_tu_ngay": tuNgay,
        "ngay_duyet_den_ngay": denNgay,
        "ma_ctcb": maCtcbKc,
        "ma_ctcb_van_thu": maCtcbKc,
        "ma_don_vi_nhan": maDonViQuanTri,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": 0,
        "trich_yeu": trichYeu
      });
      final response = await dio.post(VbdeApi.auVbdeDsChoChuyenVt, data: data);
      return VbDenDienTuChuaLuu.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdeUploadTuLienThong(String chucNang, int donVi,
      int maCtcb, String partition, String tenFile) async {
    try {
      var data = FormData.fromMap({
        "chuc_nang": chucNang,
        "don_vi": donVi,
        "ma_ctcb": maCtcb,
        "partition": partition,
        "ten_file": tenFile
      });
      final response =
          await dio.post(VbdeApi.auVbdeUploadTuLienThong, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ResponseCommon> auVbdeCNCVBDDTLT(
      int maVanBan, String chuoiFileMoi) async {
    try {
      final response = await dio.get(
          "${VbdeApi.auVbdeCNCVBDDTLT}?ma_van_ban=$maVanBan&chuoi_file_moi=$chuoiFileMoi");
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách danh mucj sổ văn bản đến
  Future<DanhSachDMSoVbde> auVbdeDsDmSoVBDE() async {
    try {
      DateTime now = DateTime.now();
      int nam = now.year;
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      final response = await dio.get(
          "${VbdeApi.auVbdeDsDmSoVBD}?ma_don_vi_quan_tri=$maDonViQuanTri&nam=$nam");
      return DanhSachDMSoVbde.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sách lấy số văn bản đến
  Future<LaySoDenBySoVb> auVbdeLaySoDe(int maSoVbDen) async {
    try {
      DateTime now = DateTime.now();
      int nam = now.year;
      final response = await dio
          .get("${VbdeApi.auVbdeLsDTSoVBD}?ma_so_vb_den=$maSoVbDen&nam=$nam");
      return LaySoDenBySoVb.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lâý danh mục loại văn bản
  Future<DsDmLoaiVb> auVbdeLoaiVanBan(int trangThai) async {
    try {
      final response =
          await dio.get("${VbdeApi.auVbdeDsLoaiVanBan}?trang_thai=$trangThai");
      return DsDmLoaiVb.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh mục cấp độ khẩn
  Future<DsDmCapDoKhan> auVbdeCapDoKhan() async {
    try {
      final response = await dio.get(VbdeApi.auVbdeDsCapDoKhan);
      return DsDmCapDoKhan.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh mục cấp độ mật
  Future<DsDmCapDoMat> auVbdeCapDoMat() async {
    try {
      final response = await dio.get(VbdeApi.auVbdeDsCapDoMat);
      return DsDmCapDoMat.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // lấy danh sách lãnh đạo duyệt
  Future<DsLanhDaoDuyetVbde> auVbdeLanhDaoDuyetVbde() async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      int maDonVi = _store.read(GetStorageKey.maDonVi);
      final response = await dio.get(
          "${VbdeApi.auVbdeDsLanhDaoDuyetVbde}?ma_ctcb=$maCtcb&ma_don_vi=$maDonVi");
      return DsLanhDaoDuyetVbde.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<GetInfoEdxml> auVbdeGetInfoEdxml(String fileEdxml) async {
    try {
      // String refreshToken =base64.encode(_store.read(GetStorageKey.refreshToken));
      var fresh = _store.read(GetStorageKey.refreshToken);
      String refreshToken = base64.encode(utf8.encode(fresh));
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      String maDinhDanh = _store.read(GetStorageKey.maDinhDanhQuanTri);
      String userEdoc = _store.read(GetStorageKey.userEdoc);
      String passEdoc = _store.read(GetStorageKey.passEdoc);
      String pubKeyEdoc = _store.read(GetStorageKey.pubKeyEdoc);
      String priKeyEdoc = _store.read(GetStorageKey.priKeyEdoc);
      var data = FormData.fromMap({
        "filexml": fileEdxml,
        "ma_ctcb": maCtcb,
        "ma_dinh_danh": maDinhDanh,
        "user_edoc": userEdoc,
        "pass_edoc": passEdoc,
        "pub_key_edoc": pubKeyEdoc,
        "pri_key_edoc": priKeyEdoc
      });
      final response = await dio
          .post("${VbdeApi.auVbdeGetInforEdxml}/$refreshToken", data: data);
      return GetInfoEdxml.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<CopyEdocDocument> copyDocumentEdoc(String? strFile) async {
    try {
      String refreshToken = _store.read(GetStorageKey.refreshToken);
      String enCodeRefreshToken = base64.encode(utf8.encode(refreshToken));
      var data = FormData.fromMap(
          {"chuoi_file_path": strFile, "chuc_nang": "vbde_edoc"});
      final response = await dio
          .post("${FileApi.copyDocumentEdoc}$enCodeRefreshToken", data: data);
      return CopyEdocDocument.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<DmDsLinhVuc> auVbdeDsLvVb() async {
    try {
      final response = await dio.get(VbdeApi.auVbdeDsLvVb);
      return DmDsLinhVuc.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // Thêm mới văn bản đến
  Future<ThemMoiVbde> auVbdeThemMoiVbde(DsVanDenDienTu item) async {
    try {
      int maDonVi = _store.read(GetStorageKey.maDonVi);
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "ma_van_ban_den_kc": item.maVanBanDenKc!.toInt(),
        "ma_van_ban_kc": item.maVanBanKc,
        "ma_xu_ly_den": item.maXuLyDen!.toInt(),
        "so_den": item.soDen,
        "trich_yeu": item.trichYeu,
        "so_ky_hieu": item.soKyHieu,
        "ngay_nhan": item.ngayNhan,
        "ngay_xem": item.ngayXem,
        "file_van_ban_bs": item.fileVanBanBs,
        "ten_co_quan_ban_hanh": item.tenCoQuanBanHanh,
        "ten_don_vi_gui": item.tenDonViGui,
        "file_van_ban": item.fileVanBan,
        "ma_linh_vuc_van_ban": item.maLinhVucVanBan!.toInt(),
        "ma_loai_van_ban": item.maLoaiVanBan!.toInt(),
        "ma_cap_do_khan": item.maCapDoKhan!.toInt(),
        "ma_cap_do_mat": item.maCapDoMat!.toInt(),
        "ngay_den": item.ngayDen,
        "han_xu_ly_chung": item.hanXuLy,
        "ngay_ban_hanh": item.ngayBanHanh,
        "so_ioffice": item.soIoffice,
        "gui_kem_vb_giay": item.guiKemVbGiay!.toInt(),
        "so_ban_phat_hanh": item.soBanPhatHanh,
        "so_trang_vb": item.soTrangVb,
        "nguoi_ky": item.nguoiKy,
        "noi_luu_ban_chinh": item.noiLuuBanChinh,
        "ma_ctcb_duyet": item.maCtcbDuyet!.toInt(),
        "hien_thi_vblq": item.hienThiVblq!.toInt(),
        "chuoi_vb_dien_tu": item.chuoiVbDienTu,
        "da_luu_file": item.daLuuFile!.toInt(),
        "r": item.r,
        "ma_don_vi": maDonVi,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_ctcb_tao": maCtcbKc,
        "ma_van_ban_den_inout": item.maVanBanDenKc!.toInt(),
        "ma_xu_ly_den_inout": item.maXuLyDen!.toInt(),
        "gui_kem_van_ban_giay": item.guiKemVbGiay,
        "ma_so_vb_den": item.maSoVbDen!.toInt(),
        "chuoi_ma_vb_lien_quan": "",
        "trang_thai_van_ban_den": item.trangThaiVanBanDen!.toInt(),
        "ngay_duyet": "",
        "ghi_chu": item.ghiChu,
        "sms": item.sms,
        "email": 0
      });
      final response = await dio.post(VbdeApi.auThemMoiVbde, data: data);
      return ThemMoiVbde.fromJson(response.data);
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // câp nhật
  Future<ResponseCommon> auVbdeCnKTmVbd(int maVanBanDen) async {
    try {
      var data = FormData.fromMap({"ma_van_ban_den": maVanBanDen});
      final response = await dio.post(VbdeApi.auVbdeCnKtMvBd, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // cập nhật văn bản edoc
  Future<ResponseCommon> auVbdeUpadteStatusEdocTemp(
      int maVbdenEdocTemp, int status) async {
    try {
      var data = FormData.fromMap(
          {"ma_vbde_edoc_temp_kc": maVbdenEdocTemp, "trang_thai": status});
      final response =
          await dio.post(VbdeApi.auVbdeUpdateStatusEdocTemp, data: data);
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // văn thư huỷ văn bản điện tử
  Future<ResponseCommon> auVbdeVanThuHVBDDT(int maXuLyDen) async {
    int maCtcbkc = _store.read(GetStorageKey.maCtcbKc);
    try {
      final response = await dio.get(
          "${VbdeApi.auVbdeVanThuHVBDDT}?ma_xu_ly_den=$maXuLyDen&ma_ctcb=$maCtcbkc");
      return ResponseCommon.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ThemMoiVbde> auVbdeThemDeHuyVbEdoc(DsVanDenDienTu item) async {
    try {
      int maDonVi = _store.read(GetStorageKey.maDonVi);
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "ma_van_ban_den_kc": item.maVanBanDenKc!.toInt(),
        "ma_van_ban_kc": item.maVanBanKc,
        "ma_xu_ly_den": item.maXuLyDen!.toInt(),
        "so_den": item.soDen,
        "trich_yeu": item.trichYeu,
        "so_ky_hieu": item.soKyHieu,
        "ngay_nhan": item.ngayNhan,
        "ngay_xem": item.ngayXem,
        "file_van_ban_bs": item.fileVanBanBs,
        "ten_co_quan_ban_hanh": item.tenCoQuanBanHanh,
        "ten_don_vi_gui": item.tenDonViGui,
        "file_van_ban": item.fileVanBan,
        "ma_linh_vuc_van_ban": item.maLinhVucVanBan!.toInt(),
        "ma_loai_van_ban": item.maLoaiVanBan!.toInt(),
        "ma_cap_do_khan": item.maCapDoKhan!.toInt(),
        "ma_cap_do_mat": item.maCapDoMat!.toInt(),
        "ngay_den": item.ngayDen,
        "han_xu_ly_chung": item.hanXuLy,
        "ngay_ban_hanh": item.ngayBanHanh,
        "so_ioffice": item.soIoffice,
        "gui_kem_vb_giay": item.guiKemVbGiay!.toInt(),
        "so_ban_phat_hanh": item.soBanPhatHanh,
        "so_trang_vb": item.soTrangVb,
        "nguoi_ky": item.nguoiKy,
        "noi_luu_ban_chinh": item.noiLuuBanChinh,
        "ma_ctcb_duyet": item.maCtcbDuyet!.toInt(),
        "hien_thi_vblq": item.hienThiVblq!.toInt(),
        "chuoi_vb_dien_tu": item.chuoiVbDienTu,
        "da_luu_file": item.daLuuFile!.toInt(),
        "r": item.r,
        "ma_don_vi": maDonVi,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_ctcb_tao": maCtcbKc,
        "ma_van_ban_den_inout": item.maVanBanDenKc!.toInt(),
        "ma_xu_ly_den_inout": item.maXuLyDen!.toInt(),
        "gui_kem_van_ban_giay": item.guiKemVbGiay,
        "ma_so_vb_den": item.maSoVbDen!.toInt(),
        "chuoi_ma_vb_lien_quan": "",
        "trang_thai_van_ban_den": item.trangThaiVanBanDen!.toInt(),
        "ngay_duyet": "",
        "ghi_chu": item.ghiChu,
        "sms": item.sms,
        "email": 0
      });
      final response = await dio.post(VbdeApi.auVbdeVanThuHVBDDT, data: data);
      return ThemMoiVbde.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // cập nhật trạng thái gói tin edxml
  Future<dynamic> auVbdeUpdateStatusEdxml(String filexml) async {
    try {
      String maDinhDanh = _store.read(GetStorageKey.maDinhDanhQuanTri);
      String userEdoc = _store.read(GetStorageKey.userEdoc);
      String passEdoc = _store.read(GetStorageKey.passEdoc);
      String pubKeyEdoc = _store.read(GetStorageKey.pubKeyEdoc);
      String priKeyEdoc = _store.read(GetStorageKey.priKeyEdoc);
      var data = FormData.fromMap({
        "filexml": filexml,
        "trang_thai": 4,
        "ma_dinh_danh": maDinhDanh,
        "user_edoc": userEdoc,
        "pass_edoc": passEdoc,
        "pub_key_edoc": pubKeyEdoc,
        "pri_key_edoc": priKeyEdoc
      });
      final response =
          await dio.post(VbdeApi.auVbdeUpdateStatusEdxml, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auVbdeUpdateStatusTrucEdoc(String filexml) async {
    try {
      DateTime now = DateTime.now();
      String userEdoc = _store.read(GetStorageKey.userEdoc);
      String passEdoc = _store.read(GetStorageKey.passEdoc);
      String pubKeyEdoc = _store.read(GetStorageKey.pubKeyEdoc);
      String priKeyEdoc = _store.read(GetStorageKey.priKeyEdoc);
      var data = FormData.fromMap({
        "filexml": filexml,
        "trang_thai": 0,
        "DocumentStatusID": 2,
        "staff": _store.read(GetStorageKey.hoVaTen),
        "Position": _store.read(GetStorageKey.chucVu),
        "Description": "Văn thu huỷ văn bản",
        "ma_dinh_danh": _store.read(GetStorageKey.maDinhDanhQuanTri),
        "Timestamp": DateFormat('dd/MM/yyyy').format(now),
        "Department": _store.read(GetStorageKey.tenDonViQuanTri),
        "user_edoc": userEdoc,
        "pass_edoc": passEdoc,
        "pub_key_edoc": pubKeyEdoc,
        "pri_key_edoc": priKeyEdoc
      });
      final response =
          await dio.post(VbdeApi.auVbdeUpdateStatusTrucEdoc, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<VbdeChiTietVt> auVbdeVBDChiTiet(int maXuLyDen, int maVanBanDen) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "ma_xu_ly_den": maXuLyDen,
        "ma_van_ban_den": maVanBanDen,
        "ma_ctcb": maCtcbKc
      });
      final response = await dio.post(VbdeApi.auVbdeVBDChiTiet, data: data);
      return VbdeChiTietVt.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // danh sachs văn bản đến theo dõi
  Future<DsLanhDaoDuyet> getDsVbdeTheoDoi(int page, int size,
      [String? trichYeu]) async {
    DateTime now = DateTime.now();
    String denNgay = DateFormat('dd/MM/yyyy').format(now);
    String tuNgay = "";
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCanBo = _store.read(GetStorageKey.maCanBo);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));
    int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var data = FormData.fromMap({
        "trich_yeu": trichYeu,
        "co_tep_tin": -1,
        "ma_can_bo": maCanBo,
        "ma_ctcb_nhan": maCtcbKc,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_loai_ttdh": 0,
        "ma_yeu_cau": 4,
        "nam": 0,
        "nhan_den_ngay": denNgay,
        "nhan_tu_ngay": tuNgay,
        "page": page,
        "size": size,
        "trang_thai_ttdh_gui": -1,
        "trang_thai_xu_ly": 0
      });
      final reponse = await dio.post(VbdeApi.auVbdeTheoDoi, data: data);
      return DsLanhDaoDuyet.fromJson(reponse.data);
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // huỷ theo dỗi
  Future<dynamic> auVbdeHuyTheoDoi(int maXuLyDen) async {
    try {
      var data = FormData.fromMap({"ma_xu_ly_den": maXuLyDen});
      final response = await dio.post(VbdeApi.auVbdeHuyTheoDoi, data: data);
      return response.data;
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
