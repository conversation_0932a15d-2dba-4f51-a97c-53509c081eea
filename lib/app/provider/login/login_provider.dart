import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/src/form_data.dart' as _fromData;
import 'package:get_storage/get_storage.dart';
import 'package:intl/date_symbols.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/access_token.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/auth_model.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/dsdvchuquan_model.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/dsthamso_model.dart';
import 'package:vnpt_ioffice_camau/app/model/login/model_thamso_sso.dart';
import 'package:vnpt_ioffice_camau/app/model/login/thamso_mobile_model.dart';
import 'package:vnpt_ioffice_camau/app/model/user/ctcb_kiemnhiem.dart';
import 'package:vnpt_ioffice_camau/app/model/user/user_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/login_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class LoginProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();
  Future<AuthModel> getDanhSachCTCB(String? username, String? password,
      {String? accessTokenSSO, String? domainSSO}) async {
    final formData = _fromData.FormData.fromMap({
      'username': username,
      'password': password,
      'access_token_sso_ybi': accessTokenSSO,
      'domain_sso_ybi': domainSSO
    });
    try {
      final reponsitory = await dio.post(LoginApi.auDSCTCB, data: formData);

      if (AuthModel.fromJson(reponsitory.data).data.isEmpty) {
        return AuthModel.fromJson(reponsitory.data);
      } else {
        final infoCB = AuthModel.fromJson(reponsitory.data);
        return infoCB;
      }
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<AccessToken> getAccessToken(String refreshToken) async {
    try {
      final accessToken = await dio
          .get('${LoginApi.auACCESSTOKEN}?refresh_token=${refreshToken}');
      var reponsitory = AccessToken.fromJson(accessToken.data);
      return reponsitory;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<bool> getDsThamSo(String maCtcbkc) async {
    bool result = false;
    try {
      final reponsitory =
          await dio.get('${LoginApi.auDSThamSo}?ma_ctcb=${maCtcbkc}');
      var reps = DsThamSo.fromJson(reponsitory.data);
      if (reps.data.isNotEmpty) {
        var htChonTgianNhacViec = reps.data
            .firstWhere((item) => item.maThamSo == 'ht_chon_tgian_nhac_viec')
            .giaTriThamSo;
        _store.write(GetStorageKey.tsHtChonTgianNhacViec, htChonTgianNhacViec);
        var tnMauGuiSms = reps.data
            .firstWhere((item) => item.maThamSo == 'tn_mau_gui_sms')
            .giaTriThamSo;
        _store.write(GetStorageKey.tsTnMauGuiSms, tnMauGuiSms);
        var locDauTinNhan = reps.data
            .firstWhere((element) => element.maThamSo == 'loc_dau_tin_nhan')
            .giaTriThamSo;
        _store.write(GetStorageKey.tslocDauTinNhan, locDauTinNhan);
        var chuVuCbTinNhan = reps.data
            .firstWhere((element) => element.maThamSo == 'chu_vu_cb_tin_nhan')
            .giaTriThamSo;
        _store.write(GetStorageKey.tsChuVuCbTinNhan, chuVuCbTinNhan);
        var tienToTinNhan = reps.data
            .firstWhere((element) => element.maThamSo == 'tien_to_tin_nhan')
            .giaTriThamSo;
        _store.write(GetStorageKey.tsTienToTinNhan, tienToTinNhan);
        var vbdeGopChungVbDienTu = reps.data
            .firstWhere(
                (element) => element.maThamSo == 'vbde_gop_chung_vb_dien_tu')
            .giaTriThamSo;
        _store.write(GetStorageKey.vbdeGopChungVbDienTu, vbdeGopChungVbDienTu);
        var userEdoc = reps.data
            .firstWhere((element) => element.maThamSo == 'user_edoc')
            .giaTriThamSo;
        _store.write(GetStorageKey.userEdoc, userEdoc);
        var passEdoc = reps.data
            .firstWhere((element) => element.maThamSo == 'pass_edoc')
            .giaTriThamSo;
        _store.write(GetStorageKey.passEdoc, passEdoc);
        var pubKeyEdoc = reps.data
            .firstWhere((element) => element.maThamSo == 'pub_key_edoc')
            .giaTriThamSo;
        _store.write(GetStorageKey.pubKeyEdoc, pubKeyEdoc);
        var priKeyEdoc = reps.data
            .firstWhere((element) => element.maThamSo == 'pri_key_edoc')
            .giaTriThamSo;
        _store.write(GetStorageKey.priKeyEdoc, priKeyEdoc);
        var cksXoaFileSauKySo = reps.data
            .firstWhere(
                (element) => element.maThamSo == 'cks_xoa_file_sau_khi_ky_so',
                orElse: () => DetailThamSo(
                    maThamSo: 'cks_xoa_file_sau_khi_ky_so', giaTriThamSo: '0'))
            .giaTriThamSo;
        _store.write(GetStorageKey.cksXoaFileSauKhiKySo, cksXoaFileSauKySo);
        var cksSmartCaUrl = reps.data
            .firstWhere((element) => element.maThamSo == 'cks_smartca_url',
                orElse: () => DetailThamSo(
                    maThamSo: 'cks_smartca_url',
                    giaTriThamSo: 'https://gwsca.vnpt.vn'))
            .giaTriThamSo;
        _store.write(GetStorageKey.cksSmartCaUrl, cksSmartCaUrl);
        var cksSmartCaClientId = reps.data
            .firstWhere(
                (element) => element.maThamSo == "cks_smartca_client_id",
                orElse: () => DetailThamSo(
                    maThamSo: 'cks_smartca_client_id',
                    giaTriThamSo:
                        '42ee-637744055357737854.apps.smartcaapi.com'))
            .giaTriThamSo;
        _store.write(GetStorageKey.cksSmartCaClientId, cksSmartCaClientId);
        var cksSmartCaCallback = reps.data
            .firstWhere((element) => element.maThamSo == "cks_smartca_callback",
                orElse: () => DetailThamSo(
                    maThamSo: "cks_smartca_callback",
                    giaTriThamSo:
                        "${_store.read(GetStorageKey.domainApi)}/api/tien-ich/smartca-handle-access-token"))
            .giaTriThamSo;
        _store.write(GetStorageKey.cksSmartCaCallback, cksSmartCaCallback);

        var vbdeXuLySoNgayHxl = reps.data
            .firstWhere(
                (element) => element.maThamSo == "vbde_xuly_so_ngay_hxl",
                orElse: () => DetailThamSo(
                    maThamSo: 'vbde_xuly_so_ngay_hxl', giaTriThamSo: '0'))
            .giaTriThamSo;
        _store.write(GetStorageKey.vbdeXuLySoNgayHxl, vbdeXuLySoNgayHxl);
        var vbdeUserHienThiSoNgayHxl = reps.data
            .firstWhere(
                (element) =>
                    element.maThamSo == "vbde_user_hienthi_so_ngay_hxl",
                orElse: () => DetailThamSo(
                    maThamSo: 'vbde_user_hienthi_so_ngay_hxl',
                    giaTriThamSo: '0'))
            .giaTriThamSo;
        _store.write(
            GetStorageKey.vbdeUserHienThiSoNgayHxl, vbdeUserHienThiSoNgayHxl);
        var htMatKhauMacDinh = reps.data
            .firstWhere((element) => element.maThamSo == "ht_mat_khau_mac_dinh",
                orElse: () => DetailThamSo(
                    maThamSo: 'ht_mat_khau_mac_dinh', giaTriThamSo: '123456'))
            .giaTriThamSo;
        _store.write(GetStorageKey.htMatKhauMacDinh, htMatKhauMacDinh);
        return true;
      } else {
        return result;
      }
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //ghi log đăng nhập
  Future<dynamic> ghiLogDn(int maCtcbKc, String userName) async {
    try {
      final data = _fromData.FormData.fromMap({
        "ma_log_dang_nha": 0,
        "ma_ctcb": maCtcbKc,
        "dia_chi_dang_nhap": "",
        "trinh_duyet_web": "App Mobile",
        "he_dieu_hanh": Platform.operatingSystem.toString(),
        "thiet_bi": Platform.isIOS ? "IOS" : "Android",
        "user_name": userName,
      });
      var repons = await dio.post(LoginApi.auGhiLogDN, data: data);
      return repons;
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  //Kiem tra quyền cán bộ
  Future<KtQuyenModel> auKTQCVLDVTCCCB(int maCtcb) async {
    try {
      KtQuyenModel reponse;
      var respone = await dio.get('${LoginApi.auKTQCVLDVTCCB}?ma_ctcb=$maCtcb');
      return KtQuyenModel.fromJson(respone.data);
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<List<DonVi>> getDsDvCQ(int maCtcb) async {
    try {
      var respone = await dio.get('${LoginApi.auDSDVCQ}?ma_ctcb=$maCtcb');
      var listdv = DsDonViChuQuan.fromJson(respone.data).data!;

      List<DonVi> listDonVi = [];
      for (var item in listdv) {
        listDonVi.add(item);
      }
      return listDonVi;
    } on DioError catch (err) {
      final erroMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // get tham số mobile
  Future<ThamSoMobile> auGTSM() async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      final response = await dio.get('${LoginApi.auGTSM}?ma_ctcb_kc=$maCtcbKc');
      return ThamSoMobile.fromJson(response.data);
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  // FCM
  Future<dynamic> auFCM(String token) async {
    try {
      int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "ma_ctcb": maCtcbKc,
        "Token": token,
        "url_api": _store.read(GetStorageKey.domainApi).toString()
      });
      final response = await dio.post(LoginApi.auFCM, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auPushFCM(
      String chuoiMaCtcbNhan, String title, String body) async {
    try {
      var data = FormData.fromMap({
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
        "title": title,
        "body": body
      });
      final response = await dio.post(LoginApi.auPushFCM, data: data);
      return response.data;
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> auDelFCM() async {
    try {
      var data = FormData.fromMap({
        "ma_ctcb": _store.read(GetStorageKey.maCtcbKc),
        "Token": _store.read(GetStorageKey.accessTokenFCM)
      });
      final response = await dio.post(LoginApi.auDelFCM, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<HtDsCtcbKiemNhiem> auDsCtcbKiemNhiem() async {
    try {
      var data = FormData.fromMap(
          {"refresh_token": _store.read(GetStorageKey.refreshToken)});
      final response = await dio.get(LoginApi.auDsCtCbKiemNhiem, data: data);
      return HtDsCtcbKiemNhiem.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<ModelThamSoSSO> getThamSoSSO() async {
    try {
      final reponsitory = await dio.get(LoginApi.auTSCSY);
      return ModelThamSoSSO.fromJson(reponsitory.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> logOutSSO(String domainSso) async {
    Dio dio = Dio();

    try {
      final responsitory = await dio.get('$domainSso/oidc/logout');
      return responsitory;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<AuthModel> getDanhSachCTCBSSO(
    String? username,
    String? key,
    String? password,
  ) async {
    Dio dio2 = Dio();
    final formData = _fromData.FormData.fromMap({
      'username': username,
      'key': key,
      'password': password,
    });
    try {
      final reponsitory = await dio2.post(
        'https://camau-api.vnptioffice.vn${LoginApi.auDSCTCBMOBILE}',
        data: formData,
        options: Options(headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        }),
      );

      if (AuthModel.fromJson(reponsitory.data).data.isEmpty) {
        return AuthModel.fromJson(reponsitory.data);
      } else {
        final infoCB = AuthModel.fromJson(reponsitory.data);
        return infoCB;
      }
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage.toString());
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
