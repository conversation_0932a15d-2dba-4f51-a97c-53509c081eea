import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/mauchuky/mau_chu_ky_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/mauchuky_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class MauChuKyProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();

  Future<MauChuKyModel> getDanhSachMauCks() async {
    try {
      int mactcb = _store.read(GetStorageKey.maCtcbKc);
      final response =
          await dio.get('${MauChuKyApi.dsMauChuKy}?ma_ctcb=$mactcb');
      return MauChuKyModel.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> xoaMauChuKy(int idKySo) async {
    try {
      var maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "id_ky_so": idKySo,
        "ma_ctcb_tao": maCtcb,
        "ten_ky_so": "",
        "trang_thai": "-1"
      });
      final response = await dio.delete(MauChuKyApi.xoaMayChuKy, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> capNhatCks(int idKySo) async {
    try {
      var maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap(
          {"id_ky_so": idKySo, "ma_ctcb_tao": maCtcb, "trang_thai": 1});
      final response = await dio.post(MauChuKyApi.capNhatCks, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> taoMauCksMoi(String pathFile, String tenKySo) async {
    try {
      var maCtcb = _store.read(GetStorageKey.maCtcbKc);
      var data = FormData.fromMap({
        "link_cks": pathFile,
        "ma_ctcb_tao": maCtcb,
        "ten_ky_so": tenKySo,
        "trang_thai": 0
      });
      final response = await dio.post(MauChuKyApi.taoMauChuky, data: data);
      return response.data;
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
