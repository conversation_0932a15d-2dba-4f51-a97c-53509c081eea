// To parse this JSON data, do
//
//     final mdThongTinDieuHanh = mdThongTinDieuHanhFromJson(jsonString);

import 'dart:convert';

import 'package:intl/intl.dart';

MdThongTinDieuHanhNhan mdThongTinDieuHanhFromJson(String str) =>
    MdThongTinDieuHanhNhan.fromJson(json.decode(str));

String mdThongTinDieuHanhToJson(MdThongTinDieuHanhNhan data) =>
    json.encode(data.toJson());

class MdThongTinDieuHanhNhan {
  dynamic? totalPage;
  dynamic? totalRow;
  bool? success;
  String? message;
  String? storeName;
  dynamic storeType;
  List<ThongTinDieuHanhNhan> data;

  MdThongTinDieuHanhNhan({
    this.totalPage,
    this.totalRow,
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    required this.data,
  });

  factory MdThongTinDieuHanhNhan.from<PERSON>son(Map<String, dynamic> json) =>
      MdThongTinDieuHanh<PERSON>han(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: List<ThongTinDieuHanhNhan>.from(
            json["data"].map((x) => ThongTinDieuHanhNhan.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class ThongTinDieuHanhNhan {
  double? maTtdhKc;
  double? maLoaiTtdh;
  double? maCtcbTao;
  String? tieuDe;
  String? noiDung;
  dynamic ngayTao;
  dynamic ngayGui;
  double? sttTtdh;
  String? srcFileTtdh;
  double? trangThaiTtdh;
  dynamic traLoiChoTtdh;
  dynamic chuyenTiepTuTtdh;
  double? maTtdhGoc;
  String? srcVanBanLienQuan;
  dynamic chuoiMaVblq;
  double? maTtdhCu;
  double? maLoaiTtdhCu;
  double? maCtcbTaoCu;
  double? maTtdhGocCu;
  dynamic chuoiMaVblqCu;
  dynamic ngayXem;
  dynamic dem;
  dynamic trangThaiBold;
  double? maCtcbNhan;
  double? trangThaiTtdhGui;
  double? maTtdhGuiKc;
  String? tenNguoiGui;
  String? tenDonViGui;
  dynamic ngayNhan;
  double? r;
  double? totalRow;
  ThongTinDieuHanhNhan(
      {this.maTtdhKc,
      this.maLoaiTtdh,
      this.maCtcbTao,
      this.tieuDe,
      this.noiDung,
      this.ngayTao,
      this.ngayGui,
      this.sttTtdh,
      this.srcFileTtdh,
      this.trangThaiTtdh,
      this.traLoiChoTtdh,
      this.chuyenTiepTuTtdh,
      this.maTtdhGoc,
      this.srcVanBanLienQuan,
      this.chuoiMaVblq,
      this.maTtdhCu,
      this.maLoaiTtdhCu,
      this.maCtcbTaoCu,
      this.maTtdhGocCu,
      this.chuoiMaVblqCu,
      this.ngayXem,
      this.dem,
      this.trangThaiBold,
      this.maCtcbNhan,
      this.trangThaiTtdhGui,
      this.maTtdhGuiKc,
      this.tenNguoiGui,
      this.tenDonViGui,
      this.ngayNhan,
      this.r,
      this.totalRow});

  factory ThongTinDieuHanhNhan.fromJson(Map<String, dynamic> json) =>
      ThongTinDieuHanhNhan(
          maTtdhKc: json["ma_ttdh_kc"],
          maLoaiTtdh: json["ma_loai_ttdh"],
          maCtcbTao: json["ma_ctcb_tao"],
          tieuDe: json["tieu_de"],
          noiDung: json["noi_dung"],
          ngayTao:
              json["ngay_tao"] == null ? "" : DateTime.parse(json["ngay_tao"]),
          ngayGui:
              json["ngay_gui"] == null ? "" : DateTime.parse(json["ngay_gui"]),
          sttTtdh: json["stt_ttdh"],
          srcFileTtdh: json["src_file_ttdh"],
          trangThaiTtdh: json["trang_thai_ttdh"],
          traLoiChoTtdh: json["tra_loi_cho_ttdh"],
          chuyenTiepTuTtdh: json["chuyen_tiep_tu_ttdh"],
          maTtdhGoc: json["ma_ttdh_goc"],
          srcVanBanLienQuan: json["src_van_ban_lien_quan"],
          chuoiMaVblq: json["chuoi_ma_vblq"],
          maTtdhCu: json["ma_ttdh_cu"],
          maLoaiTtdhCu: json["ma_loai_ttdh_cu"],
          maCtcbTaoCu: json["ma_ctcb_tao_cu"],
          maTtdhGocCu: json["ma_ttdh_goc_cu"],
          chuoiMaVblqCu: json["chuoi_ma_vblq_cu"],
          ngayXem: json["ngay_xem"] == null
              ? ""
              : DateFormat('dd/mm/yyyy hh:ss')
                  .format(DateTime.parse(json["ngay_xem"])),
          dem: json["dem"],
          trangThaiBold: json["trang_thai_bold"],
          maCtcbNhan: json["ma_ctcb_nhan"],
          trangThaiTtdhGui: json["trang_thai_ttdh_gui"],
          maTtdhGuiKc: json["ma_ttdh_gui_kc"],
          tenNguoiGui: json["ten_nguoi_gui"],
          tenDonViGui: json["ten_don_vi_gui"],
          ngayNhan: json["ngay_nhan"] == null
              ? ""
              : DateFormat('dd/mm/yyyy hh:ss')
                  .format(DateTime.parse(json["ngay_nhan"])),
          r: json["r"],
          totalRow: json["total_row"]);

  Map<String, dynamic> toJson() => {
        "ma_ttdh_kc": maTtdhKc,
        "ma_loai_ttdh": maLoaiTtdh,
        "ma_ctcb_tao": maCtcbTao,
        "tieu_de": tieuDe,
        "noi_dung": noiDung,
        "ngay_tao": ngayTao?.toIso8601String(),
        "ngay_gui": ngayGui?.toIso8601String(),
        "stt_ttdh": sttTtdh,
        "src_file_ttdh": srcFileTtdh,
        "trang_thai_ttdh": trangThaiTtdh,
        "tra_loi_cho_ttdh": traLoiChoTtdh,
        "chuyen_tiep_tu_ttdh": chuyenTiepTuTtdh,
        "ma_ttdh_goc": maTtdhGoc,
        "src_van_ban_lien_quan": srcVanBanLienQuan,
        "chuoi_ma_vblq": chuoiMaVblq,
        "ma_ttdh_cu": maTtdhCu,
        "ma_loai_ttdh_cu": maLoaiTtdhCu,
        "ma_ctcb_tao_cu": maCtcbTaoCu,
        "ma_ttdh_goc_cu": maTtdhGocCu,
        "chuoi_ma_vblq_cu": chuoiMaVblqCu,
        "ngay_xem": ngayXem?.toIso8601String(),
        "dem": dem,
        "trang_thai_bold": trangThaiBold,
        "ma_ctcb_nhan": maCtcbNhan,
        "trang_thai_ttdh_gui": trangThaiTtdhGui,
        "ma_ttdh_gui_kc": maTtdhGuiKc,
        "ten_nguoi_gui": tenNguoiGui,
        "ten_don_vi_gui": tenDonViGui,
        "ngay_nhan": ngayNhan?.toIso8601String(),
        "r": r,
        "total_row": totalRow
      };
}
