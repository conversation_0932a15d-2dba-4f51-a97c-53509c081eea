// To parse this JSON data, do
//
//     final mdLoaiThongDiep = mdLoaiThongDiepFromJson(jsonString);

import 'dart:convert';

MdLoaiThongDiep mdLoaiThongDiepFromJson(String str) =>
    MdLoaiThongDiep.fromJson(json.decode(str));

String mdLoaiThongDiepToJson(MdLoaiThongDiep data) =>
    json.encode(data.toJson());

class MdLoaiThongDiep {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<LoaiThongDiep>? data;

  MdLoaiThongDiep({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory MdLoaiThongDiep.fromJson(Map<String, dynamic> json) =>
      MdLoaiThongDiep(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<LoaiThongDiep>.from(
                json["data"]!.map((x) => LoaiThongDiep.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class LoaiThongDiep {
  double? maLoaiTtdhKc;
  String? tenLoaiTtdh;
  double? sttLoaiTtdh;
  double? trangThaiLoaiTtdh;
  double? maLoaiTtdhCu;

  LoaiThongDiep({
    this.maLoaiTtdhKc,
    this.tenLoaiTtdh,
    this.sttLoaiTtdh,
    this.trangThaiLoaiTtdh,
    this.maLoaiTtdhCu,
  });

  factory LoaiThongDiep.fromJson(Map<String, dynamic> json) => LoaiThongDiep(
        maLoaiTtdhKc: json["ma_loai_ttdh_kc"],
        tenLoaiTtdh: json["ten_loai_ttdh"],
        sttLoaiTtdh: json["stt_loai_ttdh"],
        trangThaiLoaiTtdh: json["trang_thai_loai_ttdh"],
        maLoaiTtdhCu: json["ma_loai_ttdh_cu"],
      );

  Map<String, dynamic> toJson() => {
        "ma_loai_ttdh_kc": maLoaiTtdhKc,
        "ten_loai_ttdh": tenLoaiTtdh,
        "stt_loai_ttdh": sttLoaiTtdh,
        "trang_thai_loai_ttdh": trangThaiLoaiTtdh,
        "ma_loai_ttdh_cu": maLoaiTtdhCu,
      };
}
