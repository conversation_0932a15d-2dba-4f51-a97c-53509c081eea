// To parse this JSON data, do
//
//     final dsTttdhDetail = dsTttdhDetailFromJson(jsonString);

import 'dart:convert';

DsTttdhDetail dsTttdhDetailFromJson(String str) =>
    DsTttdhDetail.fromJson(json.decode(str));

String dsTttdhDetailToJson(DsTttdhDetail data) => json.encode(data.toJson());

class DsTttdhDetail {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<TTDHChiTiet>? data;

  DsTttdhDetail({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsTttdhDetail.fromJson(Map<String, dynamic> json) => DsTttdhDetail(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<TTDHChiTiet>.from(
                json["data"]!.map((x) => TTDHChiTiet.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class TTDHChiTiet {
  double? maTtdhKc;
  double? maLoaiTtdh;
  double? maCtcbTao;
  String? tieuDe;
  String? noiDung;
  DateTime? ngayTao;
  DateTime? ngayGui;
  double? sttTtdh;
  String? srcFileTtdh;
  double? trangThaiTtdh;
  double? maTtdhGoc;
  dynamic srcVanBanLienQuan;
  dynamic chuoiMaVblq;
  String? ngayGuiVn;
  double? maCanBoGui;
  String? tenNguoiGui;
  String? tenDonViNguoiGui;
  String? diDongCanBo;
  double? gioiTinh;
  dynamic avatarNguoiGui;
  String? tenLoaiTtdh;
  double? maTtdhGuiKc;
  double? maCtcbNhan;
  double? maDonVi;
  double? maDvDd;
  String? tenDvDd;
  double? maDvCaoNhat;
  String? tenDvCaoNhat;

  TTDHChiTiet({
    this.maTtdhKc,
    this.maLoaiTtdh,
    this.maCtcbTao,
    this.tieuDe,
    this.noiDung,
    this.ngayTao,
    this.ngayGui,
    this.sttTtdh,
    this.srcFileTtdh,
    this.trangThaiTtdh,
    this.maTtdhGoc,
    this.srcVanBanLienQuan,
    this.chuoiMaVblq,
    this.ngayGuiVn,
    this.maCanBoGui,
    this.tenNguoiGui,
    this.tenDonViNguoiGui,
    this.diDongCanBo,
    this.gioiTinh,
    this.avatarNguoiGui,
    this.tenLoaiTtdh,
    this.maTtdhGuiKc,
    this.maCtcbNhan,
    this.maDonVi,
    this.maDvDd,
    this.tenDvDd,
    this.maDvCaoNhat,
    this.tenDvCaoNhat,
  });

  factory TTDHChiTiet.fromJson(Map<String, dynamic> json) => TTDHChiTiet(
        maTtdhKc: json["ma_ttdh_kc"],
        maLoaiTtdh: json["ma_loai_ttdh"],
        maCtcbTao: json["ma_ctcb_tao"],
        tieuDe: json["tieu_de"],
        noiDung: json["noi_dung"],
        ngayTao:
            json["ngay_tao"] == null ? null : DateTime.parse(json["ngay_tao"]),
        ngayGui:
            json["ngay_gui"] == null ? null : DateTime.parse(json["ngay_gui"]),
        sttTtdh: json["stt_ttdh"],
        srcFileTtdh: json["src_file_ttdh"],
        trangThaiTtdh: json["trang_thai_ttdh"],
        maTtdhGoc: json["ma_ttdh_goc"],
        srcVanBanLienQuan: json["src_van_ban_lien_quan"],
        chuoiMaVblq: json["chuoi_ma_vblq"],
        ngayGuiVn: json["ngay_gui_vn"],
        maCanBoGui: json["ma_can_bo_gui"],
        tenNguoiGui: json["ten_nguoi_gui"],
        tenDonViNguoiGui: json["ten_don_vi_nguoi_gui"],
        diDongCanBo: json["di_dong_can_bo"],
        gioiTinh: json["gioi_tinh"],
        avatarNguoiGui: json["avatar_nguoi_gui"],
        tenLoaiTtdh: json["ten_loai_ttdh"],
        maTtdhGuiKc: json["ma_ttdh_gui_kc"],
        maCtcbNhan: json["ma_ctcb_nhan"],
        maDonVi: json["ma_don_vi"],
        maDvDd: json["ma_dv_dd"],
        tenDvDd: json["ten_dv_dd"],
        maDvCaoNhat: json["ma_dv_cao_nhat"],
        tenDvCaoNhat: json["ten_dv_cao_nhat"],
      );

  Map<String, dynamic> toJson() => {
        "ma_ttdh_kc": maTtdhKc,
        "ma_loai_ttdh": maLoaiTtdh,
        "ma_ctcb_tao": maCtcbTao,
        "tieu_de": tieuDe,
        "noi_dung": noiDung,
        "ngay_tao": ngayTao?.toIso8601String(),
        "ngay_gui": ngayGui?.toIso8601String(),
        "stt_ttdh": sttTtdh,
        "src_file_ttdh": srcFileTtdh,
        "trang_thai_ttdh": trangThaiTtdh,
        "ma_ttdh_goc": maTtdhGoc,
        "src_van_ban_lien_quan": srcVanBanLienQuan,
        "chuoi_ma_vblq": chuoiMaVblq,
        "ngay_gui_vn": ngayGuiVn,
        "ma_can_bo_gui": maCanBoGui,
        "ten_nguoi_gui": tenNguoiGui,
        "ten_don_vi_nguoi_gui": tenDonViNguoiGui,
        "di_dong_can_bo": diDongCanBo,
        "gioi_tinh": gioiTinh,
        "avatar_nguoi_gui": avatarNguoiGui,
        "ten_loai_ttdh": tenLoaiTtdh,
        "ma_ttdh_gui_kc": maTtdhGuiKc,
        "ma_ctcb_nhan": maCtcbNhan,
        "ma_don_vi": maDonVi,
        "ma_dv_dd": maDvDd,
        "ten_dv_dd": tenDvDd,
        "ma_dv_cao_nhat": maDvCaoNhat,
        "ten_dv_cao_nhat": tenDvCaoNhat,
      };
}

// To parse this JSON data, do
//
//     final mdDsCanBoTtdh = mdDsCanBoTtdhFromJson(jsonString);

MdDsCanBoTtdh mdDsCanBoTtdhFromJson(String str) =>
    MdDsCanBoTtdh.fromJson(json.decode(str));

String mdDsCanBoTtdhToJson(MdDsCanBoTtdh data) => json.encode(data.toJson());

class MdDsCanBoTtdh {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsCanBoTtdh>? data;

  MdDsCanBoTtdh({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory MdDsCanBoTtdh.fromJson(Map<String, dynamic> json) => MdDsCanBoTtdh(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsCanBoTtdh>.from(
                json["data"]!.map((x) => DsCanBoTtdh.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsCanBoTtdh {
  double? maCtcbKc;
  DateTime? ngayBatDau;
  dynamic ngayKetThuc;
  double? maChucVu;
  double? maCanBo;
  double? maDonVi;
  double? laMacDinh;
  String? refreshToken;
  dynamic laLanhDao;
  double? maQuyenSauKhiDangNhap;
  dynamic mauChuKySo;
  dynamic maCanBoCu;
  dynamic maToCu;
  dynamic lanhDaoCu;
  dynamic vanThuCu;
  double? trangThaiOnline;
  double? biKhoa;
  String? smartcaAccessToken;
  double? smartcaExpire;
  dynamic smartcaThUid;
  dynamic smartcaThPassword;
  dynamic smartcaThTotp;
  String? hoVaTenCanBo;
  double? trangThaiTtdhGui;
  String? diDongCanBo;
  double? maCtcbGui;
  DateTime? ngayXem;

  DsCanBoTtdh({
    this.maCtcbKc,
    this.ngayBatDau,
    this.ngayKetThuc,
    this.maChucVu,
    this.maCanBo,
    this.maDonVi,
    this.laMacDinh,
    this.refreshToken,
    this.laLanhDao,
    this.maQuyenSauKhiDangNhap,
    this.mauChuKySo,
    this.maCanBoCu,
    this.maToCu,
    this.lanhDaoCu,
    this.vanThuCu,
    this.trangThaiOnline,
    this.biKhoa,
    this.smartcaAccessToken,
    this.smartcaExpire,
    this.smartcaThUid,
    this.smartcaThPassword,
    this.smartcaThTotp,
    this.hoVaTenCanBo,
    this.trangThaiTtdhGui,
    this.diDongCanBo,
    this.maCtcbGui,
    this.ngayXem,
  });

  factory DsCanBoTtdh.fromJson(Map<String, dynamic> json) => DsCanBoTtdh(
        maCtcbKc: json["ma_ctcb_kc"],
        ngayBatDau: json["ngay_bat_dau"] == null
            ? null
            : DateTime.parse(json["ngay_bat_dau"]),
        ngayKetThuc: json["ngay_ket_thuc"],
        maChucVu: json["ma_chuc_vu"],
        maCanBo: json["ma_can_bo"],
        maDonVi: json["ma_don_vi"],
        laMacDinh: json["la_mac_dinh"],
        refreshToken: json["refresh_token"],
        laLanhDao: json["la_lanh_dao"],
        maQuyenSauKhiDangNhap: json["ma_quyen_sau_khi_dang_nhap"],
        mauChuKySo: json["mau_chu_ky_so"],
        maCanBoCu: json["ma_can_bo_cu"],
        maToCu: json["ma_to_cu"],
        lanhDaoCu: json["lanh_dao_cu"],
        vanThuCu: json["van_thu_cu"],
        trangThaiOnline: json["trang_thai_online"],
        biKhoa: json["bi_khoa"],
        smartcaAccessToken: json["smartca_access_token"],
        smartcaExpire: json["smartca_expire"],
        smartcaThUid: json["smartca_th_uid"],
        smartcaThPassword: json["smartca_th_password"],
        smartcaThTotp: json["smartca_th_totp"],
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        trangThaiTtdhGui: json["trang_thai_ttdh_gui"],
        diDongCanBo: json["di_dong_can_bo"],
        maCtcbGui: json["ma_ctcb_gui"],
        ngayXem:
            json["ngay_xem"] == null ? null : DateTime.parse(json["ngay_xem"]),
      );

  Map<String, dynamic> toJson() => {
        "ma_ctcb_kc": maCtcbKc,
        "ngay_bat_dau": ngayBatDau?.toIso8601String(),
        "ngay_ket_thuc": ngayKetThuc,
        "ma_chuc_vu": maChucVu,
        "ma_can_bo": maCanBo,
        "ma_don_vi": maDonVi,
        "la_mac_dinh": laMacDinh,
        "refresh_token": refreshToken,
        "la_lanh_dao": laLanhDao,
        "ma_quyen_sau_khi_dang_nhap": maQuyenSauKhiDangNhap,
        "mau_chu_ky_so": mauChuKySo,
        "ma_can_bo_cu": maCanBoCu,
        "ma_to_cu": maToCu,
        "lanh_dao_cu": lanhDaoCu,
        "van_thu_cu": vanThuCu,
        "trang_thai_online": trangThaiOnline,
        "bi_khoa": biKhoa,
        "smartca_access_token": smartcaAccessToken,
        "smartca_expire": smartcaExpire,
        "smartca_th_uid": smartcaThUid,
        "smartca_th_password": smartcaThPassword,
        "smartca_th_totp": smartcaThTotp,
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "trang_thai_ttdh_gui": trangThaiTtdhGui,
        "di_dong_can_bo": diDongCanBo,
        "ma_ctcb_gui": maCtcbGui,
        "ngay_xem": ngayXem?.toIso8601String(),
      };
}
