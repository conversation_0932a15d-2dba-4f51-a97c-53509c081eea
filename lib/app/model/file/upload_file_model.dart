class UploadFileModel {
  int? id;
  String? message;
  dynamic dataInput;
  List<DetaiUploadFile>? data;
  dynamic error;
  dynamic errorDetail;
  dynamic errorApi;
  int? maCtcb;

  UploadFileModel(
      {this.id,
      this.message,
      this.dataInput,
      this.data,
      this.error,
      this.errorDetail,
      this.errorApi,
      this.maCtcb});

  UploadFileModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    message = json['message'];
    dataInput = json['data_input'];
    if (json['data'] != null) {
      data = <DetaiUploadFile>[];
      json['data'].forEach((v) {
        data!.add(new DetaiUploadFile.fromJson(v));
      });
    }
    error = json['error'];
    errorDetail = json['error_detail'];
    errorApi = json['error_api'];
    maCtcb = json['ma_ctcb'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['message'] = this.message;
    data['data_input'] = this.dataInput;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['error'] = this.error;
    data['error_detail'] = this.errorDetail;
    data['error_api'] = this.errorApi;
    data['ma_ctcb'] = this.maCtcb;
    return data;
  }
}

class DetaiUploadFile {
  int? maFileKc;
  String? fileName;
  String? message;
  int? size;
  String? path;
  int? type;

  DetaiUploadFile(
      {this.maFileKc,
      this.fileName,
      this.message,
      this.size,
      this.path,
      this.type});

  DetaiUploadFile.fromJson(Map<String, dynamic> json) {
    maFileKc = json['ma_file_kc'];
    fileName = json['fileName'];
    message = json['message'];
    size = json['size'];
    path = json['path'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_file_kc'] = this.maFileKc;
    data['fileName'] = this.fileName;
    data['message'] = this.message;
    data['size'] = this.size;
    data['path'] = this.path;
    data['type'] = this.type;
    return data;
  }
}
