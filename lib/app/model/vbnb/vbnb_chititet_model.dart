// To parse this JSON data, do
//
//     final vbnbChiTietVanBanNoij = vbnbChiTietVanBanNoijFromJson(jsonString);

import 'dart:convert';

VbnbChiTietVanBanNoi vbnbChiTietVanBanNoijFromJson(String str) =>
    VbnbChiTietVanBanNoi.fromJson(json.decode(str));

String vbnbChiTietVanBanNoijToJson(VbnbChiTietVanBanNoi data) =>
    json.encode(data.toJson());

class VbnbChiTietVanBanNoi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetaisVbnb>? data;
  Param? param;

  VbnbChiTietVanBanNoi({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory VbnbChiTietVanBanNoi.fromJson(Map<String, dynamic> json) =>
      VbnbChiTietVanBanNoi(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DetaisVbnb>.from(
                json["data"]!.map((x) => DetaisVbnb.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class DetaisVbnb {
  double? maVbnbKc;
  String? trichYeu;
  dynamic soHieu;
  DateTime? ngayLuu;
  double? maCoQuanBanHanh;
  double? maCtcbLuu;
  dynamic nguoiKy;
  dynamic noiNhan;
  String? noiLuu;
  dynamic soSaoY;
  dynamic srcVanBan;
  dynamic srcVanBanLienQuan;
  double? maLoaiVanBan;
  double? maCapDo;
  dynamic ghiChu;
  double? trangThai;
  double? maLinhVucVanBan;
  dynamic maCtcbCapNhat;
  dynamic ngayCapNhat;
  dynamic maCtcbCapSo;
  dynamic chuoiMaVblq;
  dynamic maVbnbCu;
  dynamic maLoaiVanBanCu;
  dynamic maCoQuanBanHanhCu;
  dynamic chuoiMaVblqCu;
  dynamic maCbLuuCu;
  dynamic tenCoQuanBanHanhCu;
  dynamic trangThaiCu;
  dynamic maVbCtCu;
  dynamic soSaoYCu;
  String? xuatXu;
  double? daXoa;
  DateTime? ngayNhan;
  double? trangThaiXuLy;
  String? tenCoQuanBanHanh;
  String? tenCanBoLuu;
  String? tenLoaiVanBan;
  String? tenCapDo;
  String? tenLinhVucVanBan;
  double? maCtcbGui;
  double? maVanBanNoiBoGuiKc;
  String? tenCanBoGui;
  String? diDongCanBoGui;

  DetaisVbnb({
    this.maVbnbKc,
    this.trichYeu,
    this.soHieu,
    this.ngayLuu,
    this.maCoQuanBanHanh,
    this.maCtcbLuu,
    this.nguoiKy,
    this.noiNhan,
    this.noiLuu,
    this.soSaoY,
    this.srcVanBan,
    this.srcVanBanLienQuan,
    this.maLoaiVanBan,
    this.maCapDo,
    this.ghiChu,
    this.trangThai,
    this.maLinhVucVanBan,
    this.maCtcbCapNhat,
    this.ngayCapNhat,
    this.maCtcbCapSo,
    this.chuoiMaVblq,
    this.maVbnbCu,
    this.maLoaiVanBanCu,
    this.maCoQuanBanHanhCu,
    this.chuoiMaVblqCu,
    this.maCbLuuCu,
    this.tenCoQuanBanHanhCu,
    this.trangThaiCu,
    this.maVbCtCu,
    this.soSaoYCu,
    this.xuatXu,
    this.daXoa,
    this.ngayNhan,
    this.trangThaiXuLy,
    this.tenCoQuanBanHanh,
    this.tenCanBoLuu,
    this.tenLoaiVanBan,
    this.tenCapDo,
    this.tenLinhVucVanBan,
    this.maCtcbGui,
    this.maVanBanNoiBoGuiKc,
    this.tenCanBoGui,
    this.diDongCanBoGui,
  });

  factory DetaisVbnb.fromJson(Map<String, dynamic> json) => DetaisVbnb(
        maVbnbKc: json["ma_vbnb_kc"],
        trichYeu: json["trich_yeu"],
        soHieu: json["so_hieu"],
        ngayLuu:
            json["ngay_luu"] == null ? null : DateTime.parse(json["ngay_luu"]),
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        maCtcbLuu: json["ma_ctcb_luu"],
        nguoiKy: json["nguoi_ky"],
        noiNhan: json["noi_nhan"],
        noiLuu: json["noi_luu"],
        soSaoY: json["so_sao_y"],
        srcVanBan: json["src_van_ban"],
        srcVanBanLienQuan: json["src_van_ban_lien_quan"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDo: json["ma_cap_do"],
        ghiChu: json["ghi_chu"],
        trangThai: json["trang_thai"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maCtcbCapNhat: json["ma_ctcb_cap_nhat"],
        ngayCapNhat: json["ngay_cap_nhat"],
        maCtcbCapSo: json["ma_ctcb_cap_so"],
        chuoiMaVblq: json["chuoi_ma_vblq"],
        maVbnbCu: json["ma_vbnb_cu"],
        maLoaiVanBanCu: json["ma_loai_van_ban_cu"],
        maCoQuanBanHanhCu: json["ma_co_quan_ban_hanh_cu"],
        chuoiMaVblqCu: json["chuoi_ma_vblq_cu"],
        maCbLuuCu: json["ma_cb_luu_cu"],
        tenCoQuanBanHanhCu: json["ten_co_quan_ban_hanh_cu"],
        trangThaiCu: json["trang_thai_cu"],
        maVbCtCu: json["ma_vb_ct_cu"],
        soSaoYCu: json["so_sao_y_cu"],
        xuatXu: json["xuat_xu"],
        daXoa: json["da_xoa"],
        ngayNhan: json["ngay_nhan"] == null
            ? null
            : DateTime.parse(json["ngay_nhan"]),
        trangThaiXuLy: json["trang_thai_xu_ly"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        tenCanBoLuu: json["ten_can_bo_luu"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        tenCapDo: json["ten_cap_do"],
        tenLinhVucVanBan: json["ten_linh_vuc_van_ban"],
        maCtcbGui: json["ma_ctcb_gui"],
        maVanBanNoiBoGuiKc: json["ma_van_ban_noi_bo_gui_kc"],
        tenCanBoGui: json["ten_can_bo_gui"],
        diDongCanBoGui: json["di_dong_can_bo_gui"],
      );

  Map<String, dynamic> toJson() => {
        "ma_vbnb_kc": maVbnbKc,
        "trich_yeu": trichYeu,
        "so_hieu": soHieu,
        "ngay_luu": ngayLuu?.toIso8601String(),
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "ma_ctcb_luu": maCtcbLuu,
        "nguoi_ky": nguoiKy,
        "noi_nhan": noiNhan,
        "noi_luu": noiLuu,
        "so_sao_y": soSaoY,
        "src_van_ban": srcVanBan,
        "src_van_ban_lien_quan": srcVanBanLienQuan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do": maCapDo,
        "ghi_chu": ghiChu,
        "trang_thai": trangThai,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_ctcb_cap_nhat": maCtcbCapNhat,
        "ngay_cap_nhat": ngayCapNhat,
        "ma_ctcb_cap_so": maCtcbCapSo,
        "chuoi_ma_vblq": chuoiMaVblq,
        "ma_vbnb_cu": maVbnbCu,
        "ma_loai_van_ban_cu": maLoaiVanBanCu,
        "ma_co_quan_ban_hanh_cu": maCoQuanBanHanhCu,
        "chuoi_ma_vblq_cu": chuoiMaVblqCu,
        "ma_cb_luu_cu": maCbLuuCu,
        "ten_co_quan_ban_hanh_cu": tenCoQuanBanHanhCu,
        "trang_thai_cu": trangThaiCu,
        "ma_vb_ct_cu": maVbCtCu,
        "so_sao_y_cu": soSaoYCu,
        "xuat_xu": xuatXu,
        "da_xoa": daXoa,
        "ngay_nhan": ngayNhan?.toIso8601String(),
        "trang_thai_xu_ly": trangThaiXuLy,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ten_can_bo_luu": tenCanBoLuu,
        "ten_loai_van_ban": tenLoaiVanBan,
        "ten_cap_do": tenCapDo,
        "ten_linh_vuc_van_ban": tenLinhVucVanBan,
        "ma_ctcb_gui": maCtcbGui,
        "ma_van_ban_noi_bo_gui_kc": maVanBanNoiBoGuiKc,
        "ten_can_bo_gui": tenCanBoGui,
        "di_dong_can_bo_gui": diDongCanBoGui,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}
