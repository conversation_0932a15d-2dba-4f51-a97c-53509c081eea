// To parse this JSON data, do
//
//     final vbnbDanhSachNhanCv = vbnbDanhSachNhanCvFromJson(jsonString);

import 'dart:convert';

VbnbDanhSachNhanCv vbnbDanhSachNhanCvFromJson(String str) =>
    VbnbDanhSachNhanCv.fromJson(json.decode(str));

String vbnbDanhSachNhanCvTo<PERSON>son(VbnbDanhSachNhanCv data) =>
    json.encode(data.toJson());

class VbnbDanhSachNhanCv {
  int? totalPage;
  int? totalRow;
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DanhSachVBNB>? data;
  Param? param;

  VbnbDanhSachNhanCv({
    this.totalPage,
    this.totalRow,
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory VbnbDanhSachNhanCv.fromJson(Map<String, dynamic> json) =>
      VbnbDanhSachNhanCv(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DanhSachVBNB>.from(
                json["data"]!.map((x) => DanhSachVBNB.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class DanhSachVBNB {
  double? maVbnbKc;
  String? trichYeu;
  String? soHieu;
  DateTime? ngayLuu;
  double? maCoQuanBanHanh;
  double? maCtcbLuu;
  dynamic nguoiKy;
  dynamic noiNhan;
  String? noiLuu;
  dynamic soSaoY;
  String? srcVanBan;
  dynamic srcVanBanLienQuan;
  double? maLoaiVanBan;
  double? maCapDo;
  dynamic ghiChu;
  double? trangThai;
  double? maLinhVucVanBan;
  double? maCtcbCapNhat;
  DateTime? ngayCapNhat;
  double? maCtcbCapSo;
  dynamic chuoiMaVblq;
  dynamic maVbnbCu;
  dynamic maLoaiVanBanCu;
  dynamic maCoQuanBanHanhCu;
  dynamic chuoiMaVblqCu;
  dynamic maCbLuuCu;
  dynamic tenCoQuanBanHanhCu;
  dynamic trangThaiCu;
  dynamic maVbCtCu;
  dynamic soSaoYCu;
  String? xuatXu;
  double? daXoa;
  DateTime? ngayGui;
  double? trangThaiXuLy;
  double? maVanBanNoiBoGuiKc;
  double? xem;
  String? tenCanBoGui;
  double? maCapDoKhan;
  String? tenDonViQuanTri;
  double? maCtcbGui;
  double? r;

  DanhSachVBNB({
    this.maVbnbKc,
    this.trichYeu,
    this.soHieu,
    this.ngayLuu,
    this.maCoQuanBanHanh,
    this.maCtcbLuu,
    this.nguoiKy,
    this.noiNhan,
    this.noiLuu,
    this.soSaoY,
    this.srcVanBan,
    this.srcVanBanLienQuan,
    this.maLoaiVanBan,
    this.maCapDo,
    this.ghiChu,
    this.trangThai,
    this.maLinhVucVanBan,
    this.maCtcbCapNhat,
    this.ngayCapNhat,
    this.maCtcbCapSo,
    this.chuoiMaVblq,
    this.maVbnbCu,
    this.maLoaiVanBanCu,
    this.maCoQuanBanHanhCu,
    this.chuoiMaVblqCu,
    this.maCbLuuCu,
    this.tenCoQuanBanHanhCu,
    this.trangThaiCu,
    this.maVbCtCu,
    this.soSaoYCu,
    this.xuatXu,
    this.daXoa,
    this.ngayGui,
    this.trangThaiXuLy,
    this.maVanBanNoiBoGuiKc,
    this.xem,
    this.tenCanBoGui,
    this.maCapDoKhan,
    this.tenDonViQuanTri,
    this.maCtcbGui,
    this.r,
  });

  factory DanhSachVBNB.fromJson(Map<String, dynamic> json) => DanhSachVBNB(
        maVbnbKc: json["ma_vbnb_kc"],
        trichYeu: json["trich_yeu"],
        soHieu: json["so_hieu"],
        ngayLuu:
            json["ngay_luu"] == null ? null : DateTime.parse(json["ngay_luu"]),
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        maCtcbLuu: json["ma_ctcb_luu"],
        nguoiKy: json["nguoi_ky"],
        noiNhan: json["noi_nhan"],
        noiLuu: json["noi_luu"],
        soSaoY: json["so_sao_y"],
        srcVanBan: json["src_van_ban"],
        srcVanBanLienQuan: json["src_van_ban_lien_quan"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDo: json["ma_cap_do"],
        ghiChu: json["ghi_chu"],
        trangThai: json["trang_thai"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maCtcbCapNhat: json["ma_ctcb_cap_nhat"],
        ngayCapNhat: json["ngay_cap_nhat"] == null
            ? null
            : DateTime.parse(json["ngay_cap_nhat"]),
        maCtcbCapSo: json["ma_ctcb_cap_so"],
        chuoiMaVblq: json["chuoi_ma_vblq"],
        maVbnbCu: json["ma_vbnb_cu"],
        maLoaiVanBanCu: json["ma_loai_van_ban_cu"],
        maCoQuanBanHanhCu: json["ma_co_quan_ban_hanh_cu"],
        chuoiMaVblqCu: json["chuoi_ma_vblq_cu"],
        maCbLuuCu: json["ma_cb_luu_cu"],
        tenCoQuanBanHanhCu: json["ten_co_quan_ban_hanh_cu"],
        trangThaiCu: json["trang_thai_cu"],
        maVbCtCu: json["ma_vb_ct_cu"],
        soSaoYCu: json["so_sao_y_cu"],
        xuatXu: json["xuat_xu"],
        daXoa: json["da_xoa"],
        ngayGui:
            json["ngay_gui"] == null ? null : DateTime.parse(json["ngay_gui"]),
        trangThaiXuLy: json["trang_thai_xu_ly"],
        maVanBanNoiBoGuiKc: json["ma_van_ban_noi_bo_gui_kc"],
        xem: json["xem"],
        tenCanBoGui: json["ten_can_bo_gui"],
        maCapDoKhan: json["ma_cap_do_khan"],
        tenDonViQuanTri: json["ten_don_vi_quan_tri"],
        maCtcbGui: json["ma_ctcb_gui"],
        r: json["r"],
      );

  Map<String, dynamic> toJson() => {
        "ma_vbnb_kc": maVbnbKc,
        "trich_yeu": trichYeu,
        "so_hieu": soHieu,
        "ngay_luu": ngayLuu?.toIso8601String(),
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "ma_ctcb_luu": maCtcbLuu,
        "nguoi_ky": nguoiKy,
        "noi_nhan": noiNhan,
        "noi_luu": noiLuu,
        "so_sao_y": soSaoY,
        "src_van_ban": srcVanBan,
        "src_van_ban_lien_quan": srcVanBanLienQuan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do": maCapDo,
        "ghi_chu": ghiChu,
        "trang_thai": trangThai,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_ctcb_cap_nhat": maCtcbCapNhat,
        "ngay_cap_nhat": ngayCapNhat?.toIso8601String(),
        "ma_ctcb_cap_so": maCtcbCapSo,
        "chuoi_ma_vblq": chuoiMaVblq,
        "ma_vbnb_cu": maVbnbCu,
        "ma_loai_van_ban_cu": maLoaiVanBanCu,
        "ma_co_quan_ban_hanh_cu": maCoQuanBanHanhCu,
        "chuoi_ma_vblq_cu": chuoiMaVblqCu,
        "ma_cb_luu_cu": maCbLuuCu,
        "ten_co_quan_ban_hanh_cu": tenCoQuanBanHanhCu,
        "trang_thai_cu": trangThaiCu,
        "ma_vb_ct_cu": maVbCtCu,
        "so_sao_y_cu": soSaoYCu,
        "xuat_xu": xuatXu,
        "da_xoa": daXoa,
        "ngay_gui": ngayGui?.toIso8601String(),
        "trang_thai_xu_ly": trangThaiXuLy,
        "ma_van_ban_noi_bo_gui_kc": maVanBanNoiBoGuiKc,
        "xem": xem,
        "ten_can_bo_gui": tenCanBoGui,
        "ma_cap_do_khan": maCapDoKhan,
        "ten_don_vi_quan_tri": tenDonViQuanTri,
        "ma_ctcb_gui": maCtcbGui,
        "r": r,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}
