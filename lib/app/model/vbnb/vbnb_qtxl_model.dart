// To parse this JSON data, do
//
//     final vbnbQtxlVanBanNoiBo = vbnbQtxlVanBanNoiBoFromJson(jsonString);

import 'dart:convert';

VbnbQtxlVanBanNoiBo vbnbQtxlVanBanNoiBoFromJson(String str) =>
    VbnbQtxlVanBanNoiBo.fromJson(json.decode(str));

String vbnbQtxlVanBanNoiBoToJson(VbnbQtxlVanBanNoiBo data) =>
    json.encode(data.toJson());

class VbnbQtxlVanBanNoiBo {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<QtxlVanBanNoiBo>? data;
  Param? param;

  VbnbQtxlVanBanNoiBo({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory VbnbQtxlVanBanNoiBo.fromJson(Map<String, dynamic> json) =>
      VbnbQtxlVanBanNoiBo(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<QtxlVanBanNoiBo>.from(
                json["data"]!.map((x) => QtxlVanBanNoiBo.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class QtxlVanBanNoiBo {
  double? maVanBanNoiBoGuiKc;
  double? maVbnbGuiCha;
  double? maCtcbGui;
  double? maCtcbNhan;
  String? tenCanBoNhan;
  String? tenDonVi;
  String? tenDonViQuanTri;
  double? trangThaiXuLy;
  DateTime? ngayGui;
  DateTime? ngayXem;
  double? xem;
  double? gioiTinh;
  dynamic yKienXuLy;
  dynamic fileDinhKem;

  QtxlVanBanNoiBo({
    this.maVanBanNoiBoGuiKc,
    this.maVbnbGuiCha,
    this.maCtcbGui,
    this.maCtcbNhan,
    this.tenCanBoNhan,
    this.tenDonVi,
    this.tenDonViQuanTri,
    this.trangThaiXuLy,
    this.ngayGui,
    this.ngayXem,
    this.xem,
    this.gioiTinh,
    this.yKienXuLy,
    this.fileDinhKem,
  });

  factory QtxlVanBanNoiBo.fromJson(Map<String, dynamic> json) =>
      QtxlVanBanNoiBo(
        maVanBanNoiBoGuiKc: json["ma_van_ban_noi_bo_gui_kc"],
        maVbnbGuiCha: json["ma_vbnb_gui_cha"],
        maCtcbGui: json["ma_ctcb_gui"],
        maCtcbNhan: json["ma_ctcb_nhan"],
        tenCanBoNhan: json["ten_can_bo_nhan"],
        tenDonVi: json["ten_don_vi"],
        tenDonViQuanTri: json["ten_don_vi_quan_tri"],
        trangThaiXuLy: json["trang_thai_xu_ly"],
        ngayGui:
            json["ngay_gui"] == null ? null : DateTime.parse(json["ngay_gui"]),
        ngayXem:
            json["ngay_xem"] == null ? null : DateTime.parse(json["ngay_xem"]),
        xem: json["xem"],
        gioiTinh: json["gioi_tinh"],
        yKienXuLy: json["y_kien_xu_ly"],
        fileDinhKem: json["file_dinh_kem"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_noi_bo_gui_kc": maVanBanNoiBoGuiKc,
        "ma_vbnb_gui_cha": maVbnbGuiCha,
        "ma_ctcb_gui": maCtcbGui,
        "ma_ctcb_nhan": maCtcbNhan,
        "ten_can_bo_nhan": tenCanBoNhan,
        "ten_don_vi": tenDonVi,
        "ten_don_vi_quan_tri": tenDonViQuanTri,
        "trang_thai_xu_ly": trangThaiXuLy,
        "ngay_gui": ngayGui?.toIso8601String(),
        "ngay_xem": ngayXem?.toIso8601String(),
        "xem": xem,
        "gioi_tinh": gioiTinh,
        "y_kien_xu_ly": yKienXuLy,
        "file_dinh_kem": fileDinhKem,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}
