// To parse this JSON data, do
//
//     final mdTraCuuVanBan = mdTraCuuVanBanFromJson(jsonString);

import 'dart:convert';

MdTraCuuVanBanDi mdTraCuuVanBanFromJson(String str) =>
    MdTraCuuVanBanDi.fromJson(json.decode(str));

String mdTraCuuVanBanToJson(MdTraCuuVanBanDi data) =>
    json.encode(data.toJson());

class MdTraCuuVanBanDi {
  int totalPage;
  int totalRow;
  bool success;
  String message;
  String storeName;
  int storeType;
  List<TraCuuVanBanDi> data;

  MdTraCuuVanBanDi({
    required this.totalPage,
    required this.totalRow,
    required this.success,
    required this.message,
    required this.storeName,
    required this.storeType,
    required this.data,
  });

  factory MdTraCuuVanBanDi.fromJson(Map<String, dynamic> json) =>
      MdTraCuuVanBanDi(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: List<TraCuuVanBanDi>.from(
            json["data"].map((x) => TraCuuVanBanDi.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class TraCuuVanBanDi {
  double? maVanBanDiKc;
  double? maCapDoKhan;
  double? soDi;
  String? soKyHieu;
  String? trichYeu;
  String? tenLoaiVanBan;
  DateTime? ngayTao;
  DateTime? ngayBanHanh;
  String? nguoiKy;
  double? maCtcbTao;
  double? maVanBanKc;
  String? fileVanBan;
  String? skhDen;
  double? maVanBanDenKc;

  TraCuuVanBanDi({
    this.maVanBanDiKc,
    this.maCapDoKhan,
    this.soDi,
    this.soKyHieu,
    this.trichYeu,
    this.tenLoaiVanBan,
    this.ngayTao,
    this.ngayBanHanh,
    this.nguoiKy,
    this.maCtcbTao,
    this.maVanBanKc,
    this.fileVanBan,
    this.skhDen,
    this.maVanBanDenKc,
  });

  factory TraCuuVanBanDi.fromJson(Map<String, dynamic> json) => TraCuuVanBanDi(
        maVanBanDiKc: json["ma_van_ban_di_kc"],
        maCapDoKhan: json["ma_cap_do_khan"],
        soDi: json["so_di"],
        soKyHieu: json["so_ky_hieu"],
        trichYeu: json["trich_yeu"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        ngayTao: DateTime.parse(json["ngay_tao"]),
        ngayBanHanh: DateTime.parse(json["ngay_ban_hanh"]),
        nguoiKy: json["nguoi_ky"],
        maCtcbTao: json["ma_ctcb_tao"],
        maVanBanKc: json["ma_van_ban_kc"],
        fileVanBan: json["file_van_ban"],
        skhDen: json["skh_den"],
        maVanBanDenKc: json["ma_van_ban_den_kc"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_di_kc": maVanBanDiKc,
        "ma_cap_do_khan": maCapDoKhan,
        "so_di": soDi,
        "so_ky_hieu": soKyHieu,
        "trich_yeu": trichYeu,
        "ten_loai_van_ban": tenLoaiVanBan,
        "ngay_tao": ngayTao!.toIso8601String(),
        "ngay_ban_hanh": ngayBanHanh!.toIso8601String(),
        "nguoi_ky": nguoiKy,
        "ma_ctcb_tao": maCtcbTao,
        "ma_van_ban_kc": maVanBanKc,
        "file_van_ban": fileVanBan,
        "skh_den": skhDen,
        "ma_van_ban_den_kc": maVanBanDenKc,
      };
}
