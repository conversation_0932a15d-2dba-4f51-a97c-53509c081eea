// To parse this JSON data, do
//
//     final mdTraCuuVanBanDen = mdTraCuuVanBanDenFromJson(jsonString);

import 'dart:convert';

MdTraCuuVanBanDen mdTraCuuVanBanDenFromJson(String str) =>
    MdTraCuuVanBanDen.fromJson(json.decode(str));

String mdTraCuuVanBanDenToJson(MdTraCuuVanBanDen data) =>
    json.encode(data.toJson());

class MdTraCuuVanBanDen {
  int totalPage;
  int totalRow;
  bool success;
  String message;
  String storeName;
  int storeType;
  List<TraCuuVanBanDen> data;

  MdTraCuuVanBanDen({
    required this.totalPage,
    required this.totalRow,
    required this.success,
    required this.message,
    required this.storeName,
    required this.storeType,
    required this.data,
  });

  factory MdTraCuuVanBanDen.fromJson(Map<String, dynamic> json) =>
      MdTraCuuVanBanDen(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: List<TraCuuVanBanDen>.from(
            json["data"].map((x) => TraCuuVanBanDen.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class TraCuuVanBanDen {
  double? maVanBanDenKc;
  double? maCapDoKhan;
  double? soDen;
  String? soKyHieu;
  String? trichYeu;
  String? tenLoaiVanBan;
  String? tenCoQuanBanHanh;
  DateTime? ngayTao;
  DateTime? ngayBanHanh;
  DateTime? ngayDen;
  String? nguoiKy;
  double? maCtcbTao;
  double? maVanBanKc;
  String? fileVanBanBs;
  String? hanXuLy;
  String? skhPhucDap;
  double? maVanBanDiKc;
  double? r;

  TraCuuVanBanDen({
    required this.maVanBanDenKc,
    required this.maCapDoKhan,
    required this.soDen,
    required this.soKyHieu,
    required this.trichYeu,
    required this.tenLoaiVanBan,
    required this.tenCoQuanBanHanh,
    required this.ngayTao,
    required this.ngayBanHanh,
    required this.ngayDen,
    required this.nguoiKy,
    required this.maCtcbTao,
    required this.maVanBanKc,
    required this.fileVanBanBs,
    required this.hanXuLy,
    required this.skhPhucDap,
    required this.maVanBanDiKc,
    required this.r,
  });

  factory TraCuuVanBanDen.fromJson(Map<String, dynamic> json) =>
      TraCuuVanBanDen(
        maVanBanDenKc: json["ma_van_ban_den_kc"],
        maCapDoKhan: json["ma_cap_do_khan"],
        soDen: json["so_den"],
        soKyHieu: json["so_ky_hieu"],
        trichYeu: json["trich_yeu"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        ngayTao: DateTime.parse(json["ngay_tao"]),
        ngayBanHanh: DateTime.parse(json["ngay_ban_hanh"]),
        ngayDen: DateTime.parse(json["ngay_den"]),
        nguoiKy: json["nguoi_ky"],
        maCtcbTao: json["ma_ctcb_tao"],
        maVanBanKc: json["ma_van_ban_kc"],
        fileVanBanBs: json["file_van_ban_bs"],
        hanXuLy: json["han_xu_ly"],
        skhPhucDap: json["skh_phuc_dap"],
        maVanBanDiKc: json["ma_van_ban_di_kc"],
        r: json["r"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_den_kc": maVanBanDenKc,
        "ma_cap_do_khan": maCapDoKhan,
        "so_den": soDen,
        "so_ky_hieu": soKyHieu,
        "trich_yeu": trichYeu,
        "ten_loai_van_ban": tenLoaiVanBan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ngay_tao": ngayTao!.toIso8601String() ?? "",
        "ngay_ban_hanh": ngayBanHanh!.toIso8601String() ?? "",
        "ngay_den": ngayDen!.toIso8601String() ?? "",
        "nguoi_ky": nguoiKy,
        "ma_ctcb_tao": maCtcbTao,
        "ma_van_ban_kc": maVanBanKc,
        "file_van_ban_bs": fileVanBanBs,
        "han_xu_ly": hanXuLy,
        "skh_phuc_dap": skhPhucDap,
        "ma_van_ban_di_kc": maVanBanDiKc,
        "r": r,
      };
}
