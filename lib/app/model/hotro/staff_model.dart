// Model cho dữ liệu cán bộ từ API
class StaffApiResponse {
  final bool success;
  final String message;
  final String storeName;
  final int storeType;
  final List<StaffModel> data;

  StaffApiResponse({
    required this.success,
    required this.message,
    required this.storeName,
    required this.storeType,
    required this.data,
  });

  factory StaffApiResponse.fromJson(Map<String, dynamic> json) {
    return StaffApiResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      storeName: json['store_name'] ?? '',
      storeType: json['store_type'] ?? 0,
      data: json['data'] != null
          ? (json['data'] as List)
              .map((item) => StaffModel.fromJson(item))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'store_name': storeName,
      'store_type': storeType,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

class StaffModel {
  final double maCtcbKc;
  final String hoVaTenCanBo;
  final String username;
  final String tenChucVu;
  final String? sdtCanBo;
  final String diDongCanBo;
  final String tenDonVi;
  final double maDonViKc;

  StaffModel({
    required this.maCtcbKc,
    required this.hoVaTenCanBo,
    required this.username,
    required this.tenChucVu,
    this.sdtCanBo,
    required this.diDongCanBo,
    required this.tenDonVi,
    required this.maDonViKc,
  });

  factory StaffModel.fromJson(Map<String, dynamic> json) {
    return StaffModel(
      maCtcbKc: (json['ma_ctcb_kc'] ?? 0).toDouble(),
      hoVaTenCanBo: json['ho_va_ten_can_bo'] ?? '',
      username: json['username'] ?? '',
      tenChucVu: json['ten_chuc_vu'] ?? '',
      sdtCanBo: json['sdt_can_bo']?.toString(),
      diDongCanBo: json['di_dong_can_bo']?.toString() ?? '0',
      tenDonVi: json['ten_don_vi'] ?? '',
      maDonViKc: (json['ma_don_vi_kc'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ma_ctcb_kc': maCtcbKc,
      'ho_va_ten_can_bo': hoVaTenCanBo,
      'username': username,
      'ten_chuc_vu': tenChucVu,
      'sdt_can_bo': sdtCanBo,
      'di_dong_can_bo': diDongCanBo,
      'ten_don_vi': tenDonVi,
      'ma_don_vi_kc': maDonViKc,
    };
  }

  // Method để chuyển đổi sang format cũ để tương thích
  Map<String, dynamic> toOldFormat() {
    return {
      'id': maCtcbKc.toInt(),
      'name': hoVaTenCanBo,
      'position': tenChucVu,
      'code': username,
      'phone': getPhoneNumber(),
      'unit': tenDonVi,
      'unitId': maDonViKc.toInt(),
    };
  }

  // Method để lấy số điện thoại (ưu tiên di động, fallback về sdt)
  String getPhoneNumber() {
    if (diDongCanBo.isNotEmpty && diDongCanBo != '0') {
      return diDongCanBo;
    }
    if (sdtCanBo != null && sdtCanBo!.isNotEmpty && sdtCanBo != '0') {
      return sdtCanBo!;
    }
    return '';
  }

  // Method để kiểm tra có số điện thoại không
  bool hasPhoneNumber() {
    return getPhoneNumber().isNotEmpty;
  }

  // Method để copy với các thuộc tính mới
  StaffModel copyWith({
    double? maCtcbKc,
    String? hoVaTenCanBo,
    String? username,
    String? tenChucVu,
    String? sdtCanBo,
    String? diDongCanBo,
    String? tenDonVi,
    double? maDonViKc,
  }) {
    return StaffModel(
      maCtcbKc: maCtcbKc ?? this.maCtcbKc,
      hoVaTenCanBo: hoVaTenCanBo ?? this.hoVaTenCanBo,
      username: username ?? this.username,
      tenChucVu: tenChucVu ?? this.tenChucVu,
      sdtCanBo: sdtCanBo ?? this.sdtCanBo,
      diDongCanBo: diDongCanBo ?? this.diDongCanBo,
      tenDonVi: tenDonVi ?? this.tenDonVi,
      maDonViKc: maDonViKc ?? this.maDonViKc,
    );
  }

  @override
  String toString() {
    return 'StaffModel(maCtcbKc: $maCtcbKc, hoVaTenCanBo: $hoVaTenCanBo, username: $username, tenChucVu: $tenChucVu, tenDonVi: $tenDonVi)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StaffModel &&
        other.maCtcbKc == maCtcbKc &&
        other.username == username;
  }

  @override
  int get hashCode {
    return maCtcbKc.hashCode ^ username.hashCode;
  }
}
