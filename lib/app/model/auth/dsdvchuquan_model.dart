// To parse this JSON data, do
//
//     final dsDonViChuQuan = dsDonViChuQuanFromJson(jsonString);

import 'dart:convert';

DsDonViChuQuan dsDonViChuQuanFromJson(String str) =>
    DsDonViChuQuan.fromJson(json.decode(str));

String dsDonViChuQuanToJson(DsDonViChuQuan data) => json.encode(data.toJson());

class DsDonViChuQuan {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DonVi>? data;
  Param? param;

  DsDonViChuQuan({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory DsDonViChuQuan.fromJson(Map<String, dynamic> json) => DsDonViChuQuan(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DonVi>.from(json["data"]!.map((x) => DonVi.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class DonVi {
  double? maDonViKc;
  String? tenDonVi;
  String? domainDonVi;
  String? diaChiDonVi;
  String? emailDonVi;
  String? sdtDonVi;
  double? sttDonVi;
  double? trangThaiDonVi;
  double? maLoaiDonVi;
  double? maDonViCha;
  double? soDenHienTai;
  double? soDiHienTai;
  double? coVanThu;
  String? maDinhDanh;
  double? maDonViCu;
  dynamic maToCu;
  String? domainFile;
  String? maDinhDanhCu;
  String? maSoDv;
  String? sdtHoTro;
  double? fontSizeTendv;
  String? logoDonVi;
  dynamic maDonViKhung;
  dynamic domainKhung;
  String? skinDonVi;
  String? tptkMa;
  double? maDonViChaCu;
  dynamic domainSsoSoft;
  dynamic tenDvLogin;
  dynamic maDonViElearning;
  dynamic domainSsoVp;
  DateTime? ngayTao;
  DateTime? ngayCapNhat;
  String? domainSsoYbi;
  dynamic maDonViChaTd;
  double? vaoThangTrangSso;
  String? tenVietTat;
  double? khongPhatHanh;
  double? id;
  String? text;
  double? parent;
  String? tenDonViCha;
  double? stt;
  double? laDvDinhDanh;

  DonVi({
    this.maDonViKc,
    this.tenDonVi,
    this.domainDonVi,
    this.diaChiDonVi,
    this.emailDonVi,
    this.sdtDonVi,
    this.sttDonVi,
    this.trangThaiDonVi,
    this.maLoaiDonVi,
    this.maDonViCha,
    this.soDenHienTai,
    this.soDiHienTai,
    this.coVanThu,
    this.maDinhDanh,
    this.maDonViCu,
    this.maToCu,
    this.domainFile,
    this.maDinhDanhCu,
    this.maSoDv,
    this.sdtHoTro,
    this.fontSizeTendv,
    this.logoDonVi,
    this.maDonViKhung,
    this.domainKhung,
    this.skinDonVi,
    this.tptkMa,
    this.maDonViChaCu,
    this.domainSsoSoft,
    this.tenDvLogin,
    this.maDonViElearning,
    this.domainSsoVp,
    this.ngayTao,
    this.ngayCapNhat,
    this.domainSsoYbi,
    this.maDonViChaTd,
    this.vaoThangTrangSso,
    this.tenVietTat,
    this.khongPhatHanh,
    this.id,
    this.text,
    this.parent,
    this.tenDonViCha,
    this.stt,
    this.laDvDinhDanh,
  });

  factory DonVi.fromJson(Map<String, dynamic> json) => DonVi(
        maDonViKc: json["ma_don_vi_kc"],
        tenDonVi: json["ten_don_vi"],
        domainDonVi: json["domain_don_vi"],
        diaChiDonVi: json["dia_chi_don_vi"],
        emailDonVi: json["email_don_vi"],
        sdtDonVi: json["sdt_don_vi"],
        sttDonVi: json["stt_don_vi"],
        trangThaiDonVi: json["trang_thai_don_vi"],
        maLoaiDonVi: json["ma_loai_don_vi"],
        maDonViCha: json["ma_don_vi_cha"],
        soDenHienTai: json["so_den_hien_tai"],
        soDiHienTai: json["so_di_hien_tai"],
        coVanThu: json["co_van_thu"],
        maDinhDanh: json["ma_dinh_danh"],
        maDonViCu: json["ma_don_vi_cu"],
        maToCu: json["ma_to_cu"],
        domainFile: json["domain_file"],
        maDinhDanhCu: json["ma_dinh_danh_cu"],
        maSoDv: json["ma_so_dv"],
        sdtHoTro: json["sdt_ho_tro"],
        fontSizeTendv: json["font_size_tendv"],
        logoDonVi: json["logo_don_vi"],
        maDonViKhung: json["ma_don_vi_khung"],
        domainKhung: json["domain_khung"],
        skinDonVi: json["skin_don_vi"],
        tptkMa: json["tptk_ma"],
        maDonViChaCu: json["ma_don_vi_cha_cu"],
        domainSsoSoft: json["domain_sso_soft"],
        tenDvLogin: json["ten_dv_login"],
        maDonViElearning: json["ma_don_vi_elearning"],
        domainSsoVp: json["domain_sso_vp"],
        ngayTao:
            json["ngay_tao"] == null ? null : DateTime.parse(json["ngay_tao"]),
        ngayCapNhat: json["ngay_cap_nhat"] == null
            ? null
            : DateTime.parse(json["ngay_cap_nhat"]),
        domainSsoYbi: json["domain_sso_ybi"],
        maDonViChaTd: json["ma_don_vi_cha_td"],
        vaoThangTrangSso: json["vao_thang_trang_sso"],
        tenVietTat: json["ten_viet_tat"],
        khongPhatHanh: json["khong_phat_hanh"],
        id: json["id"],
        text: json["text"],
        parent: json["parent"],
        tenDonViCha: json["ten_don_vi_cha"],
        stt: json["stt"],
        laDvDinhDanh: json["la_dv_dinh_danh"],
      );

  Map<String, dynamic> toJson() => {
        "ma_don_vi_kc": maDonViKc,
        "ten_don_vi": tenDonVi,
        "domain_don_vi": domainDonVi,
        "dia_chi_don_vi": diaChiDonVi,
        "email_don_vi": emailDonVi,
        "sdt_don_vi": sdtDonVi,
        "stt_don_vi": sttDonVi,
        "trang_thai_don_vi": trangThaiDonVi,
        "ma_loai_don_vi": maLoaiDonVi,
        "ma_don_vi_cha": maDonViCha,
        "so_den_hien_tai": soDenHienTai,
        "so_di_hien_tai": soDiHienTai,
        "co_van_thu": coVanThu,
        "ma_dinh_danh": maDinhDanh,
        "ma_don_vi_cu": maDonViCu,
        "ma_to_cu": maToCu,
        "domain_file": domainFile,
        "ma_dinh_danh_cu": maDinhDanhCu,
        "ma_so_dv": maSoDv,
        "sdt_ho_tro": sdtHoTro,
        "font_size_tendv": fontSizeTendv,
        "logo_don_vi": logoDonVi,
        "ma_don_vi_khung": maDonViKhung,
        "domain_khung": domainKhung,
        "skin_don_vi": skinDonVi,
        "tptk_ma": tptkMa,
        "ma_don_vi_cha_cu": maDonViChaCu,
        "domain_sso_soft": domainSsoSoft,
        "ten_dv_login": tenDvLogin,
        "ma_don_vi_elearning": maDonViElearning,
        "domain_sso_vp": domainSsoVp,
        "ngay_tao": ngayTao?.toIso8601String(),
        "ngay_cap_nhat": ngayCapNhat?.toIso8601String(),
        "domain_sso_ybi": domainSsoYbi,
        "ma_don_vi_cha_td": maDonViChaTd,
        "vao_thang_trang_sso": vaoThangTrangSso,
        "ten_viet_tat": tenVietTat,
        "khong_phat_hanh": khongPhatHanh,
        "id": id,
        "text": text,
        "parent": parent,
        "ten_don_vi_cha": tenDonViCha,
        "stt": stt,
        "la_dv_dinh_danh": laDvDinhDanh,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}
