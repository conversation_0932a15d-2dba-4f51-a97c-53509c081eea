// To parse this JSON data, do
//
//     final authModel = authModelFromJson(jsonString);

import 'dart:convert';

import 'package:flutter/widgets.dart';

AuthModel authModelFromJson(String str) => AuthModel.fromJson(json.decode(str));

String authModelToJson(AuthModel data) => json.encode(data.toJson());

class AuthModel {
  AuthModel({
    required this.data,
    required this.success,
    required this.message,
    required this.laProgrammer,
    required this.ssoYbiCheckOut,
    required this.techId,
  });

  List<Datum> data;
  bool success;
  String? message;
  int? laProgrammer;
  int? ssoYbiCheckOut;
  String? techId;

  factory AuthModel.fromJson(Map<String, dynamic> json) => AuthModel(
        data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
        success: json["success"],
        message: json["message"],
        laProgrammer: json["la_programmer"],
        ssoYbiCheckOut: json["sso_ybi_check_out"],
        techId: json["tech_id"],
      );

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "success": success,
        "message": message,
        "la_programmer": laProgrammer,
        "sso_ybi_check_out": ssoYbiCheckOut,
        "tech_id": techId,
      };
}

class Datum {
  Datum({
    required this.maCtcbKc,
    required this.ngayBatDau,
    this.ngayKetThuc,
    required this.maChucVu,
    required this.maCanBo,
    required this.maDonVi,
    required this.laMacDinh,
    required this.refreshToken,
    this.laLanhDao,
    required this.maQuyenSauKhiDangNhap,
    this.mauChuKySo,
    this.maCanBoCu,
    this.maToCu,
    this.lanhDaoCu,
    this.vanThuCu,
    required this.trangThaiOnline,
    required this.biKhoa,
    required this.smartcaAccessToken,
    required this.smartcaExpire,
    required this.maDonViCha,
    required this.maLoaiDonVi,
    required this.hoVaTenCanBo,
    required this.tenChucVu,
    required this.tenDonVi,
    this.ngayDangKyOtp,
    this.ngayDangKyNhacViecQuaSms,
    required this.diDongCanBo,
    this.avatar,
    required this.username,
    this.pwd,
    this.idVm,
    required this.dnMacDinh,
    this.loaiKySo,
    this.loaiKySoSim,
    this.tptkMaDvCha,
  });

  double maCtcbKc;
  DateTime ngayBatDau;
  dynamic ngayKetThuc;
  double? maChucVu;
  double? maCanBo;
  double? maDonVi;
  double? laMacDinh;
  String refreshToken;
  dynamic laLanhDao;
  double? maQuyenSauKhiDangNhap;
  dynamic mauChuKySo;
  dynamic maCanBoCu;
  dynamic maToCu;
  dynamic lanhDaoCu;
  dynamic vanThuCu;
  double? trangThaiOnline;
  double? biKhoa;
  String? smartcaAccessToken;
  double? smartcaExpire;
  double? maDonViCha;
  double? maLoaiDonVi;
  String? hoVaTenCanBo;
  String? tenChucVu;
  String? tenDonVi;
  dynamic ngayDangKyOtp;
  dynamic ngayDangKyNhacViecQuaSms;
  String? diDongCanBo;
  dynamic avatar;
  String? username;
  dynamic pwd;
  dynamic idVm;
  double? dnMacDinh;
  dynamic loaiKySo;
  dynamic loaiKySoSim;
  dynamic tptkMaDvCha;

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        maCtcbKc: json["ma_ctcb_kc"],
        ngayBatDau: DateTime.parse(json["ngay_bat_dau"]),
        ngayKetThuc: json["ngay_ket_thuc"],
        maChucVu: json["ma_chuc_vu"],
        maCanBo: json["ma_can_bo"],
        maDonVi: json["ma_don_vi"],
        laMacDinh: json["la_mac_dinh"],
        refreshToken: json["refresh_token"],
        laLanhDao: json["la_lanh_dao"],
        maQuyenSauKhiDangNhap: json["ma_quyen_sau_khi_dang_nhap"],
        mauChuKySo: json["mau_chu_ky_so"],
        maCanBoCu: json["ma_can_bo_cu"],
        maToCu: json["ma_to_cu"],
        lanhDaoCu: json["lanh_dao_cu"],
        vanThuCu: json["van_thu_cu"],
        trangThaiOnline: json["trang_thai_online"],
        biKhoa: json["bi_khoa"],
        smartcaAccessToken: json["smartca_access_token"],
        smartcaExpire: json["smartca_expire"],
        maDonViCha: json["ma_don_vi_cha"],
        maLoaiDonVi: json["ma_loai_don_vi"],
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        tenDonVi: json["ten_don_vi"],
        ngayDangKyOtp: json["ngay_dang_ky_otp"],
        ngayDangKyNhacViecQuaSms: json["ngay_dang_ky_nhac_viec_qua_sms"],
        diDongCanBo: json["di_dong_can_bo"],
        avatar: json["avatar"],
        username: json["username"],
        pwd: json["pwd"],
        idVm: json["id_vm"],
        dnMacDinh: json["dn_mac_dinh"],
        loaiKySo: json["loai_ky_so"],
        loaiKySoSim: json["loai_ky_so_sim"],
        tptkMaDvCha: json["tptk_ma_dv_cha"],
      );

  Map<String, dynamic> toJson() => {
        "ma_ctcb_kc": maCtcbKc,
        "ngay_bat_dau": ngayBatDau.toIso8601String(),
        "ngay_ket_thuc": ngayKetThuc,
        "ma_chuc_vu": maChucVu,
        "ma_can_bo": maCanBo,
        "ma_don_vi": maDonVi,
        "la_mac_dinh": laMacDinh,
        "refresh_token": refreshToken,
        "la_lanh_dao": laLanhDao,
        "ma_quyen_sau_khi_dang_nhap": maQuyenSauKhiDangNhap,
        "mau_chu_ky_so": mauChuKySo,
        "ma_can_bo_cu": maCanBoCu,
        "ma_to_cu": maToCu,
        "lanh_dao_cu": lanhDaoCu,
        "van_thu_cu": vanThuCu,
        "trang_thai_online": trangThaiOnline,
        "bi_khoa": biKhoa,
        "smartca_access_token": smartcaAccessToken,
        "smartca_expire": smartcaExpire,
        "ma_don_vi_cha": maDonViCha,
        "ma_loai_don_vi": maLoaiDonVi,
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "ten_chuc_vu": tenChucVu,
        "ten_don_vi": tenDonVi,
        "ngay_dang_ky_otp": ngayDangKyOtp,
        "ngay_dang_ky_nhac_viec_qua_sms": ngayDangKyNhacViecQuaSms,
        "di_dong_can_bo": diDongCanBo,
        "avatar": avatar,
        "username": username,
        "pwd": pwd,
        "id_vm": idVm,
        "dn_mac_dinh": dnMacDinh,
        "loai_ky_so": loaiKySo,
        "loai_ky_so_sim": loaiKySoSim,
        "tptk_ma_dv_cha": tptkMaDvCha,
      };
}

class FileBase {
  String? urlViewFile;
  IconData? iconFile;
  String? fileName;
  Color? colorIcon;
  FileBase(
      {required this.urlViewFile,
      required this.iconFile,
      this.fileName,
      this.colorIcon});
  factory FileBase.fromJson(Map<String, dynamic> json) => FileBase(
      urlViewFile: json["urlViewFile"],
      iconFile: json["iconFile"],
      fileName: json["fileName"]);
}
