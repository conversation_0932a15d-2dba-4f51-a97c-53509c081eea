// To parse this JSON data, do
//
//     final dsThamSo = dsThamSoFromJson(jsonString);

import 'dart:convert';

DsThamSo dsThamSoFromJson(String str) => DsThamSo.fromJson(json.decode(str));

String dsThamSoToJson(DsThamSo data) => json.encode(data.toJson());

class DsThamSo {
  bool success;
  String message;
  String storeName;
  int storeType;
  List<DetailThamSo> data;

  DsThamSo({
    required this.success,
    required this.message,
    required this.storeName,
    required this.storeType,
    required this.data,
  });

  factory DsThamSo.fromJson(Map<String, dynamic> json) => DsThamSo(
      success: json["success"],
      message: json["message"],
      storeName: json["store_name"],
      storeType: json["store_type"],
      data: List<DetailThamSo>.from(
          json["data"].map((x) => DetailThamSo.fromJson(x))));

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class DetailThamSo {
  String maThamSo;
  String giaTriThamSo;

  DetailThamSo({
    required this.maThamSo,
    required this.giaTriThamSo,
  });

  factory DetailThamSo.fromJson(Map<String, dynamic> json) => DetailThamSo(
        maThamSo: json["ma_tham_so"],
        giaTriThamSo: json["gia_tri_tham_so"],
      );

  Map<String, dynamic> toJson() => {
        "ma_tham_so": maThamSo,
        "gia_tri_tham_so": giaTriThamSo,
      };
}
