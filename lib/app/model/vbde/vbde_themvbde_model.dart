// To parse this JSON data, do
//
//     final empty = emptyFromJson(jsonString);

import 'dart:convert';

class DanhSachDMSoVbde {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DMSoVbde>? data;

  DanhSachDMSoVbde({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DanhSachDMSoVbde.fromJson(Map<String, dynamic> json) =>
      DanhSachDMSoVbde(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DMSoVbde>.from(
                json["data"]!.map((x) => DMSoVbde.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DMSoVbde {
  double? maSoVbDenKc;
  String? tenSoVbDen;
  double? maDonViQuanTri;
  double? trangThaiSoVbDen;
  double? sttSoVbDen;
  double? maSoVbDenCu;
  double? maDonViQuanTriCu;
  double? soDenHienTai;
  double? nam;

  DMSoVbde({
    this.maSoVbDenKc,
    this.tenSoVbDen,
    this.maDonViQuanTri,
    this.trangThaiSoVbDen,
    this.sttSoVbDen,
    this.maSoVbDenCu,
    this.maDonViQuanTriCu,
    this.soDenHienTai,
    this.nam,
  });

  factory DMSoVbde.fromJson(Map<String, dynamic> json) => DMSoVbde(
        maSoVbDenKc: json["ma_so_vb_den_kc"],
        tenSoVbDen: json["ten_so_vb_den"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        trangThaiSoVbDen: json["trang_thai_so_vb_den"],
        sttSoVbDen: json["stt_so_vb_den"],
        maSoVbDenCu: json["ma_so_vb_den_cu"],
        maDonViQuanTriCu: json["ma_don_vi_quan_tri_cu"],
        soDenHienTai: json["so_den_hien_tai"],
        nam: json["nam"],
      );

  Map<String, dynamic> toJson() => {
        "ma_so_vb_den_kc": maSoVbDenKc,
        "ten_so_vb_den": tenSoVbDen,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "trang_thai_so_vb_den": trangThaiSoVbDen,
        "stt_so_vb_den": sttSoVbDen,
        "ma_so_vb_den_cu": maSoVbDenCu,
        "ma_don_vi_quan_tri_cu": maDonViQuanTriCu,
        "so_den_hien_tai": soDenHienTai,
        "nam": nam,
      };
}

// To parse this JSON data, do
//
//     final laySoDenBySoVb = laySoDenBySoVbFromJson(jsonString);

LaySoDenBySoVb laySoDenBySoVbFromJson(String str) =>
    LaySoDenBySoVb.fromJson(json.decode(str));

String laySoDenBySoVbToJson(LaySoDenBySoVb data) => json.encode(data.toJson());

class LaySoDenBySoVb {
  bool? success;
  String? message;
  LaySoDen? data;
  String? storeName;
  int? storeType;

  LaySoDenBySoVb({
    this.success,
    this.message,
    this.data,
    this.storeName,
    this.storeType,
  });

  factory LaySoDenBySoVb.fromJson(Map<String, dynamic> json) => LaySoDenBySoVb(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null ? null : LaySoDen.fromJson(json["data"]),
        storeName: json["store_name"],
        storeType: json["store_type"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data?.toJson(),
        "store_name": storeName,
        "store_type": storeType,
      };
}

class LaySoDen {
  double? soDenHienTai;

  LaySoDen({
    this.soDenHienTai,
  });

  factory LaySoDen.fromJson(Map<String, dynamic> json) => LaySoDen(
        soDenHienTai: json["so_den_hien_tai"],
      );

  Map<String, dynamic> toJson() => {
        "so_den_hien_tai": soDenHienTai,
      };
}

// To parse this JSON data, do
//
//     final dsDmLoaiVb = dsDmLoaiVbFromJson(jsonString);

DsDmLoaiVb dsDmLoaiVbFromJson(String str) =>
    DsDmLoaiVb.fromJson(json.decode(str));

String dsDmLoaiVbToJson(DsDmLoaiVb data) => json.encode(data.toJson());

class DsDmLoaiVb {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<LoaiVanBan>? data;

  DsDmLoaiVb({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsDmLoaiVb.fromJson(Map<String, dynamic> json) => DsDmLoaiVb(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<LoaiVanBan>.from(
                json["data"]!.map((x) => LoaiVanBan.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class LoaiVanBan {
  double? maLoaiVanBanKc;
  String? tenLoaiVanBan;
  double? sttLoaiVanBan;
  double? trangThaiLoaiVanBan;
  String? kyHieu;
  double? maLoaiVanBanCu;
  dynamic maLoaiTrung;
  dynamic maVic;

  LoaiVanBan({
    this.maLoaiVanBanKc,
    this.tenLoaiVanBan,
    this.sttLoaiVanBan,
    this.trangThaiLoaiVanBan,
    this.kyHieu,
    this.maLoaiVanBanCu,
    this.maLoaiTrung,
    this.maVic,
  });

  factory LoaiVanBan.fromJson(Map<String, dynamic> json) => LoaiVanBan(
        maLoaiVanBanKc: json["ma_loai_van_ban_kc"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        sttLoaiVanBan: json["stt_loai_van_ban"],
        trangThaiLoaiVanBan: json["trang_thai_loai_van_ban"],
        kyHieu: json["ky_hieu"],
        maLoaiVanBanCu: json["ma_loai_van_ban_cu"],
        maLoaiTrung: json["ma_loai_trung"],
        maVic: json["ma_vic"],
      );

  Map<String, dynamic> toJson() => {
        "ma_loai_van_ban_kc": maLoaiVanBanKc,
        "ten_loai_van_ban": tenLoaiVanBan,
        "stt_loai_van_ban": sttLoaiVanBan,
        "trang_thai_loai_van_ban": trangThaiLoaiVanBan,
        "ky_hieu": kyHieu,
        "ma_loai_van_ban_cu": maLoaiVanBanCu,
        "ma_loai_trung": maLoaiTrung,
        "ma_vic": maVic,
      };
}

// To parse this JSON data, do
//
//     final dsDmCapDoKhan = dsDmCapDoKhanFromJson(jsonString);

DsDmCapDoKhan dsDmCapDoKhanFromJson(String str) =>
    DsDmCapDoKhan.fromJson(json.decode(str));

String dsDmCapDoKhanToJson(DsDmCapDoKhan data) => json.encode(data.toJson());

class DsDmCapDoKhan {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<dsCapDoKhan>? data;

  DsDmCapDoKhan({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsDmCapDoKhan.fromJson(Map<String, dynamic> json) => DsDmCapDoKhan(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<dsCapDoKhan>.from(
                json["data"]!.map((x) => dsCapDoKhan.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class dsCapDoKhan {
  double? maCapDoKhanKc;
  String? tenCapDoKhan;
  double? sttCapDoKhan;
  double? trangThaiCapDoKhan;
  dynamic thuocTinhMau;
  double? maCapDoKhanCu;

  dsCapDoKhan({
    this.maCapDoKhanKc,
    this.tenCapDoKhan,
    this.sttCapDoKhan,
    this.trangThaiCapDoKhan,
    this.thuocTinhMau,
    this.maCapDoKhanCu,
  });

  factory dsCapDoKhan.fromJson(Map<String, dynamic> json) => dsCapDoKhan(
        maCapDoKhanKc: json["ma_cap_do_khan_kc"],
        tenCapDoKhan: json["ten_cap_do_khan"],
        sttCapDoKhan: json["stt_cap_do_khan"],
        trangThaiCapDoKhan: json["trang_thai_cap_do_khan"],
        thuocTinhMau: json["thuoc_tinh_mau"],
        maCapDoKhanCu: json["ma_cap_do_khan_cu"],
      );

  Map<String, dynamic> toJson() => {
        "ma_cap_do_khan_kc": maCapDoKhanKc,
        "ten_cap_do_khan": tenCapDoKhan,
        "stt_cap_do_khan": sttCapDoKhan,
        "trang_thai_cap_do_khan": trangThaiCapDoKhan,
        "thuoc_tinh_mau": thuocTinhMau,
        "ma_cap_do_khan_cu": maCapDoKhanCu,
      };
}

// To parse this JSON data, do
//
//     final dsDmCapDoMat = dsDmCapDoMatFromJson(jsonString);

DsDmCapDoMat dsDmCapDoMatFromJson(String str) =>
    DsDmCapDoMat.fromJson(json.decode(str));

String dsDmCapDoMatToJson(DsDmCapDoMat data) => json.encode(data.toJson());

class DsDmCapDoMat {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DmCapDoMat>? data;

  DsDmCapDoMat({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsDmCapDoMat.fromJson(Map<String, dynamic> json) => DsDmCapDoMat(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DmCapDoMat>.from(
                json["data"]!.map((x) => DmCapDoMat.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DmCapDoMat {
  double? maCapDoMatKc;
  String? tenCapDoMat;
  double? sttCapDoMat;
  double? trangThaiCapDoMat;

  DmCapDoMat({
    this.maCapDoMatKc,
    this.tenCapDoMat,
    this.sttCapDoMat,
    this.trangThaiCapDoMat,
  });

  factory DmCapDoMat.fromJson(Map<String, dynamic> json) => DmCapDoMat(
        maCapDoMatKc: json["ma_cap_do_mat_kc"],
        tenCapDoMat: json["ten_cap_do_mat"],
        sttCapDoMat: json["stt_cap_do_mat"],
        trangThaiCapDoMat: json["trang_thai_cap_do_mat"],
      );

  Map<String, dynamic> toJson() => {
        "ma_cap_do_mat_kc": maCapDoMatKc,
        "ten_cap_do_mat": tenCapDoMat,
        "stt_cap_do_mat": sttCapDoMat,
        "trang_thai_cap_do_mat": trangThaiCapDoMat,
      };
}

// To parse this JSON data, do
//
//     final dsLanhDaoDuyetVbde = dsLanhDaoDuyetVbdeFromJson(jsonString);

DsLanhDaoDuyetVbde dsLanhDaoDuyetVbdeFromJson(String str) =>
    DsLanhDaoDuyetVbde.fromJson(json.decode(str));

String dsLanhDaoDuyetVbdeToJson(DsLanhDaoDuyetVbde data) =>
    json.encode(data.toJson());

class DsLanhDaoDuyetVbde {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<LanhDaoDuyetVbde>? data;

  DsLanhDaoDuyetVbde({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsLanhDaoDuyetVbde.fromJson(Map<String, dynamic> json) =>
      DsLanhDaoDuyetVbde(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<LanhDaoDuyetVbde>.from(
                json["data"]!.map((x) => LanhDaoDuyetVbde.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class LanhDaoDuyetVbde {
  double? ma;
  double? maCanBoKc;
  String? ten;
  String? tenDonVi;
  String? diDongCanBo;
  String? tenChucVu;
  String? emailCanBo;
  double? sttCanBo;
  double? laMacDinh;

  LanhDaoDuyetVbde({
    this.ma,
    this.maCanBoKc,
    this.ten,
    this.tenDonVi,
    this.diDongCanBo,
    this.tenChucVu,
    this.emailCanBo,
    this.sttCanBo,
    this.laMacDinh,
  });

  factory LanhDaoDuyetVbde.fromJson(Map<String, dynamic> json) =>
      LanhDaoDuyetVbde(
        ma: json["ma"],
        maCanBoKc: json["ma_can_bo_kc"],
        ten: json["ten"],
        tenDonVi: json["ten_don_vi"],
        diDongCanBo: json["di_dong_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        emailCanBo: json["email_can_bo"],
        sttCanBo: json["stt_can_bo"],
        laMacDinh: json["la_mac_dinh"],
      );

  Map<String, dynamic> toJson() => {
        "ma": ma,
        "ma_can_bo_kc": maCanBoKc,
        "ten": ten,
        "ten_don_vi": tenDonVi,
        "di_dong_can_bo": diDongCanBo,
        "ten_chuc_vu": tenChucVu,
        "email_can_bo": emailCanBo,
        "stt_can_bo": sttCanBo,
        "la_mac_dinh": laMacDinh,
      };
}

// To parse this JSON data, do
//
//     final getInfoEdxml = getInfoEdxmlFromJson(jsonString);

GetInfoEdxml getInfoEdxmlFromJson(String str) =>
    GetInfoEdxml.fromJson(json.decode(str));

String getInfoEdxmlToJson(GetInfoEdxml data) => json.encode(data.toJson());

class GetInfoEdxml {
  int? id;
  String? message;
  dynamic dataInput;
  InFoEdxml? data;
  dynamic error;
  dynamic errorDetail;
  dynamic errorApi;
  int? maCtcb;

  GetInfoEdxml({
    this.id,
    this.message,
    this.dataInput,
    this.data,
    this.error,
    this.errorDetail,
    this.errorApi,
    this.maCtcb,
  });

  factory GetInfoEdxml.fromJson(Map<String, dynamic> json) => GetInfoEdxml(
        id: json["id"],
        message: json["message"],
        dataInput: json["data_input"],
        data: json["data"] == null ? null : InFoEdxml.fromJson(json["data"]),
        error: json["error"],
        errorDetail: json["error_detail"],
        errorApi: json["error_api"],
        maCtcb: json["ma_ctcb"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "message": message,
        "data_input": dataInput,
        "data": data?.toJson(),
        "error": error,
        "error_detail": errorDetail,
        "error_api": errorApi,
        "ma_ctcb": maCtcb,
      };
}

class InFoEdxml {
  String? filevb;
  String? sokh;
  String? trichyeu;
  String? ngaybanhanh;
  String? noiluu;
  String? nguoiky;
  String? cqbh;
  String? sobph;
  String? replacementinfolist;
  String? responsefor;
  String? receiver;
  String? paper;
  String? madinhdanhvb;
  String? maHoSoIgateLgsp;
  String? tenCanBoIgate;
  int? bussinessdoctype;
  int? macapdokhan;

  InFoEdxml({
    this.filevb,
    this.sokh,
    this.trichyeu,
    this.ngaybanhanh,
    this.noiluu,
    this.nguoiky,
    this.cqbh,
    this.sobph,
    this.replacementinfolist,
    this.responsefor,
    this.receiver,
    this.paper,
    this.madinhdanhvb,
    this.maHoSoIgateLgsp,
    this.tenCanBoIgate,
    this.bussinessdoctype,
    this.macapdokhan,
  });

  factory InFoEdxml.fromJson(Map<String, dynamic> json) => InFoEdxml(
        filevb: json["filevb"],
        sokh: json["sokh"],
        trichyeu: json["trichyeu"],
        ngaybanhanh: json["ngaybanhanh"],
        noiluu: json["noiluu"],
        nguoiky: json["nguoiky"],
        cqbh: json["cqbh"],
        sobph: json["sobph"],
        replacementinfolist: json["replacementinfolist"],
        responsefor: json["responsefor"],
        receiver: json["receiver"],
        paper: json["paper"],
        madinhdanhvb: json["madinhdanhvb"],
        maHoSoIgateLgsp: json["ma_ho_so_igate_lgsp"],
        tenCanBoIgate: json["ten_can_bo_igate"],
        bussinessdoctype: json["bussinessdoctype"],
        macapdokhan: json["macapdokhan"],
      );

  Map<String, dynamic> toJson() => {
        "filevb": filevb,
        "sokh": sokh,
        "trichyeu": trichyeu,
        "ngaybanhanh": ngaybanhanh,
        "noiluu": noiluu,
        "nguoiky": nguoiky,
        "cqbh": cqbh,
        "sobph": sobph,
        "replacementinfolist": replacementinfolist,
        "responsefor": responsefor,
        "receiver": receiver,
        "paper": paper,
        "madinhdanhvb": madinhdanhvb,
        "ma_ho_so_igate_lgsp": maHoSoIgateLgsp,
        "ten_can_bo_igate": tenCanBoIgate,
        "bussinessdoctype": bussinessdoctype,
        "macapdokhan": macapdokhan,
      };
}

// To parse this JSON data, do
//
//     final copyEdocDocument = copyEdocDocumentFromJson(jsonString);

CopyEdocDocument copyEdocDocumentFromJson(String str) =>
    CopyEdocDocument.fromJson(json.decode(str));

String copyEdocDocumentToJson(CopyEdocDocument data) =>
    json.encode(data.toJson());

class CopyEdocDocument {
  int? id;
  String? message;
  dynamic dataInput;
  String? data;
  dynamic error;
  dynamic errorDetail;
  dynamic errorApi;
  int? maCtcb;

  CopyEdocDocument({
    this.id,
    this.message,
    this.dataInput,
    this.data,
    this.error,
    this.errorDetail,
    this.errorApi,
    this.maCtcb,
  });

  factory CopyEdocDocument.fromJson(Map<String, dynamic> json) =>
      CopyEdocDocument(
        id: json["id"],
        message: json["message"],
        dataInput: json["data_input"],
        data: json["data"],
        error: json["error"],
        errorDetail: json["error_detail"],
        errorApi: json["error_api"],
        maCtcb: json["ma_ctcb"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "message": message,
        "data_input": dataInput,
        "data": data,
        "error": error,
        "error_detail": errorDetail,
        "error_api": errorApi,
        "ma_ctcb": maCtcb,
      };
}

// To parse this JSON data, do
//
//     final dmDsLinhVuc = dmDsLinhVucFromJson(jsonString);

DmDsLinhVuc dmDsLinhVucFromJson(String str) =>
    DmDsLinhVuc.fromJson(json.decode(str));

String dmDsLinhVucToJson(DmDsLinhVuc data) => json.encode(data.toJson());

class DmDsLinhVuc {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsLinhVuc>? data;

  DmDsLinhVuc({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DmDsLinhVuc.fromJson(Map<String, dynamic> json) => DmDsLinhVuc(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsLinhVuc>.from(
                json["data"]!.map((x) => DsLinhVuc.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsLinhVuc {
  double? maLinhVucVanBanKc;
  String? tenLinhVucVanBan;
  double? sttLinhVucVanBan;
  double? trangThaiLinhVucVanBan;
  double? maLinhVucVanBanCu;

  DsLinhVuc({
    this.maLinhVucVanBanKc,
    this.tenLinhVucVanBan,
    this.sttLinhVucVanBan,
    this.trangThaiLinhVucVanBan,
    this.maLinhVucVanBanCu,
  });

  factory DsLinhVuc.fromJson(Map<String, dynamic> json) => DsLinhVuc(
        maLinhVucVanBanKc: json["ma_linh_vuc_van_ban_kc"],
        tenLinhVucVanBan: json["ten_linh_vuc_van_ban"],
        sttLinhVucVanBan: json["stt_linh_vuc_van_ban"],
        trangThaiLinhVucVanBan: json["trang_thai_linh_vuc_van_ban"],
        maLinhVucVanBanCu: json["ma_linh_vuc_van_ban_cu"],
      );

  Map<String, dynamic> toJson() => {
        "ma_linh_vuc_van_ban_kc": maLinhVucVanBanKc,
        "ten_linh_vuc_van_ban": tenLinhVucVanBan,
        "stt_linh_vuc_van_ban": sttLinhVucVanBan,
        "trang_thai_linh_vuc_van_ban": trangThaiLinhVucVanBan,
        "ma_linh_vuc_van_ban_cu": maLinhVucVanBanCu,
      };
}

List<ThemMoiVbde> themMoiVbdeFromJson(String str) => List<ThemMoiVbde>.from(
    json.decode(str).map((x) => ThemMoiVbde.fromJson(x)));

String themMoiVbdeToJson(List<ThemMoiVbde> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ThemMoiVbde {
  String? message;
  int? maVanBanDen;
  int? maXuLyDen;
  int? id;

  ThemMoiVbde({
    this.message,
    this.maVanBanDen,
    this.maXuLyDen,
    this.id,
  });

  factory ThemMoiVbde.fromJson(Map<String, dynamic> json) => ThemMoiVbde(
        message: json["message"],
        maVanBanDen: json["ma_van_ban_den"],
        maXuLyDen: json["ma_xu_ly_den"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "ma_van_ban_den": maVanBanDen,
        "ma_xu_ly_den": maXuLyDen,
        "id": id,
      };
}
