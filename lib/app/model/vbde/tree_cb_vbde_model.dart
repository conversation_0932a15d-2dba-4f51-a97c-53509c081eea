// To parse this JSON data, do
//
//     final treeCbVaiTro = treeCbVaiTroFromJson(jsonString);

import 'dart:convert';

TreeCbVaiTro treeCbVaiTroFromJson(String str) =>
    TreeCbVaiTro.fromJson(json.decode(str));

String treeCbVaiTroToJson(TreeCbVaiTro data) => json.encode(data.toJson());

class TreeCbVaiTro {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<TreeDetail>? data;
  Param? param;

  TreeCbVaiTro({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory TreeCbVaiTro.fromJson(Map<String, dynamic> json) => TreeCbVaiTro(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<TreeDetail>.from(
                json["data"]!.map((x) => TreeDetail.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class TreeDetail {
  String? id;
  String? parent;
  String? text;
  Icon? icon;
  LiAttr? liAttr;
  State? state;

  TreeDetail({
    this.id,
    this.parent,
    this.text,
    this.icon,
    this.liAttr,
    this.state,
  });

  factory TreeDetail.fromJson(Map<String, dynamic> json) => TreeDetail(
        id: json["id"],
        parent: json["parent"],
        text: json["text"],
        icon: iconValues.map[json["icon"]]!,
        liAttr:
            json["li_attr"] == null ? null : LiAttr.fromJson(json["li_attr"]),
        state: json["state"] == null ? null : State.fromJson(json["state"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "parent": parent,
        "text": text,
        "icon": iconValues.reverse[icon],
        "li_attr": liAttr?.toJson(),
        "state": state?.toJson(),
      };
}

enum Icon {
  IMAGES_USER_GROUP_PNG,
  IMAGES_USER_MALE_PNG,
  IMAGES_USER_FEMALE_PNG
}

final iconValues = EnumValues({
  "/images/user_female.png": Icon.IMAGES_USER_FEMALE_PNG,
  "/images/user-group.png": Icon.IMAGES_USER_GROUP_PNG,
  "/images/user_male.png": Icon.IMAGES_USER_MALE_PNG
});

class LiAttr {
  String? dataId;
  String? dataTenCanBo;
  String? dataChucVu;
  String? dataPhone;
  String? dataEmail;
  double? dataChiDaoTrucTiep;
  String? dataTenDonVi;

  LiAttr({
    this.dataId,
    this.dataTenCanBo,
    this.dataChucVu,
    this.dataPhone,
    this.dataEmail,
    this.dataChiDaoTrucTiep,
    this.dataTenDonVi,
  });

  factory LiAttr.fromJson(Map<String, dynamic> json) => LiAttr(
        dataId: json["data_id"],
        dataTenCanBo: json["data_ten_can_bo"],
        dataChucVu: json["data_chuc_vu"],
        dataPhone: json["data_phone"],
        dataEmail: json["data_email"],
        dataChiDaoTrucTiep: json["data_chi_dao_truc_tiep"],
        dataTenDonVi: json["data_ten_don_vi"],
      );

  Map<String, dynamic> toJson() => {
        "data_id": dataId,
        "data_ten_can_bo": dataTenCanBo,
        "data_chuc_vu": dataChucVu,
        "data_phone": dataPhone,
        "data_email": dataEmail,
        "data_chi_dao_truc_tiep": dataChiDaoTrucTiep,
        "data_ten_don_vi": dataTenDonVi,
      };
}

class State {
  bool? loaded;
  bool? opened;
  bool? selected;
  bool? disabled;

  State({
    this.loaded,
    this.opened,
    this.selected,
    this.disabled,
  });

  factory State.fromJson(Map<String, dynamic> json) => State(
        loaded: json["loaded"],
        opened: json["opened"],
        selected: json["selected"],
        disabled: json["disabled"],
      );

  Map<String, dynamic> toJson() => {
        "loaded": loaded,
        "opened": opened,
        "selected": selected,
        "disabled": disabled,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
