// To parse this JSON data, do
//
//     final vbDenDienTuChuaLuu = vbDenDienTuChuaLuuFromJson(jsonString);

import 'dart:convert';

VbDenDienTuChuaLuu vbDenDienTuChuaLuuFromJson(String str) =>
    VbDenDienTuChuaLuu.fromJson(json.decode(str));

String vbDenDienTuChuaLuuToJson(VbDenDienTuChuaLuu data) =>
    json.encode(data.toJson());

class VbDenDienTuChuaLuu {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsVanDenDienTu>? data;
  Param? param;

  VbDenDienTuChuaLuu({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory VbDenDienTuChuaLuu.fromJson(Map<String, dynamic> json) =>
      VbDenDienTuChuaLuu(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsVanDenDienTu>.from(
                json["data"]!.map((x) => DsVanDenDienTu.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class DsVanDenDienTu {
  double? maVanBanDenKc;
  double? loaiVbDienTu;
  double? maVanBanKc;
  double? maXuLyDen;
  dynamic soDen;
  String? trichYeu;
  String? soKyHieu;
  dynamic tenGoiTinXml;
  DateTime? ngayNhan;
  String? ngayXem;
  String? ngayGui;
  String? fileVanBanBs;
  String? fileVanBan;
  double? maLinhVucVanBan;
  double? maLoaiVanBan;
  double? maCapDoKhan;
  double? maCapDoMat;
  double? maSoVbDen;
  String? tenCoQuanBanHanh;
  String? tenDonViGui;
  String? ngayDen;
  DateTime? ngayDenOrder;
  dynamic hanXuLy;
  String? ngayBanHanh;
  dynamic soIoffice;
  double? guiKemVbGiay;
  String? soBanPhatHanh;
  dynamic soTrangVb;
  String? nguoiKy;
  String? noiLuuBanChinh;
  dynamic maCtcbDuyet;
  dynamic hienThiVblq;
  String? chuoiVbDienTu;
  double? daLuuFile;
  double? bussinessDoctype;
  dynamic vblqNgoaiHeThong;
  double? r;
  double? totalRow;
  String? ngayDuyet;
  double? trangThaiVanBanDen;
  int? sms;
  String? ghiChu;
  int? nam;

  DsVanDenDienTu(
      {this.maVanBanDenKc,
      this.loaiVbDienTu,
      this.maVanBanKc,
      this.maXuLyDen,
      this.soDen,
      this.trichYeu,
      this.soKyHieu,
      this.tenGoiTinXml,
      this.ngayNhan,
      this.ngayXem,
      this.ngayGui,
      this.fileVanBanBs,
      this.fileVanBan,
      this.maLinhVucVanBan,
      this.maLoaiVanBan,
      this.maCapDoKhan,
      this.maCapDoMat,
      this.maSoVbDen,
      this.tenCoQuanBanHanh,
      this.tenDonViGui,
      this.ngayDen,
      this.ngayDenOrder,
      this.hanXuLy,
      this.ngayBanHanh,
      this.soIoffice,
      this.guiKemVbGiay,
      this.soBanPhatHanh,
      this.soTrangVb,
      this.nguoiKy,
      this.noiLuuBanChinh,
      this.maCtcbDuyet,
      this.hienThiVblq,
      this.chuoiVbDienTu,
      this.daLuuFile,
      this.bussinessDoctype,
      this.vblqNgoaiHeThong,
      this.r,
      this.ngayDuyet,
      this.trangThaiVanBanDen,
      this.sms,
      this.ghiChu,
      this.totalRow,
      this.nam});

  factory DsVanDenDienTu.fromJson(Map<String, dynamic> json) => DsVanDenDienTu(
      maVanBanDenKc: json["ma_van_ban_den_kc"],
      loaiVbDienTu: json["loai_vb_dien_tu"],
      maVanBanKc: json["ma_van_ban_kc"],
      maXuLyDen: json["ma_xu_ly_den"],
      soDen: json["so_den"],
      trichYeu: json["trich_yeu"],
      soKyHieu: json["so_ky_hieu"],
      tenGoiTinXml: json["ten_goi_tin_xml"],
      ngayNhan:
          json["ngay_nhan"] == null ? null : DateTime.parse(json["ngay_nhan"]),
      ngayXem: json["ngay_xem"],
      ngayGui: json["ngay_gui"] == null ? null : json["ngay_gui"],
      fileVanBanBs: json["file_van_ban_bs"],
      fileVanBan: json["file_van_ban"],
      maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
      maLoaiVanBan: json["ma_loai_van_ban"],
      maCapDoKhan: json["ma_cap_do_khan"],
      maCapDoMat: json["ma_cap_do_mat"],
      tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
      tenDonViGui: json["ten_don_vi_gui"],
      ngayDen: json["ngay_den"],
      ngayDenOrder: json["ngay_den_order"] == null
          ? null
          : DateTime.parse(json["ngay_den_order"]),
      hanXuLy: json["han_xu_ly"],
      ngayBanHanh: json["ngay_ban_hanh"],
      soIoffice: json["so_ioffice"],
      guiKemVbGiay: json["gui_kem_vb_giay"],
      soBanPhatHanh: json["so_ban_phat_hanh"],
      soTrangVb: json["so_trang_vb"],
      nguoiKy: json["nguoi_ky"],
      noiLuuBanChinh: json["noi_luu_ban_chinh"],
      maCtcbDuyet: json["ma_ctcb_duyet"],
      hienThiVblq: json["hien_thi_vblq"],
      chuoiVbDienTu: json["chuoi_vb_dien_tu"],
      daLuuFile: json["da_luu_file"],
      bussinessDoctype: json["bussiness_doctype"],
      vblqNgoaiHeThong: json["vblq_ngoai_he_thong"],
      ngayDuyet: json["ngay_duyet"],
      r: json["r"],
      totalRow: json["total_row"]);

  Map<String, dynamic> toJson() => {
        "ma_van_ban_den_kc": maVanBanDenKc,
        "loai_vb_dien_tu": loaiVbDienTu,
        "ma_van_ban_kc": maVanBanKc,
        "ma_xu_ly_den": maXuLyDen,
        "so_den": soDen,
        "trich_yeu": trichYeu,
        "so_ky_hieu": soKyHieu,
        "ten_goi_tin_xml": tenGoiTinXml,
        "ngay_nhan": ngayNhan?.toIso8601String(),
        "ngay_xem": ngayXem,
        "ngay_gui": ngayGui,
        "file_van_ban_bs": fileVanBanBs,
        "file_van_ban": fileVanBan,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_cap_do_mat": maCapDoMat,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ten_don_vi_gui": tenDonViGui,
        "ngay_den": ngayDen,
        "ngay_den_order": ngayDenOrder?.toIso8601String(),
        "han_xu_ly": hanXuLy,
        "ngay_ban_hanh": ngayBanHanh,
        "so_ioffice": soIoffice,
        "gui_kem_vb_giay": guiKemVbGiay,
        "so_ban_phat_hanh": soBanPhatHanh,
        "so_trang_vb": soTrangVb,
        "nguoi_ky": nguoiKy,
        "noi_luu_ban_chinh": noiLuuBanChinh,
        "ma_ctcb_duyet": maCtcbDuyet,
        "hien_thi_vblq": hienThiVblq,
        "chuoi_vb_dien_tu": chuoiVbDienTu,
        "da_luu_file": daLuuFile,
        "bussiness_doctype": bussinessDoctype,
        "vblq_ngoai_he_thong": vblqNgoaiHeThong,
        "ngay_duyet": ngayDuyet,
        "r": r,
        "total_row": totalRow
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}
