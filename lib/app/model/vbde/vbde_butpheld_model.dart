// To parse this JSON data, do
//
//     final dsButPheLanhDao = dsButPheLanhDaoFromJson(jsonString);

import 'dart:convert';

List<DsButPheLanhDao> dsButPheLanhDaoFromJson(String str) =>
    List<DsButPheLanhDao>.from(
        json.decode(str).map((x) => DsButPheLanhDao.fromJson(x)));

String dsButPheLanhDaoToJson(List<DsButPheLanhDao> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DsButPheLanhDao {
  dynamic noiDungChuyen;
  String? hoVaTenCanBo;
  String? tenChucVu;
  String? ngayNhan;
  String? ngayNhanOrder;
  String? tenDonVi;
  dynamic tenDonViNhan;
  int? maXuLyDenKc;
  int? butPheCu;
  int? maXuLyDen;
  List<TenNguoiNhan>? tenNguoiNhan;

  DsButPheLanhDao({
    this.noiDungChuyen,
    this.hoVaTenCanBo,
    this.tenChucVu,
    this.ngayNhan,
    this.ngayNhanOrder,
    this.tenDonVi,
    this.tenDonViNhan,
    this.maXuLyDenKc,
    this.butPheCu,
    this.maXuLyDen,
    this.tenNguoiNhan,
  });

  factory DsButPheLanhDao.fromJson(Map<String, dynamic> json) =>
      DsButPheLanhDao(
        noiDungChuyen: json["noi_dung_chuyen"],
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        ngayNhan: json["ngay_nhan"],
        ngayNhanOrder: json["ngay_nhan_order"],
        tenDonVi: json["ten_don_vi"],
        tenDonViNhan: json["ten_don_vi_nhan"],
        maXuLyDenKc: json["ma_xu_ly_den_kc"],
        butPheCu: json["but_phe_cu"],
        maXuLyDen: json["ma_xu_ly_den"],
        tenNguoiNhan: json["ten_nguoi_nhan"] == null
            ? []
            : List<TenNguoiNhan>.from(
                json["ten_nguoi_nhan"]!.map((x) => TenNguoiNhan.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "noi_dung_chuyen": noiDungChuyen,
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "ten_chuc_vu": tenChucVu,
        "ngay_nhan": ngayNhan,
        "ngay_nhan_order": ngayNhanOrder,
        "ten_don_vi": tenDonVi,
        "ten_don_vi_nhan": tenDonViNhan,
        "ma_xu_ly_den_kc": maXuLyDenKc,
        "but_phe_cu": butPheCu,
        "ma_xu_ly_den": maXuLyDen,
        "ten_nguoi_nhan": tenNguoiNhan == null
            ? []
            : List<dynamic>.from(tenNguoiNhan!.map((x) => x.toJson())),
      };
}

class TenNguoiNhan {
  String? tenDonViNhan;
  String? hoVaTenCanBoNhan;
  dynamic maYeuCau;

  TenNguoiNhan({
    this.tenDonViNhan,
    this.hoVaTenCanBoNhan,
    this.maYeuCau,
  });

  factory TenNguoiNhan.fromJson(Map<String, dynamic> json) => TenNguoiNhan(
        tenDonViNhan: json["ten_don_vi_nhan"],
        hoVaTenCanBoNhan: json["ho_va_ten_can_bo_nhan"],
        maYeuCau: json["ma_yeu_cau"],
      );

  Map<String, dynamic> toJson() => {
        "ten_don_vi_nhan": tenDonViNhan,
        "ho_va_ten_can_bo_nhan": hoVaTenCanBoNhan,
        "ma_yeu_cau": maYeuCau,
      };
}
