// To parse this JSON data, do
//
//     final vbdeChiTiet = vbdeChiTietFromJson(jsonString);

import 'dart:convert';

VbdeChiTiet vbdeChiTietFromJson(String str) =>
    VbdeChiTiet.fromJson(json.decode(str));

String vbdeChiTietToJson(VbdeChiTiet data) => json.encode(data.toJson());

class VbdeChiTietVt {
  bool? success;
  String? message;
  VbdeChiTiet? data;
  String? storeName;
  int? storeType;

  VbdeChiTietVt({
    this.success,
    this.message,
    this.data,
    this.storeName,
    this.storeType,
  });

  factory VbdeChiTietVt.fromJson(Map<String, dynamic> json) => VbdeChiTietVt(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null ? null : VbdeChiTiet.fromJson(json["data"]),
        storeName: json["store_name"],
        storeType: json["store_type"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data?.toJson(),
        "store_name": storeName,
        "store_type": storeType,
      };
}

class VbdeChiTiet {
  double? maVanBanKc;
  String? trichYeu;
  String? soKyHieu;
  String? nguoiKy;
  String? ngayBanHanh;
  dynamic maCoQuanBanHanh;
  String? soBanPhatHanh;
  String? fileVanBan;
  DateTime? ngayTao;
  double? maCtcbTao;
  double? maDonViTao;
  double? maDonViQuanTriTao;
  double? loaiVanBanKhiMoiTao;
  double? trangThaiVanBan;
  String? noiLuuBanChinh;
  dynamic nguoiKyVanBan;
  dynamic canBoDuyet;
  dynamic soKyHieuVbDuocPhucDap;
  dynamic ngayBanHanhVbGoc;
  dynamic donViGuiVbGoc;
  dynamic vanBanLienQuan;
  String? tenCoQuanBanHanh;
  double? maGoc;
  double? maVanBanDenKc;
  String? ngayDen;
  double? soDen;
  dynamic soSaoY;
  dynamic soBan;
  DateTime? ngayLuu;
  double? maCtcbLuu;
  dynamic ngayDuyet;
  double? maCtcbDuyet;
  dynamic soNgayXuLyChung;
  dynamic hanXuLyChung;
  dynamic hanXuLyPhatHanh;
  String? fileVanBanBs;
  dynamic noiLuuBanChinhVb;
  dynamic nguoiXuLyChinh;
  double? maDonViQuanTri;
  double? maSoVbDen;
  double? maLinhVucVanBan;
  double? maLoaiVanBan;
  double? maCapDoKhan;
  double? maCapDoMat;
  double? maQuyTrinh;
  double? maVanBanGoc;
  double? trangThaiVanBanDen;
  double? trangThaiXuLy;
  dynamic maVanBanDenCha;
  String? maDinhDanh;
  String? maDinhDanhGui;
  String? tenDonViGui;
  double? maLoaiLienThong;
  double? lanhDaoChuyenVb;
  dynamic canPhucDap;
  double? hienThiVblq;
  double? maXuLyDen;
  DateTime? ngayNhan;
  dynamic hanXuLy;
  dynamic hanXuLyVn;
  DateTime? ngayXem;
  double? trangThaiXuLyDen;
  dynamic butPheCbDuyet;
  double? maCtcbGui;
  String? fileVbGoc;
  dynamic maYeuCau;
  dynamic fileDuThao;
  String? tenCapDoKhan;
  String? tenCapDoMat;
  String? tenLoaiVanBan;
  String? tenLinhVucVanBan;
  dynamic tenYeuCau;
  String? tenNguoiDuyet;
  dynamic fileVanBanLienQuan;
  dynamic chuoiMaVbLienQuan;
  dynamic fileVblqDinhKem;
  String? tenNguoiLuu;
  String? diDongNguoiLuu;
  dynamic emailNguoiLuu;
  String? hoTenNguoiLuu;
  String? diDongNguoiGui;
  dynamic emailNguoiGui;
  String? hoVaTenCanBoGui;
  double? trangThaiRaSoat;
  dynamic noiDungRaSoat;
  String? tenSoVbDen;
  dynamic ghiChu;
  String? maDinhDanhVb;
  dynamic maCongViecChiTiet;
  dynamic maLichCongTac;
  double? maHoSoIgate;
  dynamic maHoSoIgateLgsp;
  dynamic bussinessdoctype;
  dynamic vblqNgoaiHeThong;
  dynamic tenDoKho;
  dynamic maDoKhoCv;

  VbdeChiTiet({
    this.maVanBanKc,
    this.trichYeu,
    this.soKyHieu,
    this.nguoiKy,
    this.ngayBanHanh,
    this.maCoQuanBanHanh,
    this.soBanPhatHanh,
    this.fileVanBan,
    this.ngayTao,
    this.maCtcbTao,
    this.maDonViTao,
    this.maDonViQuanTriTao,
    this.loaiVanBanKhiMoiTao,
    this.trangThaiVanBan,
    this.noiLuuBanChinh,
    this.nguoiKyVanBan,
    this.canBoDuyet,
    this.soKyHieuVbDuocPhucDap,
    this.ngayBanHanhVbGoc,
    this.donViGuiVbGoc,
    this.vanBanLienQuan,
    this.tenCoQuanBanHanh,
    this.maGoc,
    this.maVanBanDenKc,
    this.ngayDen,
    this.soDen,
    this.soSaoY,
    this.soBan,
    this.ngayLuu,
    this.maCtcbLuu,
    this.ngayDuyet,
    this.maCtcbDuyet,
    this.soNgayXuLyChung,
    this.hanXuLyChung,
    this.hanXuLyPhatHanh,
    this.fileVanBanBs,
    this.noiLuuBanChinhVb,
    this.nguoiXuLyChinh,
    this.maDonViQuanTri,
    this.maSoVbDen,
    this.maLinhVucVanBan,
    this.maLoaiVanBan,
    this.maCapDoKhan,
    this.maCapDoMat,
    this.maQuyTrinh,
    this.maVanBanGoc,
    this.trangThaiVanBanDen,
    this.trangThaiXuLy,
    this.maVanBanDenCha,
    this.maDinhDanh,
    this.maDinhDanhGui,
    this.tenDonViGui,
    this.maLoaiLienThong,
    this.lanhDaoChuyenVb,
    this.canPhucDap,
    this.hienThiVblq,
    this.maXuLyDen,
    this.ngayNhan,
    this.hanXuLy,
    this.hanXuLyVn,
    this.ngayXem,
    this.trangThaiXuLyDen,
    this.butPheCbDuyet,
    this.maCtcbGui,
    this.fileVbGoc,
    this.maYeuCau,
    this.fileDuThao,
    this.tenCapDoKhan,
    this.tenCapDoMat,
    this.tenLoaiVanBan,
    this.tenLinhVucVanBan,
    this.tenYeuCau,
    this.tenNguoiDuyet,
    this.fileVanBanLienQuan,
    this.chuoiMaVbLienQuan,
    this.fileVblqDinhKem,
    this.tenNguoiLuu,
    this.diDongNguoiLuu,
    this.emailNguoiLuu,
    this.hoTenNguoiLuu,
    this.diDongNguoiGui,
    this.emailNguoiGui,
    this.hoVaTenCanBoGui,
    this.trangThaiRaSoat,
    this.noiDungRaSoat,
    this.tenSoVbDen,
    this.ghiChu,
    this.maDinhDanhVb,
    this.maCongViecChiTiet,
    this.maLichCongTac,
    this.maHoSoIgate,
    this.maHoSoIgateLgsp,
    this.bussinessdoctype,
    this.vblqNgoaiHeThong,
    this.tenDoKho,
    this.maDoKhoCv,
  });

  factory VbdeChiTiet.fromJson(Map<String, dynamic> json) => VbdeChiTiet(
        maVanBanKc: json["ma_van_ban_kc"],
        trichYeu: json["trich_yeu"],
        soKyHieu: json["so_ky_hieu"],
        nguoiKy: json["nguoi_ky"],
        ngayBanHanh: json["ngay_ban_hanh"],
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        soBanPhatHanh: json["so_ban_phat_hanh"],
        fileVanBan: json["file_van_ban"],
        ngayTao:
            json["ngay_tao"] == null ? null : DateTime.parse(json["ngay_tao"]),
        maCtcbTao: json["ma_ctcb_tao"],
        maDonViTao: json["ma_don_vi_tao"],
        maDonViQuanTriTao: json["ma_don_vi_quan_tri_tao"],
        loaiVanBanKhiMoiTao: json["loai_van_ban_khi_moi_tao"],
        trangThaiVanBan: json["trang_thai_van_ban"],
        noiLuuBanChinh: json["noi_luu_ban_chinh"],
        nguoiKyVanBan: json["nguoi_ky_van_ban"],
        canBoDuyet: json["can_bo_duyet"],
        soKyHieuVbDuocPhucDap: json["so_ky_hieu_vb_duoc_phuc_dap"],
        ngayBanHanhVbGoc: json["ngay_ban_hanh_vb_goc"],
        donViGuiVbGoc: json["don_vi_gui_vb_goc"],
        vanBanLienQuan: json["van_ban_lien_quan"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        maGoc: json["ma_goc"],
        maVanBanDenKc: json["ma_van_ban_den_kc"],
        ngayDen: json["ngay_den"],
        soDen: json["so_den"],
        soSaoY: json["so_sao_y"],
        soBan: json["so_ban"],
        ngayLuu:
            json["ngay_luu"] == null ? null : DateTime.parse(json["ngay_luu"]),
        maCtcbLuu: json["ma_ctcb_luu"],
        ngayDuyet: json["ngay_duyet"],
        maCtcbDuyet: json["ma_ctcb_duyet"],
        soNgayXuLyChung: json["so_ngay_xu_ly_chung"],
        hanXuLyChung: json["han_xu_ly_chung"],
        hanXuLyPhatHanh: json["han_xu_ly_phat_hanh"],
        fileVanBanBs: json["file_van_ban_bs"],
        noiLuuBanChinhVb: json["noi_luu_ban_chinh_vb"],
        nguoiXuLyChinh: json["nguoi_xu_ly_chinh"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        maSoVbDen: json["ma_so_vb_den"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDoKhan: json["ma_cap_do_khan"],
        maCapDoMat: json["ma_cap_do_mat"],
        maQuyTrinh: json["ma_quy_trinh"],
        maVanBanGoc: json["ma_van_ban_goc"],
        trangThaiVanBanDen: json["trang_thai_van_ban_den"],
        trangThaiXuLy: json["trang_thai_xu_ly"],
        maVanBanDenCha: json["ma_van_ban_den_cha"],
        maDinhDanh: json["ma_dinh_danh"],
        maDinhDanhGui: json["ma_dinh_danh_gui"],
        tenDonViGui: json["ten_don_vi_gui"],
        maLoaiLienThong: json["ma_loai_lien_thong"],
        lanhDaoChuyenVb: json["lanh_dao_chuyen_vb"],
        canPhucDap: json["can_phuc_dap"],
        hienThiVblq: json["hien_thi_vblq"],
        maXuLyDen: json["ma_xu_ly_den"],
        ngayNhan: json["ngay_nhan"] == null
            ? null
            : DateTime.parse(json["ngay_nhan"]),
        hanXuLy: json["han_xu_ly"],
        hanXuLyVn: json["han_xu_ly_vn"],
        ngayXem:
            json["ngay_xem"] == null ? null : DateTime.parse(json["ngay_xem"]),
        trangThaiXuLyDen: json["trang_thai_xu_ly_den"],
        butPheCbDuyet: json["but_phe_cb_duyet"],
        maCtcbGui: json["ma_ctcb_gui"],
        fileVbGoc: json["file_vb_goc"],
        maYeuCau: json["ma_yeu_cau"],
        fileDuThao: json["file_du_thao"],
        tenCapDoKhan: json["ten_cap_do_khan"],
        tenCapDoMat: json["ten_cap_do_mat"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        tenLinhVucVanBan: json["ten_linh_vuc_van_ban"],
        tenYeuCau: json["ten_yeu_cau"],
        tenNguoiDuyet: json["ten_nguoi_duyet"],
        fileVanBanLienQuan: json["file_van_ban_lien_quan"],
        chuoiMaVbLienQuan: json["chuoi_ma_vb_lien_quan"],
        fileVblqDinhKem: json["file_vblq_dinh_kem"],
        tenNguoiLuu: json["ten_nguoi_luu"],
        diDongNguoiLuu: json["di_dong_nguoi_luu"],
        emailNguoiLuu: json["email_nguoi_luu"],
        hoTenNguoiLuu: json["ho_ten_nguoi_luu"],
        diDongNguoiGui: json["di_dong_nguoi_gui"],
        emailNguoiGui: json["email_nguoi_gui"],
        hoVaTenCanBoGui: json["ho_va_ten_can_bo_gui"],
        trangThaiRaSoat: json["trang_thai_ra_soat"],
        noiDungRaSoat: json["noi_dung_ra_soat"],
        tenSoVbDen: json["ten_so_vb_den"],
        ghiChu: json["ghi_chu"],
        maDinhDanhVb: json["ma_dinh_danh_vb"],
        maCongViecChiTiet: json["ma_cong_viec_chi_tiet"],
        maLichCongTac: json["ma_lich_cong_tac"],
        maHoSoIgate: json["ma_ho_so_igate"],
        maHoSoIgateLgsp: json["ma_ho_so_igate_lgsp"],
        bussinessdoctype: json["bussinessdoctype"],
        vblqNgoaiHeThong: json["vblq_ngoai_he_thong"],
        tenDoKho: json["ten_do_kho"],
        maDoKhoCv: json["ma_do_kho_cv"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_kc": maVanBanKc,
        "trich_yeu": trichYeu,
        "so_ky_hieu": soKyHieu,
        "nguoi_ky": nguoiKy,
        "ngay_ban_hanh": ngayBanHanh,
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "so_ban_phat_hanh": soBanPhatHanh,
        "file_van_ban": fileVanBan,
        "ngay_tao": ngayTao?.toIso8601String(),
        "ma_ctcb_tao": maCtcbTao,
        "ma_don_vi_tao": maDonViTao,
        "ma_don_vi_quan_tri_tao": maDonViQuanTriTao,
        "loai_van_ban_khi_moi_tao": loaiVanBanKhiMoiTao,
        "trang_thai_van_ban": trangThaiVanBan,
        "noi_luu_ban_chinh": noiLuuBanChinh,
        "nguoi_ky_van_ban": nguoiKyVanBan,
        "can_bo_duyet": canBoDuyet,
        "so_ky_hieu_vb_duoc_phuc_dap": soKyHieuVbDuocPhucDap,
        "ngay_ban_hanh_vb_goc": ngayBanHanhVbGoc,
        "don_vi_gui_vb_goc": donViGuiVbGoc,
        "van_ban_lien_quan": vanBanLienQuan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ma_goc": maGoc,
        "ma_van_ban_den_kc": maVanBanDenKc,
        "ngay_den": ngayDen,
        "so_den": soDen,
        "so_sao_y": soSaoY,
        "so_ban": soBan,
        "ngay_luu": ngayLuu?.toIso8601String(),
        "ma_ctcb_luu": maCtcbLuu,
        "ngay_duyet": ngayDuyet,
        "ma_ctcb_duyet": maCtcbDuyet,
        "so_ngay_xu_ly_chung": soNgayXuLyChung,
        "han_xu_ly_chung": hanXuLyChung,
        "han_xu_ly_phat_hanh": hanXuLyPhatHanh,
        "file_van_ban_bs": fileVanBanBs,
        "noi_luu_ban_chinh_vb": noiLuuBanChinhVb,
        "nguoi_xu_ly_chinh": nguoiXuLyChinh,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": maSoVbDen,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_cap_do_mat": maCapDoMat,
        "ma_quy_trinh": maQuyTrinh,
        "ma_van_ban_goc": maVanBanGoc,
        "trang_thai_van_ban_den": trangThaiVanBanDen,
        "trang_thai_xu_ly": trangThaiXuLy,
        "ma_van_ban_den_cha": maVanBanDenCha,
        "ma_dinh_danh": maDinhDanh,
        "ma_dinh_danh_gui": maDinhDanhGui,
        "ten_don_vi_gui": tenDonViGui,
        "ma_loai_lien_thong": maLoaiLienThong,
        "lanh_dao_chuyen_vb": lanhDaoChuyenVb,
        "can_phuc_dap": canPhucDap,
        "hien_thi_vblq": hienThiVblq,
        "ma_xu_ly_den": maXuLyDen,
        "ngay_nhan": ngayNhan?.toIso8601String(),
        "han_xu_ly": hanXuLy,
        "han_xu_ly_vn": hanXuLyVn,
        "ngay_xem": ngayXem?.toIso8601String(),
        "trang_thai_xu_ly_den": trangThaiXuLyDen,
        "but_phe_cb_duyet": butPheCbDuyet,
        "ma_ctcb_gui": maCtcbGui,
        "file_vb_goc": fileVbGoc,
        "ma_yeu_cau": maYeuCau,
        "file_du_thao": fileDuThao,
        "ten_cap_do_khan": tenCapDoKhan,
        "ten_cap_do_mat": tenCapDoMat,
        "ten_loai_van_ban": tenLoaiVanBan,
        "ten_linh_vuc_van_ban": tenLinhVucVanBan,
        "ten_yeu_cau": tenYeuCau,
        "ten_nguoi_duyet": tenNguoiDuyet,
        "file_van_ban_lien_quan": fileVanBanLienQuan,
        "chuoi_ma_vb_lien_quan": chuoiMaVbLienQuan,
        "file_vblq_dinh_kem": fileVblqDinhKem,
        "ten_nguoi_luu": tenNguoiLuu,
        "di_dong_nguoi_luu": diDongNguoiLuu,
        "email_nguoi_luu": emailNguoiLuu,
        "ho_ten_nguoi_luu": hoTenNguoiLuu,
        "di_dong_nguoi_gui": diDongNguoiGui,
        "email_nguoi_gui": emailNguoiGui,
        "ho_va_ten_can_bo_gui": hoVaTenCanBoGui,
        "trang_thai_ra_soat": trangThaiRaSoat,
        "noi_dung_ra_soat": noiDungRaSoat,
        "ten_so_vb_den": tenSoVbDen,
        "ghi_chu": ghiChu,
        "ma_dinh_danh_vb": maDinhDanhVb,
        "ma_cong_viec_chi_tiet": maCongViecChiTiet,
        "ma_lich_cong_tac": maLichCongTac,
        "ma_ho_so_igate": maHoSoIgate,
        "ma_ho_so_igate_lgsp": maHoSoIgateLgsp,
        "bussinessdoctype": bussinessdoctype,
        "vblq_ngoai_he_thong": vblqNgoaiHeThong,
        "ten_do_kho": tenDoKho,
        "ma_do_kho_cv": maDoKhoCv,
      };
}
