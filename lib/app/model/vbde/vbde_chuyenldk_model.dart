// To parse this JSON data, do
//
//     final chuyenLdKhac = chuyenLdKhacFrom<PERSON>son(jsonString);

import 'dart:convert';

ChuyenLdKhac chuyenLdKhacFromJson(String str) =>
    ChuyenLdKhac.fromJson(json.decode(str));

String chuyenLdKhac<PERSON>(ChuyenLdKhac data) => json.encode(data.toJson());

class ChuyenLdKhac {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<LanhDaoKhac>? data;
  Param? param;

  ChuyenLdKhac({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
    this.param,
  });

  factory ChuyenLdKhac.fromJson(Map<String, dynamic> json) => ChuyenLdKhac(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<LanhDaoKhac>.from(
                json["data"]!.map((x) => LanhDaoKhac.fromJson(x))),
        param: json["param"] == null ? null : Param.fromJson(json["param"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "param": param?.toJson(),
      };
}

class LanhDaoKhac {
  double? ma;
  double? maCanBoKc;
  String? ten;
  String? tenDonVi;
  String? diDongCanBo;
  String? tenChucVu;
  String? emailCanBo;
  double? sttCanBo;
  double? laMacDinh;

  LanhDaoKhac({
    this.ma,
    this.maCanBoKc,
    this.ten,
    this.tenDonVi,
    this.diDongCanBo,
    this.tenChucVu,
    this.emailCanBo,
    this.sttCanBo,
    this.laMacDinh,
  });

  factory LanhDaoKhac.fromJson(Map<String, dynamic> json) => LanhDaoKhac(
        ma: json["ma"],
        maCanBoKc: json["ma_can_bo_kc"],
        ten: json["ten"],
        tenDonVi: json["ten_don_vi"],
        diDongCanBo: json["di_dong_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        emailCanBo: json["email_can_bo"],
        sttCanBo: json["stt_can_bo"],
        laMacDinh: json["la_mac_dinh"],
      );

  Map<String, dynamic> toJson() => {
        "ma": ma,
        "ma_can_bo_kc": maCanBoKc,
        "ten": ten,
        "ten_don_vi": tenDonVi,
        "di_dong_can_bo": diDongCanBo,
        "ten_chuc_vu": tenChucVu,
        "email_can_bo": emailCanBo,
        "stt_can_bo": sttCanBo,
        "la_mac_dinh": laMacDinh,
      };
}

class Param {
  List<String>? parameternames;

  Param({
    this.parameternames,
  });

  factory Param.fromJson(Map<String, dynamic> json) => Param(
        parameternames: json["parameternames"] == null
            ? []
            : List<String>.from(json["parameternames"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "parameternames": parameternames == null
            ? []
            : List<dynamic>.from(parameternames!.map((x) => x)),
      };
}

class ResponseCommon {
  int? id;
  String? message;
  String? store_name;
  int? store_type;
  int? huy_duoc;
  dynamic data;

  ResponseCommon(
      {this.id,
      this.message,
      this.store_name,
      this.store_type,
      this.huy_duoc,
      this.data});

  factory ResponseCommon.fromJson(Map<String, dynamic> json) => ResponseCommon(
      id: json["id"],
      message: json["message"],
      store_name: json["store_name"],
      store_type: json["store_type"],
      huy_duoc: json["huy_duoc"],
      data: json["data"] == null ? [] : dynamic);
}

class ResponseLdXuLy {
  int? maVanBanDenKc;
  String? chuoiMaCtcbNhan;

  ResponseLdXuLy({
    this.maVanBanDenKc,
    this.chuoiMaCtcbNhan,
  });
  factory ResponseLdXuLy.fromJson(Map<String, dynamic> json) => ResponseLdXuLy(
        maVanBanDenKc: json["ma_van_ban_den_kc"],
        chuoiMaCtcbNhan: json["chuoi_ma_ctcb_nhan"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_den_kc": maVanBanDenKc,
        "chuoi_ma_ctcb_nhan": chuoiMaCtcbNhan,
      };
}
