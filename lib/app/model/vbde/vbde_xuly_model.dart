class ResponeHtVbde {
  int? id;
  dynamic message;
  dynamic storeName;
  int? storeType;
  dynamic param;

  ResponeHtVbde({
    this.id,
    this.message,
    this.storeName,
    this.storeType,
    this.param,
  });

  factory ResponeHtVbde.fromJson(Map<String, dynamic> json) => ResponeHtVbde(
        id: json["id"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        param: json["param"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "param": param,
      };
}

class DetailCBTree {
  String? ma;
  String? ten;
  String? tenChucVu;
  String? diDongCanBo;
  String? maYeuCau;
  String? vaiTroChuyen;

  DetailCBTree(
    this.ma,
    this.ten,
    this.tenChucVu,
    this.diDongCanBo,
    this.maYeuCau,
    this.vaiTroChuyen,
  );
}
