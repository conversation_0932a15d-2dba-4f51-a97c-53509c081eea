// To parse this JSON data, do
//
//     final dsLanhDaoDuyet = dsLanhDaoDuyetFromJson(jsonString);

import 'dart:convert';

DsLanhDaoDuyet dsLanhDaoDuyetFromJson(String str) =>
    DsLanhDaoDuyet.fromJson(json.decode(str));

String dsLanhDaoDuyetToJson(DsLanhDaoDuyet data) => json.encode(data.toJson());

class DsLanhDaoDuyet {
  int totalPage;
  int totalRow;
  bool success;
  String message;
  String storeName;
  int storeType;
  List<ChoDuyet> data;
  DsLanhDaoDuyet({
    required this.totalPage,
    required this.totalRow,
    required this.success,
    required this.message,
    required this.storeName,
    required this.storeType,
    required this.data,
  });

  factory DsLanhDaoDuyet.fromJson(Map<String, dynamic> json) => DsLanhDaoDuyet(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data:
            List<ChoDuyet>.from(json["data"].map((x) => ChoDuyet.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

class ChoDuyet {
  double? maVanBanKc;
  String? trichYeu;
  String? soKyHieu;
  String? nguoiKy;
  String? ngayBanHanh;
  double? maCoQuanBanHanh;
  String? soBanPhatHanh;
  String? fileVanBan;
  String? ngayTao;
  double? maCtcbTao;
  double? maDonViTao;
  double? maDonViQuanTriTao;
  double? loaiVanBanKhiMoiTao;
  double? trangThaiVanBan;
  String? noiLuuBanChinh;
  String? nguoiKyVanBan;
  String? canBoDuyet;
  String? soKyHieuVbDuocPhucDap;
  String? ngayBanHanhVbGoc;
  String? donViGuiVbGoc;
  String? vanBanLienQuan;
  String? tenCoQuanBanHanh;
  double? maVanBanDenKc;
  String? ngayDen;
  String? ngayDenOrder;
  double? soDen;
  String? soSaoY;
  String? soBan;
  String? ngayLuu;
  double? maCtcbLuu;
  String? ngayDuyet;
  double? maCtcbDuyet;
  String? soNgayXuLyChung;
  String? hanXuLyChung;
  String? fileVanBanBs;
  String? ghiChu;
  String? noiLuuBanChinhVb;
  String? nguoiXuLyChinh;
  double? maDonViQuanTri;
  double? maSoVbDen;
  double? maLinhVucVanBan;
  double? maLoaiVanBan;
  double? maCapDoKhan;
  double? maCapDoMat;
  double? maQuyTrinh;
  double? maVanBanGoc;
  double? trangThaiVanBanDen;
  double? trangThaiXuLy;
  double? maVanBanDenCha;
  String? maDinhDanh;
  String? maDinhDanhGui;
  String? tenDonViGui;
  double? maLoaiLienThong;
  double? maHoSoIgate;
  double? soUuTien;
  double? maXuLyDen;
  String? ngayNhan;
  String? hanXuLy;
  String? ngayXem;
  double? trangThaiXuLyDen;
  dynamic butPheCbDuyet;
  String? soIoffice;
  double? trangThaiRaSoat;
  double? r;

  ChoDuyet({
    this.maVanBanKc,
    this.trichYeu,
    this.soKyHieu,
    this.nguoiKy,
    this.ngayBanHanh,
    this.maCoQuanBanHanh,
    this.soBanPhatHanh,
    this.fileVanBan,
    this.ngayTao,
    this.maCtcbTao,
    this.maDonViTao,
    this.maDonViQuanTriTao,
    this.loaiVanBanKhiMoiTao,
    this.trangThaiVanBan,
    this.noiLuuBanChinh,
    this.nguoiKyVanBan,
    this.canBoDuyet,
    this.soKyHieuVbDuocPhucDap,
    this.ngayBanHanhVbGoc,
    this.donViGuiVbGoc,
    this.vanBanLienQuan,
    this.tenCoQuanBanHanh,
    this.maVanBanDenKc,
    this.ngayDen,
    this.ngayDenOrder,
    this.soDen,
    this.soSaoY,
    this.soBan,
    this.ngayLuu,
    this.maCtcbLuu,
    this.ngayDuyet,
    this.maCtcbDuyet,
    this.soNgayXuLyChung,
    this.hanXuLyChung,
    this.fileVanBanBs,
    this.ghiChu,
    this.noiLuuBanChinhVb,
    this.nguoiXuLyChinh,
    this.maDonViQuanTri,
    this.maSoVbDen,
    this.maLinhVucVanBan,
    this.maLoaiVanBan,
    this.maCapDoKhan,
    this.maCapDoMat,
    this.maQuyTrinh,
    this.maVanBanGoc,
    this.trangThaiVanBanDen,
    this.trangThaiXuLy,
    this.maVanBanDenCha,
    this.maDinhDanh,
    this.maDinhDanhGui,
    this.tenDonViGui,
    this.maLoaiLienThong,
    this.maHoSoIgate,
    this.soUuTien,
    this.maXuLyDen,
    this.ngayNhan,
    this.hanXuLy,
    this.ngayXem,
    this.trangThaiXuLyDen,
    this.butPheCbDuyet,
    this.soIoffice,
    this.trangThaiRaSoat,
    this.r,
  });

  factory ChoDuyet.fromJson(Map<String, dynamic> json) => ChoDuyet(
        maVanBanKc: json["ma_van_ban_kc"],
        trichYeu: json["trich_yeu"],
        soKyHieu: json["so_ky_hieu"],
        nguoiKy: json["nguoi_ky"],
        ngayBanHanh: json["ngay_ban_hanh"],
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        soBanPhatHanh: json["so_ban_phat_hanh"],
        fileVanBan: json["file_van_ban"],
        ngayTao: json["ngay_tao"],
        maCtcbTao: json["ma_ctcb_tao"],
        maDonViTao: json["ma_don_vi_tao"],
        maDonViQuanTriTao: json["ma_don_vi_quan_tri_tao"],
        loaiVanBanKhiMoiTao: json["loai_van_ban_khi_moi_tao"],
        trangThaiVanBan: json["trang_thai_van_ban"],
        noiLuuBanChinh: json["noi_luu_ban_chinh"],
        nguoiKyVanBan: json["nguoi_ky_van_ban"],
        canBoDuyet: json["can_bo_duyet"],
        soKyHieuVbDuocPhucDap: json["so_ky_hieu_vb_duoc_phuc_dap"],
        ngayBanHanhVbGoc: json["ngay_ban_hanh_vb_goc"],
        donViGuiVbGoc: json["don_vi_gui_vb_goc"],
        vanBanLienQuan: json["van_ban_lien_quan"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        maVanBanDenKc: json["ma_van_ban_den_kc"],
        ngayDen: json["ngay_den"],
        ngayDenOrder: json["ngay_den_order"],
        soDen: json["so_den"],
        soSaoY: json["so_sao_y"],
        soBan: json["so_ban"],
        ngayLuu: json["ngay_luu"],
        maCtcbLuu: json["ma_ctcb_luu"],
        ngayDuyet: json["ngay_duyet"],
        maCtcbDuyet: json["ma_ctcb_duyet"],
        soNgayXuLyChung: json["so_ngay_xu_ly_chung"],
        hanXuLyChung: json["han_xu_ly_chung"],
        fileVanBanBs: json["file_van_ban_bs"],
        ghiChu: json["ghi_chu"],
        noiLuuBanChinhVb: json["noi_luu_ban_chinh_vb"],
        nguoiXuLyChinh: json["nguoi_xu_ly_chinh"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        maSoVbDen: json["ma_so_vb_den"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDoKhan: json["ma_cap_do_khan"],
        maCapDoMat: json["ma_cap_do_mat"],
        maQuyTrinh: json["ma_quy_trinh"],
        maVanBanGoc: json["ma_van_ban_goc"],
        trangThaiVanBanDen: json["trang_thai_van_ban_den"],
        trangThaiXuLy: json["trang_thai_xu_ly"],
        maVanBanDenCha: json["ma_van_ban_den_cha"],
        maDinhDanh: json["ma_dinh_danh"],
        maDinhDanhGui: json["ma_dinh_danh_gui"],
        tenDonViGui: json["ten_don_vi_gui"],
        maLoaiLienThong: json["ma_loai_lien_thong"],
        maHoSoIgate: json["ma_ho_so_igate"],
        soUuTien: json["so_uu_tien"],
        maXuLyDen: json["ma_xu_ly_den"],
        ngayNhan: json["ngay_nhan"],
        hanXuLy: json["han_xu_ly"],
        ngayXem: json["ngay_xem"],
        trangThaiXuLyDen: json["trang_thai_xu_ly_den"],
        butPheCbDuyet: json["but_phe_cb_duyet"],
        soIoffice: json["so_ioffice"],
        trangThaiRaSoat: json["trang_thai_ra_soat"],
        r: json["r"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_kc": maVanBanKc,
        "trich_yeu": trichYeu,
        "so_ky_hieu": soKyHieu,
        "nguoi_ky": nguoiKy,
        "ngay_ban_hanh": ngayBanHanh,
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "so_ban_phat_hanh": soBanPhatHanh,
        "file_van_ban": fileVanBan,
        "ngay_tao": ngayTao,
        "ma_ctcb_tao": maCtcbTao,
        "ma_don_vi_tao": maDonViTao,
        "ma_don_vi_quan_tri_tao": maDonViQuanTriTao,
        "loai_van_ban_khi_moi_tao": loaiVanBanKhiMoiTao,
        "trang_thai_van_ban": trangThaiVanBan,
        "noi_luu_ban_chinh": noiLuuBanChinh,
        "nguoi_ky_van_ban": nguoiKyVanBan,
        "can_bo_duyet": canBoDuyet,
        "so_ky_hieu_vb_duoc_phuc_dap": soKyHieuVbDuocPhucDap,
        "ngay_ban_hanh_vb_goc": ngayBanHanhVbGoc,
        "don_vi_gui_vb_goc": donViGuiVbGoc,
        "van_ban_lien_quan": vanBanLienQuan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ma_van_ban_den_kc": maVanBanDenKc,
        "ngay_den": ngayDen,
        "ngay_den_order": ngayDenOrder,
        "so_den": soDen,
        "so_sao_y": soSaoY,
        "so_ban": soBan,
        "ngay_luu": ngayLuu,
        "ma_ctcb_luu": maCtcbLuu,
        "ngay_duyet": ngayDuyet,
        "ma_ctcb_duyet": maCtcbDuyet,
        "so_ngay_xu_ly_chung": soNgayXuLyChung,
        "han_xu_ly_chung": hanXuLyChung,
        "file_van_ban_bs": fileVanBanBs,
        "ghi_chu": ghiChu,
        "noi_luu_ban_chinh_vb": noiLuuBanChinhVb,
        "nguoi_xu_ly_chinh": nguoiXuLyChinh,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_so_vb_den": maSoVbDen,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_cap_do_mat": maCapDoMat,
        "ma_quy_trinh": maQuyTrinh,
        "ma_van_ban_goc": maVanBanGoc,
        "trang_thai_van_ban_den": trangThaiVanBanDen,
        "trang_thai_xu_ly": trangThaiXuLy,
        "ma_van_ban_den_cha": maVanBanDenCha,
        "ma_dinh_danh": maDinhDanh,
        "ma_dinh_danh_gui": maDinhDanhGui,
        "ten_don_vi_gui": tenDonViGui,
        "ma_loai_lien_thong": maLoaiLienThong,
        "ma_ho_so_igate": maHoSoIgate,
        "so_uu_tien": soUuTien,
        "ma_xu_ly_den": maXuLyDen,
        "ngay_nhan": ngayNhan,
        "han_xu_ly": hanXuLy,
        "ngay_xem": ngayXem,
        "trang_thai_xu_ly_den": trangThaiXuLyDen,
        "but_phe_cb_duyet": butPheCbDuyet,
        "so_ioffice": soIoffice,
        "trang_thai_ra_soat": trangThaiRaSoat,
        "r": r,
      };
}
