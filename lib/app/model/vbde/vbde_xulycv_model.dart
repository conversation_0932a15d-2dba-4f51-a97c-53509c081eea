// To parse this JSON data, do
//
//     final dsChuyenVienXuLy = dsChuyenVienXuLyFromJson(jsonString);

import 'dart:convert';

DsChuyenVienXuLy dsChuyenVienXuLyFromJson(String str) =>
    DsChuyenVienXuLy.fromJson(json.decode(str));

String dsChuyenVienXuLyToJson(DsChuyenVienXuLy data) =>
    json.encode(data.toJson());

class DsChuyenVienXuLy {
  int? totalPage;
  int? totalRow;
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailDsXlCv>? data;

  DsChuyenVienXuLy({
    this.totalPage,
    this.totalRow,
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsChuyenVienXuLy.fromJson(Map<String, dynamic> json) =>
      DsChuyenVienXuLy(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DetailDsXlCv>.from(
                json["data"]!.map((x) => DetailDsXlCv.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DetailDsXlCv {
  double? maVanBanKc;
  String? trichYeu;
  String? soKyHieu;
  String? nguoiKy;
  String? ngayBanHanh;
  String? fileVanBan;
  double? maCtcbTao;
  double? trangThaiVanBan;
  String? tenCoQuanBanHanh;
  double? maVanBanDenKc;
  String? ngayDen;
  double? soDen;
  String? ngayLuu;
  double? maCtcbLuu;
  String? hanXuLyChung;
  String? fileVanBanBs;
  String? nguoiXuLyChinh;
  double? maDonViQuanTri;
  double? maCapDoKhan;
  double? maVanBanGoc;
  double? trangThaiVanBanDen;
  double? trangThaiXuLy;
  double? maHoSoIgate;
  String? ngayPhucDap;
  dynamic canPhucDap;
  double? maXuLyDen;
  String? ngayNhan;
  double? maCtcbGui;
  String? diDongNguoiGui;
  String? ngayXuLy;
  dynamic hanXuLy;
  double? statusHanXuLy;
  DateTime? ngayXem;
  double? trangThaiXuLyDen;
  String? butPheCbDuyet;
  double? maYeuCau;
  String? fileDinhKem;
  double? maLoaiLienThong;
  String? soIoffice;
  String? tenYeuCau;
  double? yKien;
  double? r;

  DetailDsXlCv({
    this.maVanBanKc,
    this.trichYeu,
    this.soKyHieu,
    this.nguoiKy,
    this.ngayBanHanh,
    this.fileVanBan,
    this.maCtcbTao,
    this.trangThaiVanBan,
    this.tenCoQuanBanHanh,
    this.maVanBanDenKc,
    this.ngayDen,
    this.soDen,
    this.ngayLuu,
    this.maCtcbLuu,
    this.hanXuLyChung,
    this.fileVanBanBs,
    this.nguoiXuLyChinh,
    this.maDonViQuanTri,
    this.maCapDoKhan,
    this.maVanBanGoc,
    this.trangThaiVanBanDen,
    this.trangThaiXuLy,
    this.maHoSoIgate,
    this.ngayPhucDap,
    this.canPhucDap,
    this.maXuLyDen,
    this.ngayNhan,
    this.maCtcbGui,
    this.diDongNguoiGui,
    this.ngayXuLy,
    this.hanXuLy,
    this.statusHanXuLy,
    this.ngayXem,
    this.trangThaiXuLyDen,
    this.butPheCbDuyet,
    this.maYeuCau,
    this.fileDinhKem,
    this.maLoaiLienThong,
    this.soIoffice,
    this.tenYeuCau,
    this.yKien,
    this.r,
  });

  factory DetailDsXlCv.fromJson(Map<String, dynamic> json) => DetailDsXlCv(
        maVanBanKc: json["ma_van_ban_kc"],
        trichYeu: json["trich_yeu"],
        soKyHieu: json["so_ky_hieu"],
        nguoiKy: json["nguoi_ky"],
        ngayBanHanh:
            json["ngay_ban_hanh"] == null ? null : json["ngay_ban_hanh"],
        fileVanBan: json["file_van_ban"],
        maCtcbTao: json["ma_ctcb_tao"],
        trangThaiVanBan: json["trang_thai_van_ban"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        maVanBanDenKc: json["ma_van_ban_den_kc"],
        ngayDen: json["ngay_den"],
        soDen: json["so_den"],
        ngayLuu: json["ngay_luu"] == null ? null : json["ngay_luu"],
        maCtcbLuu: json["ma_ctcb_luu"],
        hanXuLyChung: json["han_xu_ly_chung"],
        fileVanBanBs: json["file_van_ban_bs"],
        nguoiXuLyChinh: json["nguoi_xu_ly_chinh"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        maCapDoKhan: json["ma_cap_do_khan"],
        maVanBanGoc: json["ma_van_ban_goc"],
        trangThaiVanBanDen: json["trang_thai_van_ban_den"],
        trangThaiXuLy: json["trang_thai_xu_ly"],
        maHoSoIgate: json["ma_ho_so_igate"],
        ngayPhucDap:
            json["ngay_phuc_dap"] == null ? null : json["ngay_phuc_dap"],
        canPhucDap: json["can_phuc_dap"],
        maXuLyDen: json["ma_xu_ly_den"],
        ngayNhan: json["ngay_nhan"] == null ? null : json["ngay_nhan"],
        maCtcbGui: json["ma_ctcb_gui"],
        diDongNguoiGui: json["di_dong_nguoi_gui"],
        ngayXuLy: json["ngay_xu_ly"] == null ? null : json["ngay_xu_ly"],
        hanXuLy: json["han_xu_ly"],
        statusHanXuLy: json["status_han_xu_ly"],
        ngayXem:
            json["ngay_xem"] == null ? null : DateTime.parse(json["ngay_xem"]),
        trangThaiXuLyDen: json["trang_thai_xu_ly_den"],
        butPheCbDuyet: json["but_phe_cb_duyet"],
        maYeuCau: json["ma_yeu_cau"],
        fileDinhKem: json["file_dinh_kem"],
        maLoaiLienThong: json["ma_loai_lien_thong"],
        soIoffice: json["so_ioffice"],
        tenYeuCau: json["ten_yeu_cau"],
        yKien: json["y_kien"],
        r: json["r"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_kc": maVanBanKc,
        "trich_yeu": trichYeu,
        "so_ky_hieu": soKyHieu,
        "nguoi_ky": nguoiKy,
        "ngay_ban_hanh": ngayBanHanh,
        "file_van_ban": fileVanBan,
        "ma_ctcb_tao": maCtcbTao,
        "trang_thai_van_ban": trangThaiVanBan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ma_van_ban_den_kc": maVanBanDenKc,
        "ngay_den": ngayDen,
        "so_den": soDen,
        "ngay_luu": ngayLuu,
        "ma_ctcb_luu": maCtcbLuu,
        "han_xu_ly_chung": hanXuLyChung,
        "file_van_ban_bs": fileVanBanBs,
        "nguoi_xu_ly_chinh": nguoiXuLyChinh,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_van_ban_goc": maVanBanGoc,
        "trang_thai_van_ban_den": trangThaiVanBanDen,
        "trang_thai_xu_ly": trangThaiXuLy,
        "ma_ho_so_igate": maHoSoIgate,
        "ngay_phuc_dap": ngayPhucDap,
        "can_phuc_dap": canPhucDap,
        "ma_xu_ly_den": maXuLyDen,
        "ngay_nhan": ngayNhan,
        "ma_ctcb_gui": maCtcbGui,
        "di_dong_nguoi_gui": diDongNguoiGui,
        "ngay_xu_ly": ngayXuLy,
        "han_xu_ly": hanXuLy,
        "status_han_xu_ly": statusHanXuLy,
        "ngay_xem": ngayXem,
        "trang_thai_xu_ly_den": trangThaiXuLyDen,
        "but_phe_cb_duyet": butPheCbDuyet,
        "ma_yeu_cau": maYeuCau,
        "file_dinh_kem": fileDinhKem,
        "ma_loai_lien_thong": maLoaiLienThong,
        "so_ioffice": soIoffice,
        "ten_yeu_cau": tenYeuCau,
        "y_kien": yKien,
        "r": r,
      };
}
