class ModeldsCoQuan {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailDsCoQuanLct>? data;
  Param? param;

  ModeldsCoQuan(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  ModeldsCoQuan.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailDsCoQuanLct>[];
      json['data'].forEach((v) {
        data!.add(new DetailDsCoQuanLct.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailDsCoQuanLct {
  double? maDonViKc;
  String? tenDonVi;
  dynamic domainDonVi;
  dynamic diaChiDonVi;
  dynamic emailDonVi;
  dynamic sdtDonVi;
  double? sttDonVi;
  double? trangThaiDonVi;
  double? maLoaiDonVi;
  double? maDonViCha;
  double? soDenHienTai;
  double? soDiHienTai;
  double? coVanThu;
  double? maDonViCu;
  double? maToCu;
  String? domainFile;
  dynamic maDinhDanhCu;
  String? maSoDv;
  dynamic sdtHoTro;
  double? fontSizeTendv;
  String? logoDonVi;
  dynamic maDonViKhung;
  dynamic domainKhung;
  dynamic skinDonVi;
  dynamic tptkMa;
  dynamic maDonViChaCu;
  dynamic domainSsoSoft;
  dynamic tenDvLogin;
  dynamic maDonViElearning;
  dynamic domainSsoVp;
  dynamic ngayTao;
  String? ngayCapNhat;
  dynamic domainSsoYbi;
  dynamic maDonViChaTd;
  double? vaoThangTrangSso;
  String? tenVietTat;
  double? khongPhatHanh;
  dynamic dvTrongTinhVpc;
  String? tenDonViCha;
  double? stt;
  String? maDinhDanh;

  DetailDsCoQuanLct(
      {this.maDonViKc,
      this.tenDonVi,
      this.domainDonVi,
      this.diaChiDonVi,
      this.emailDonVi,
      this.sdtDonVi,
      this.sttDonVi,
      this.trangThaiDonVi,
      this.maLoaiDonVi,
      this.maDonViCha,
      this.soDenHienTai,
      this.soDiHienTai,
      this.coVanThu,
      this.maDonViCu,
      this.maToCu,
      this.domainFile,
      this.maDinhDanhCu,
      this.maSoDv,
      this.sdtHoTro,
      this.fontSizeTendv,
      this.logoDonVi,
      this.maDonViKhung,
      this.domainKhung,
      this.skinDonVi,
      this.tptkMa,
      this.maDonViChaCu,
      this.domainSsoSoft,
      this.tenDvLogin,
      this.maDonViElearning,
      this.domainSsoVp,
      this.ngayTao,
      this.ngayCapNhat,
      this.domainSsoYbi,
      this.maDonViChaTd,
      this.vaoThangTrangSso,
      this.tenVietTat,
      this.khongPhatHanh,
      this.dvTrongTinhVpc,
      this.tenDonViCha,
      this.stt,
      this.maDinhDanh});

  DetailDsCoQuanLct.fromJson(Map<String, dynamic> json) {
    maDonViKc = json['ma_don_vi_kc'];
    tenDonVi = json['ten_don_vi'];
    domainDonVi = json['domain_don_vi'];
    diaChiDonVi = json['dia_chi_don_vi'];
    emailDonVi = json['email_don_vi'];
    sdtDonVi = json['sdt_don_vi'];
    sttDonVi = json['stt_don_vi'];
    trangThaiDonVi = json['trang_thai_don_vi'];
    maLoaiDonVi = json['ma_loai_don_vi'];
    maDonViCha = json['ma_don_vi_cha'];
    soDenHienTai = json['so_den_hien_tai'];
    soDiHienTai = json['so_di_hien_tai'];
    coVanThu = json['co_van_thu'];
    maDonViCu = json['ma_don_vi_cu'];
    maToCu = json['ma_to_cu'];
    domainFile = json['domain_file'];
    maDinhDanhCu = json['ma_dinh_danh_cu'];
    maSoDv = json['ma_so_dv'];
    sdtHoTro = json['sdt_ho_tro'];
    fontSizeTendv = json['font_size_tendv'];
    logoDonVi = json['logo_don_vi'];
    maDonViKhung = json['ma_don_vi_khung'];
    domainKhung = json['domain_khung'];
    skinDonVi = json['skin_don_vi'];
    tptkMa = json['tptk_ma'];
    maDonViChaCu = json['ma_don_vi_cha_cu'];
    domainSsoSoft = json['domain_sso_soft'];
    tenDvLogin = json['ten_dv_login'];
    maDonViElearning = json['ma_don_vi_elearning'];
    domainSsoVp = json['domain_sso_vp'];
    ngayTao = json['ngay_tao'];
    ngayCapNhat = json['ngay_cap_nhat'];
    domainSsoYbi = json['domain_sso_ybi'];
    maDonViChaTd = json['ma_don_vi_cha_td'];
    vaoThangTrangSso = json['vao_thang_trang_sso'];
    tenVietTat = json['ten_viet_tat'];
    khongPhatHanh = json['khong_phat_hanh'];
    dvTrongTinhVpc = json['dv_trong_tinh_vpc'];
    tenDonViCha = json['ten_don_vi_cha'];
    stt = json['stt'];
    maDinhDanh = json['ma_dinh_danh'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_don_vi_kc'] = this.maDonViKc;
    data['ten_don_vi'] = this.tenDonVi;
    data['domain_don_vi'] = this.domainDonVi;
    data['dia_chi_don_vi'] = this.diaChiDonVi;
    data['email_don_vi'] = this.emailDonVi;
    data['sdt_don_vi'] = this.sdtDonVi;
    data['stt_don_vi'] = this.sttDonVi;
    data['trang_thai_don_vi'] = this.trangThaiDonVi;
    data['ma_loai_don_vi'] = this.maLoaiDonVi;
    data['ma_don_vi_cha'] = this.maDonViCha;
    data['so_den_hien_tai'] = this.soDenHienTai;
    data['so_di_hien_tai'] = this.soDiHienTai;
    data['co_van_thu'] = this.coVanThu;
    data['ma_don_vi_cu'] = this.maDonViCu;
    data['ma_to_cu'] = this.maToCu;
    data['domain_file'] = this.domainFile;
    data['ma_dinh_danh_cu'] = this.maDinhDanhCu;
    data['ma_so_dv'] = this.maSoDv;
    data['sdt_ho_tro'] = this.sdtHoTro;
    data['font_size_tendv'] = this.fontSizeTendv;
    data['logo_don_vi'] = this.logoDonVi;
    data['ma_don_vi_khung'] = this.maDonViKhung;
    data['domain_khung'] = this.domainKhung;
    data['skin_don_vi'] = this.skinDonVi;
    data['tptk_ma'] = this.tptkMa;
    data['ma_don_vi_cha_cu'] = this.maDonViChaCu;
    data['domain_sso_soft'] = this.domainSsoSoft;
    data['ten_dv_login'] = this.tenDvLogin;
    data['ma_don_vi_elearning'] = this.maDonViElearning;
    data['domain_sso_vp'] = this.domainSsoVp;
    data['ngay_tao'] = this.ngayTao;
    data['ngay_cap_nhat'] = this.ngayCapNhat;
    data['domain_sso_ybi'] = this.domainSsoYbi;
    data['ma_don_vi_cha_td'] = this.maDonViChaTd;
    data['vao_thang_trang_sso'] = this.vaoThangTrangSso;
    data['ten_viet_tat'] = this.tenVietTat;
    data['khong_phat_hanh'] = this.khongPhatHanh;
    data['dv_trong_tinh_vpc'] = this.dvTrongTinhVpc;
    data['ten_don_vi_cha'] = this.tenDonViCha;
    data['stt'] = this.stt;
    data['ma_dinh_danh'] = this.maDinhDanh;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
