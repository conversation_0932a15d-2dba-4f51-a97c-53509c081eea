class ModelLCT {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailDsDonViLCT>? data;
  Param? param;

  ModelLCT(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  ModelLCT.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailDsDonViLCT>[];
      json['data'].forEach((v) {
        data!.add(new DetailDsDonViLCT.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailDsDonViLCT {
  double? maDonViKc;
  String? tenDonVi;
  double? sttDonVi;
  String? tenDonViCha;

  DetailDsDonViLCT(
      {this.maDonViKc, this.tenDonVi, this.sttDonVi, this.tenDonViCha});

  DetailDsDonViLCT.fromJson(Map<String, dynamic> json) {
    maDonViKc = json['ma_don_vi_kc'];
    tenDonVi = json['ten_don_vi'];
    sttDonVi = json['stt_don_vi'];
    tenDonViCha = json['ten_don_vi_cha'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_don_vi_kc'] = this.maDonViKc;
    data['ten_don_vi'] = this.tenDonVi;
    data['stt_don_vi'] = this.sttDonVi;
    data['ten_don_vi_cha'] = this.tenDonViCha;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
