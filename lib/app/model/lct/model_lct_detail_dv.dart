class ModelDetailsLctDonvi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<LctDetaiThuDonVi>? data;
  Param? param;

  ModelDetailsLctDonvi(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  ModelDetailsLctDonvi.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <LctDetaiThuDonVi>[];
      json['data'].forEach((v) {
        data!.add(new LctDetaiThuDonVi.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class LctDetaiThuDonVi {
  int? maCongViecChiTietKc;
  int? maLichCongTacChiTiet;
  String? gioThucHien;
  String? noiDung;
  String? fileLct;
  String? fileTaiLieu;
  String? diaDiem;
  String? thanhPhanThamDu;
  int? trangThaiCvct;
  int? daDuyet;
  int? maLichCongTacChiTietKc;
  int? maLichCongTac;
  int? maMocThoiGian;
  String? ngayThucHien;
  int? trangThai;
  int? maMocThoiGianKc;
  int? buoi;
  String? gioBd;
  String? gioKt;
  String? ghiChuMocThoiGian;
  int? macDinh;
  int? maDonVi;
  int? sttMocThoiGian;
  int? trangThaiMocThoiGian;
  String? ngayThucHienVn;
  int? soCongViecMocThoiGian;
  int? soCongViecNgay;
  dynamic giaTriCot1;
  dynamic giaTriCot2;
  dynamic giaTriCot3;
  dynamic giaTriCot4;
  dynamic giaTriCot5;
  dynamic giaTriCot6;
  dynamic giaTriCot7;
  dynamic giaTriCot8;
  dynamic giaTriCot9;
  dynamic maVanBanDen;

  LctDetaiThuDonVi(
      {this.maCongViecChiTietKc,
      this.maLichCongTacChiTiet,
      this.gioThucHien,
      this.noiDung,
      this.fileLct,
      this.fileTaiLieu,
      this.diaDiem,
      this.thanhPhanThamDu,
      this.trangThaiCvct,
      this.daDuyet,
      this.maLichCongTacChiTietKc,
      this.maLichCongTac,
      this.maMocThoiGian,
      this.ngayThucHien,
      this.trangThai,
      this.maMocThoiGianKc,
      this.buoi,
      this.gioBd,
      this.gioKt,
      this.ghiChuMocThoiGian,
      this.macDinh,
      this.maDonVi,
      this.sttMocThoiGian,
      this.trangThaiMocThoiGian,
      this.ngayThucHienVn,
      this.soCongViecMocThoiGian,
      this.soCongViecNgay,
      this.giaTriCot1,
      this.giaTriCot2,
      this.giaTriCot3,
      this.giaTriCot4,
      this.giaTriCot5,
      this.giaTriCot6,
      this.giaTriCot7,
      this.giaTriCot8,
      this.giaTriCot9,
      this.maVanBanDen});

  LctDetaiThuDonVi.fromJson(Map<String, dynamic> json) {
    maCongViecChiTietKc = json['ma_cong_viec_chi_tiet_kc'];
    maLichCongTacChiTiet = json['ma_lich_cong_tac_chi_tiet'];
    gioThucHien = json['gio_thuc_hien'];
    noiDung = json['noi_dung'];
    fileLct = json['file_lct'];
    fileTaiLieu = json['file_tai_lieu'];
    diaDiem = json['dia_diem'];
    thanhPhanThamDu = json['thanh_phan_tham_du'];
    trangThaiCvct = json['trang_thai_cvct'];
    daDuyet = json['da_duyet'];
    maLichCongTacChiTietKc = json['ma_lich_cong_tac_chi_tiet_kc'];
    maLichCongTac = json['ma_lich_cong_tac'];
    maMocThoiGian = json['ma_moc_thoi_gian'];
    ngayThucHien = json['ngay_thuc_hien'];
    trangThai = json['trang_thai'];
    maMocThoiGianKc = json['ma_moc_thoi_gian_kc'];
    buoi = json['buoi'];
    gioBd = json['gio_bd'];
    gioKt = json['gio_kt'];
    ghiChuMocThoiGian = json['ghi_chu_moc_thoi_gian'];
    macDinh = json['mac_dinh'];
    maDonVi = json['ma_don_vi'];
    sttMocThoiGian = json['stt_moc_thoi_gian'];
    trangThaiMocThoiGian = json['trang_thai_moc_thoi_gian'];
    ngayThucHienVn = json['ngay_thuc_hien_vn'];
    soCongViecMocThoiGian = json['so_cong_viec_moc_thoi_gian'];
    soCongViecNgay = json['so_cong_viec_ngay'];
    giaTriCot1 = json['gia_tri_cot_1'];
    giaTriCot2 = json['gia_tri_cot_2'];
    giaTriCot3 = json['gia_tri_cot_3'];
    giaTriCot4 = json['gia_tri_cot_4'];
    giaTriCot5 = json['gia_tri_cot_5'];
    giaTriCot6 = json['gia_tri_cot_6'];
    giaTriCot7 = json['gia_tri_cot_7'];
    giaTriCot8 = json['gia_tri_cot_8'];
    giaTriCot9 = json['gia_tri_cot_9'];
    maVanBanDen = json['ma_van_ban_den'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_cong_viec_chi_tiet_kc'] = this.maCongViecChiTietKc;
    data['ma_lich_cong_tac_chi_tiet'] = this.maLichCongTacChiTiet;
    data['gio_thuc_hien'] = this.gioThucHien;
    data['noi_dung'] = this.noiDung;
    data['file_lct'] = this.fileLct;
    data['file_tai_lieu'] = this.fileTaiLieu;
    data['dia_diem'] = this.diaDiem;
    data['thanh_phan_tham_du'] = this.thanhPhanThamDu;
    data['trang_thai_cvct'] = this.trangThaiCvct;
    data['da_duyet'] = this.daDuyet;
    data['ma_lich_cong_tac_chi_tiet_kc'] = this.maLichCongTacChiTietKc;
    data['ma_lich_cong_tac'] = this.maLichCongTac;
    data['ma_moc_thoi_gian'] = this.maMocThoiGian;
    data['ngay_thuc_hien'] = this.ngayThucHien;
    data['trang_thai'] = this.trangThai;
    data['ma_moc_thoi_gian_kc'] = this.maMocThoiGianKc;
    data['buoi'] = this.buoi;
    data['gio_bd'] = this.gioBd;
    data['gio_kt'] = this.gioKt;
    data['ghi_chu_moc_thoi_gian'] = this.ghiChuMocThoiGian;
    data['mac_dinh'] = this.macDinh;
    data['ma_don_vi'] = this.maDonVi;
    data['stt_moc_thoi_gian'] = this.sttMocThoiGian;
    data['trang_thai_moc_thoi_gian'] = this.trangThaiMocThoiGian;
    data['ngay_thuc_hien_vn'] = this.ngayThucHienVn;
    data['so_cong_viec_moc_thoi_gian'] = this.soCongViecMocThoiGian;
    data['so_cong_viec_ngay'] = this.soCongViecNgay;
    data['gia_tri_cot_1'] = this.giaTriCot1;
    data['gia_tri_cot_2'] = this.giaTriCot2;
    data['gia_tri_cot_3'] = this.giaTriCot3;
    data['gia_tri_cot_4'] = this.giaTriCot4;
    data['gia_tri_cot_5'] = this.giaTriCot5;
    data['gia_tri_cot_6'] = this.giaTriCot6;
    data['gia_tri_cot_7'] = this.giaTriCot7;
    data['gia_tri_cot_8'] = this.giaTriCot8;
    data['gia_tri_cot_9'] = this.giaTriCot9;
    data['ma_van_ban_den'] = this.maVanBanDen;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}

class ShowDetalThu {
  String? ngayThucHien;
  List<LctDetaiThuDonVi>? data;
  ShowDetalThu({this.ngayThucHien, this.data});
}
