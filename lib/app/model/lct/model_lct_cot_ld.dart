class LctLanhDaoByCot {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailLctLanhDaoByCot>? data;
  Param? param;

  LctLanhDaoByCot(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  LctLanhDaoByCot.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailLctLanhDaoByCot>[];
      json['data'].forEach((v) {
        data!.add(new DetailLctLanhDaoByCot.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailLctLanhDaoByCot {
  double? maCtcbKc;
  String? hoVaTenCanBo;

  DetailLctLanhDaoByCot({this.maCtcbKc, this.hoVaTenCanBo});

  DetailLctLanhDaoByCot.fromJson(Map<String, dynamic> json) {
    maCtcbKc = json['ma_ctcb_kc'];
    hoVaTenCanBo = json['ho_va_ten_can_bo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_ctcb_kc'] = this.maCtcbKc;
    data['ho_va_ten_can_bo'] = this.hoVaTenCanBo;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
