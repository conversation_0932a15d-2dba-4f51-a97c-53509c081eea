class ModelLctDsCb {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailLctDsCb>? data;
  Param? param;

  ModelLctDsCb(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  ModelLctDsCb.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailLctDsCb>[];
      json['data'].forEach((v) {
        data!.add(new DetailLctDsCb.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailLctDsCb {
  double? maCtcbKc;
  double? maCanBoKc;
  String? hoVaTenCanBo;
  String? tenDonVi;
  double? sttCanBo;

  DetailLctDsCb(
      {this.maCtcbKc,
      this.maCanBoKc,
      this.hoVaTenCanBo,
      this.tenDonVi,
      this.sttCanBo});

  DetailLctDsCb.fromJson(Map<String, dynamic> json) {
    maCtcbKc = json['ma_ctcb_kc'];
    maCanBoKc = json['ma_can_bo_kc'];
    hoVaTenCanBo = json['ho_va_ten_can_bo'];
    tenDonVi = json['ten_don_vi'];
    sttCanBo = json['stt_can_bo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_ctcb_kc'] = this.maCtcbKc;
    data['ma_can_bo_kc'] = this.maCanBoKc;
    data['ho_va_ten_can_bo'] = this.hoVaTenCanBo;
    data['ten_don_vi'] = this.tenDonVi;
    data['stt_can_bo'] = this.sttCanBo;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
