class lctDsLichCongTacDonVi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailDsLctDonVi>? data;
  Param? param;

  lctDsLichCongTacDonVi(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  lctDsLichCongTacDonVi.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailDsLctDonVi>[];
      json['data'].forEach((v) {
        data!.add(new DetailDsLctDonVi.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailDsLctDonVi {
  double? maLichCongTacKc;
  double? nam;
  double? tuan;
  String? ngayBatDau;
  String? ngayKetThuc;
  double? maCtcb;
  double? maDonVi;
  double? maDonViQuanTri;
  String? ngayTao;
  String? ghiChuLichCongTac;
  double? trangThaiLichCongTac;
  double? maLichCongTacCha;
  double? maLoaiLichCongTac;
  double? maCbDkCu;
  double? maToCu;
  double? maDonViCu;
  double? maHinhThucCu;
  double? maCbLuuCu;
  double? maLctCu;
  double? congBo;
  String? ngayBatDauVn;
  String? ngayKetThucVn;
  double? soCot;

  DetailDsLctDonVi(
      {this.maLichCongTacKc,
      this.nam,
      this.tuan,
      this.ngayBatDau,
      this.ngayKetThuc,
      this.maCtcb,
      this.maDonVi,
      this.maDonViQuanTri,
      this.ngayTao,
      this.ghiChuLichCongTac,
      this.trangThaiLichCongTac,
      this.maLichCongTacCha,
      this.maLoaiLichCongTac,
      this.maCbDkCu,
      this.maToCu,
      this.maDonViCu,
      this.maHinhThucCu,
      this.maCbLuuCu,
      this.maLctCu,
      this.congBo,
      this.ngayBatDauVn,
      this.ngayKetThucVn,
      this.soCot});

  DetailDsLctDonVi.fromJson(Map<String, dynamic> json) {
    maLichCongTacKc = json['ma_lich_cong_tac_kc'];
    nam = json['nam'];
    tuan = json['tuan'];
    ngayBatDau = json['ngay_bat_dau'];
    ngayKetThuc = json['ngay_ket_thuc'];
    maCtcb = json['ma_ctcb'];
    maDonVi = json['ma_don_vi'];
    maDonViQuanTri = json['ma_don_vi_quan_tri'];
    ngayTao = json['ngay_tao'];
    ghiChuLichCongTac = json['ghi_chu_lich_cong_tac'];
    trangThaiLichCongTac = json['trang_thai_lich_cong_tac'];
    maLichCongTacCha = json['ma_lich_cong_tac_cha'];
    maLoaiLichCongTac = json['ma_loai_lich_cong_tac'];
    maCbDkCu = json['ma_cb_dk_cu'];
    maToCu = json['ma_to_cu'];
    maDonViCu = json['ma_don_vi_cu'];
    maHinhThucCu = json['ma_hinh_thuc_cu'];
    maCbLuuCu = json['ma_cb_luu_cu'];
    maLctCu = json['ma_lct_cu'];
    congBo = json['cong_bo'];
    ngayBatDauVn = json['ngay_bat_dau_vn'];
    ngayKetThucVn = json['ngay_ket_thuc_vn'];
    soCot = json['so_cot'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_lich_cong_tac_kc'] = this.maLichCongTacKc;
    data['nam'] = this.nam;
    data['tuan'] = this.tuan;
    data['ngay_bat_dau'] = this.ngayBatDau;
    data['ngay_ket_thuc'] = this.ngayKetThuc;
    data['ma_ctcb'] = this.maCtcb;
    data['ma_don_vi'] = this.maDonVi;
    data['ma_don_vi_quan_tri'] = this.maDonViQuanTri;
    data['ngay_tao'] = this.ngayTao;
    data['ghi_chu_lich_cong_tac'] = this.ghiChuLichCongTac;
    data['trang_thai_lich_cong_tac'] = this.trangThaiLichCongTac;
    data['ma_lich_cong_tac_cha'] = this.maLichCongTacCha;
    data['ma_loai_lich_cong_tac'] = this.maLoaiLichCongTac;
    data['ma_cb_dk_cu'] = this.maCbDkCu;
    data['ma_to_cu'] = this.maToCu;
    data['ma_don_vi_cu'] = this.maDonViCu;
    data['ma_hinh_thuc_cu'] = this.maHinhThucCu;
    data['ma_cb_luu_cu'] = this.maCbLuuCu;
    data['ma_lct_cu'] = this.maLctCu;
    data['cong_bo'] = this.congBo;
    data['ngay_bat_dau_vn'] = this.ngayBatDauVn;
    data['ngay_ket_thuc_vn'] = this.ngayKetThucVn;
    data['so_cot'] = this.soCot;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
