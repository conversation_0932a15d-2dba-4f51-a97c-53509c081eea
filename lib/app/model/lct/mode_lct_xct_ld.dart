class LctXemLichCtLanhDao {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailXemLctLanhDao>? data;
  Param? param;

  LctXemLichCtLanhDao(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  LctXemLichCtLanhDao.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailXemLctLanhDao>[];
      json['data'].forEach((v) {
        data!.add(new DetailXemLctLanhDao.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailXemLctLanhDao {
  String? maAbc;
  String? ngayThucHien;
  String? ngayThucHienVn;
  double? buoi;
  String? gioBd;
  String? gioKt;
  String? cb1;
  String? cb2;
  String? cb3;
  String? cb4;
  String? cb5;
  String? cb6;
  String? cb7;
  String? cb8;
  String? cb9;
  String? cb10;
  double? soBuoi;

  DetailXemLctLanhDao(
      {this.maAbc,
      this.ngayThucHien,
      this.ngayThucHienVn,
      this.buoi,
      this.gioBd,
      this.gioKt,
      this.cb1,
      this.cb2,
      this.cb3,
      this.cb4,
      this.cb5,
      this.cb6,
      this.cb7,
      this.cb8,
      this.cb9,
      this.cb10,
      this.soBuoi});

  DetailXemLctLanhDao.fromJson(Map<String, dynamic> json) {
    maAbc = json['ma_abc'];
    ngayThucHien = json['ngay_thuc_hien'];
    ngayThucHienVn = json['ngay_thuc_hien_vn'];
    buoi = json['buoi'];
    gioBd = json['gio_bd'];
    gioKt = json['gio_kt'];
    cb1 = json['cb1'];
    cb2 = json['cb2'];
    cb3 = json['cb3'];
    cb4 = json['cb4'];
    cb5 = json['cb5'];
    cb6 = json['cb6'];
    cb7 = json['cb7'];
    cb8 = json['cb8'];
    cb9 = json['cb9'];
    cb10 = json['cb10'];
    soBuoi = json['so_buoi'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_abc'] = this.maAbc;
    data['ngay_thuc_hien'] = this.ngayThucHien;
    data['ngay_thuc_hien_vn'] = this.ngayThucHienVn;
    data['buoi'] = this.buoi;
    data['gio_bd'] = this.gioBd;
    data['gio_kt'] = this.gioKt;
    data['cb1'] = this.cb1;
    data['cb2'] = this.cb2;
    data['cb3'] = this.cb3;
    data['cb4'] = this.cb4;
    data['cb5'] = this.cb5;
    data['cb6'] = this.cb6;
    data['cb7'] = this.cb7;
    data['cb8'] = this.cb8;
    data['cb9'] = this.cb9;
    data['cb10'] = this.cb10;
    data['so_buoi'] = this.soBuoi;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}

class ShowDetalThuLd {
  String? ngayThucHien;
  List<DetailXemLctLanhDao>? data;
  ShowDetalThuLd({this.ngayThucHien, this.data});
}

class SoCotLanhDao {
  String? maCot;
  String? tenLanhDao;
  SoCotLanhDao({this.maCot, this.tenLanhDao});
}
