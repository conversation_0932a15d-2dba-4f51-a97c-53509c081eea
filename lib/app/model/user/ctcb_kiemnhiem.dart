import 'dart:convert';

HtDsCtcbKiemNhiem htDsCtcbKiemNhiemFromJson(String str) =>
    HtDsCtcbKiemNhiem.fromJson(json.decode(str));

String htDsCtcbKiemNhiemToJson(HtDsCtcbKiemNhiem data) =>
    json.encode(data.toJson());

class HtDsCtcbKiemNhiem {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsCtcbKiemNhiem>? data;

  HtDsCtcbKiemNhiem({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory HtDsCtcbKiemNhiem.fromJson(Map<String, dynamic> json) =>
      HtDsCtcbKiemNhiem(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsCtcbKiemNhiem>.from(
                json["data"]!.map((x) => DsCtcbKiemNhiem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsCtcbKiemNhiem {
  double? maCtcbKc;
  DateTime? ngayBatDau;
  dynamic ngayKetThuc;
  double? maChucVu;
  double? maCanBo;
  double? maDonVi;
  double? laMacDinh;
  String? refreshToken;
  dynamic laLanhDao;
  double? maQuyenSauKhiDangNhap;
  dynamic mauChuKySo;
  dynamic maCanBoCu;
  dynamic maToCu;
  dynamic lanhDaoCu;
  dynamic vanThuCu;
  double? trangThaiOnline;
  double? biKhoa;
  String? smartcaAccessToken;
  double? smartcaExpire;
  dynamic smartcaThUid;
  dynamic smartcaThPassword;
  dynamic smartcaThTotp;
  double? maDonViCha;
  double? maLoaiDonVi;
  String? hoVaTenCanBo;
  String? tenChucVu;
  String? tenDonVi;
  dynamic ngayDangKyOtp;
  dynamic ngayDangKyNhacViecQuaSms;
  String? diDongCanBo;
  dynamic avatar;
  String? username;
  dynamic pwd;
  dynamic idVm;
  dynamic tptkMaDvCha;
  double? dnMacDinh;
  String? loaiKySo;
  String? loaiKySoSim;
  String? encryptCb;

  DsCtcbKiemNhiem({
    this.maCtcbKc,
    this.ngayBatDau,
    this.ngayKetThuc,
    this.maChucVu,
    this.maCanBo,
    this.maDonVi,
    this.laMacDinh,
    this.refreshToken,
    this.laLanhDao,
    this.maQuyenSauKhiDangNhap,
    this.mauChuKySo,
    this.maCanBoCu,
    this.maToCu,
    this.lanhDaoCu,
    this.vanThuCu,
    this.trangThaiOnline,
    this.biKhoa,
    this.smartcaAccessToken,
    this.smartcaExpire,
    this.smartcaThUid,
    this.smartcaThPassword,
    this.smartcaThTotp,
    this.maDonViCha,
    this.maLoaiDonVi,
    this.hoVaTenCanBo,
    this.tenChucVu,
    this.tenDonVi,
    this.ngayDangKyOtp,
    this.ngayDangKyNhacViecQuaSms,
    this.diDongCanBo,
    this.avatar,
    this.username,
    this.pwd,
    this.idVm,
    this.tptkMaDvCha,
    this.dnMacDinh,
    this.loaiKySo,
    this.loaiKySoSim,
    this.encryptCb,
  });

  factory DsCtcbKiemNhiem.fromJson(Map<String, dynamic> json) =>
      DsCtcbKiemNhiem(
        maCtcbKc: json["ma_ctcb_kc"],
        ngayBatDau: json["ngay_bat_dau"] == null
            ? null
            : DateTime.parse(json["ngay_bat_dau"]),
        ngayKetThuc: json["ngay_ket_thuc"],
        maChucVu: json["ma_chuc_vu"],
        maCanBo: json["ma_can_bo"],
        maDonVi: json["ma_don_vi"],
        laMacDinh: json["la_mac_dinh"],
        refreshToken: json["refresh_token"],
        laLanhDao: json["la_lanh_dao"],
        maQuyenSauKhiDangNhap: json["ma_quyen_sau_khi_dang_nhap"],
        mauChuKySo: json["mau_chu_ky_so"],
        maCanBoCu: json["ma_can_bo_cu"],
        maToCu: json["ma_to_cu"],
        lanhDaoCu: json["lanh_dao_cu"],
        vanThuCu: json["van_thu_cu"],
        trangThaiOnline: json["trang_thai_online"],
        biKhoa: json["bi_khoa"],
        smartcaAccessToken: json["smartca_access_token"],
        smartcaExpire: json["smartca_expire"],
        smartcaThUid: json["smartca_th_uid"],
        smartcaThPassword: json["smartca_th_password"],
        smartcaThTotp: json["smartca_th_totp"],
        maDonViCha: json["ma_don_vi_cha"],
        maLoaiDonVi: json["ma_loai_don_vi"],
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        tenDonVi: json["ten_don_vi"],
        ngayDangKyOtp: json["ngay_dang_ky_otp"],
        ngayDangKyNhacViecQuaSms: json["ngay_dang_ky_nhac_viec_qua_sms"],
        diDongCanBo: json["di_dong_can_bo"],
        avatar: json["avatar"],
        username: json["username"],
        pwd: json["pwd"],
        idVm: json["id_vm"],
        tptkMaDvCha: json["tptk_ma_dv_cha"],
        dnMacDinh: json["dn_mac_dinh"],
        loaiKySo: json["loai_ky_so"],
        loaiKySoSim: json["loai_ky_so_sim"],
        encryptCb: json["encrypt_cb"],
      );

  Map<String, dynamic> toJson() => {
        "ma_ctcb_kc": maCtcbKc,
        "ngay_bat_dau": ngayBatDau?.toIso8601String(),
        "ngay_ket_thuc": ngayKetThuc,
        "ma_chuc_vu": maChucVu,
        "ma_can_bo": maCanBo,
        "ma_don_vi": maDonVi,
        "la_mac_dinh": laMacDinh,
        "refresh_token": refreshToken,
        "la_lanh_dao": laLanhDao,
        "ma_quyen_sau_khi_dang_nhap": maQuyenSauKhiDangNhap,
        "mau_chu_ky_so": mauChuKySo,
        "ma_can_bo_cu": maCanBoCu,
        "ma_to_cu": maToCu,
        "lanh_dao_cu": lanhDaoCu,
        "van_thu_cu": vanThuCu,
        "trang_thai_online": trangThaiOnline,
        "bi_khoa": biKhoa,
        "smartca_access_token": smartcaAccessToken,
        "smartca_expire": smartcaExpire,
        "smartca_th_uid": smartcaThUid,
        "smartca_th_password": smartcaThPassword,
        "smartca_th_totp": smartcaThTotp,
        "ma_don_vi_cha": maDonViCha,
        "ma_loai_don_vi": maLoaiDonVi,
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "ten_chuc_vu": tenChucVu,
        "ten_don_vi": tenDonVi,
        "ngay_dang_ky_otp": ngayDangKyOtp,
        "ngay_dang_ky_nhac_viec_qua_sms": ngayDangKyNhacViecQuaSms,
        "di_dong_can_bo": diDongCanBo,
        "avatar": avatar,
        "username": username,
        "pwd": pwd,
        "id_vm": idVm,
        "tptk_ma_dv_cha": tptkMaDvCha,
        "dn_mac_dinh": dnMacDinh,
        "loai_ky_so": loaiKySo,
        "loai_ky_so_sim": loaiKySoSim,
        "encrypt_cb": encryptCb,
      };
}
