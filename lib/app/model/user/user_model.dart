import 'dart:convert';

// To parse this JSON data, do
//
//     final ktQuyenModel = ktQuyenModelFromJson(jsonString);

KtQuyenModel ktQuyenModelFromJson(String str) =>
    KtQuyenModel.fromJson(json.decode(str));

String ktQuyenModelToJson(KtQuyenModel data) => json.encode(data.toJson());

class KtQuyenModel {
  int id;
  String message;
  int vanThu;
  int lanhDao;
  int chuyenVien;
  int lanhDaoPhong;
  int phapChe;
  int truongDonVi;

  KtQuyenModel({
    required this.id,
    required this.message,
    required this.vanThu,
    required this.lanhDao,
    required this.chuyenVien,
    required this.lanhDaoPhong,
    required this.phapChe,
    required this.truongDonVi,
  });

  factory KtQuyenModel.fromJson(Map<String, dynamic> json) => KtQuyenModel(
        id: json["id"],
        message: json["message"],
        vanThu: json["van_thu"],
        lanh<PERSON>ao: json["lanh_dao"],
        chuyenVien: json["chuyen_vien"],
        lanhDaoPhong: json["lanh_dao_phong"],
        phapChe: json["phap_che"],
        truongDonVi: json["truong_don_vi"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "message": message,
        "van_thu": vanThu,
        "lanh_dao": lanhDao,
        "chuyen_vien": chuyenVien,
        "lanh_dao_phong": lanhDaoPhong,
        "phap_che": phapChe,
        "truong_don_vi": truongDonVi,
      };
}
