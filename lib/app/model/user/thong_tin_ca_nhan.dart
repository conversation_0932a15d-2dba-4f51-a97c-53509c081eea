class ThongTinCaNhan {
  bool? success;
  String? message;
  DetailThongTinCaNhan? data;
  String? storeName;
  int? storeType;
  Param? param;

  ThongTinCaNhan(
      {this.success,
      this.message,
      this.data,
      this.storeName,
      this.storeType,
      this.param});

  ThongTinCaNhan.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    data = json['data'] != null
        ? new DetailThongTinCaNhan.fromJson(json['data'])
        : null;
    storeName = json['store_name'];
    storeType = json['store_type'];
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailThongTinCaNhan {
  double? maCanBoKc;
  String? username;
  String? hoVaTenCanBo;
  String? cmndCanBo;
  String? sdtCanBo;
  String? diDongCanBo;
  String? diaChiCanBo;
  double? trangThaiCanBo;
  double? sttCanBo;
  double? maDonViBanDau;
  double? gioiTinh;
  String? ngayDangKyOtp;
  String? ngayDangKyNhacViecQuaSms;
  String? avatar;
  double? maTonGiao;
  double? maDanToc;
  String? emailCanBo;
  double? xacThucEmail;
  String? ngayCapCmnd;
  String? noiCapCmnd;
  String? ngaySinh;
  double? maCanBoCu;
  double? lanhDaoCu;
  double? vanThuCu;
  double? maChucVuCu;
  double? maChucVu;
  double? maToCu;
  String? tenChucVu;
  double? maChucVuKc;
  dynamic smartcaThUid;
  dynamic smartcaThPassword;
  dynamic smartcaThTotp;
  String? ngaySinhVn;
  String? ngayCapCmndVn;
  dynamic pageMacDinh;
  dynamic chuoiMaLoaiKySo;
  dynamic loaiKySoSim;

  DetailThongTinCaNhan(
      {this.maCanBoKc,
      this.username,
      this.hoVaTenCanBo,
      this.cmndCanBo,
      this.sdtCanBo,
      this.diDongCanBo,
      this.diaChiCanBo,
      this.trangThaiCanBo,
      this.sttCanBo,
      this.maDonViBanDau,
      this.gioiTinh,
      this.ngayDangKyOtp,
      this.ngayDangKyNhacViecQuaSms,
      this.avatar,
      this.maTonGiao,
      this.maDanToc,
      this.emailCanBo,
      this.xacThucEmail,
      this.ngayCapCmnd,
      this.noiCapCmnd,
      this.ngaySinh,
      this.maCanBoCu,
      this.lanhDaoCu,
      this.vanThuCu,
      this.maChucVuCu,
      this.maChucVu,
      this.maToCu,
      this.tenChucVu,
      this.maChucVuKc,
      this.smartcaThUid,
      this.smartcaThPassword,
      this.smartcaThTotp,
      this.ngaySinhVn,
      this.ngayCapCmndVn,
      this.pageMacDinh,
      this.chuoiMaLoaiKySo,
      this.loaiKySoSim});

  DetailThongTinCaNhan.fromJson(Map<String, dynamic> json) {
    maCanBoKc = json['ma_can_bo_kc'];
    username = json['username'];
    hoVaTenCanBo = json['ho_va_ten_can_bo'];
    cmndCanBo = json['cmnd_can_bo'];
    sdtCanBo = json['sdt_can_bo'];
    diDongCanBo = json['di_dong_can_bo'];
    diaChiCanBo = json['dia_chi_can_bo'];
    trangThaiCanBo = json['trang_thai_can_bo'];
    sttCanBo = json['stt_can_bo'];
    maDonViBanDau = json['ma_don_vi_ban_dau'];
    gioiTinh = json['gioi_tinh'];
    ngayDangKyOtp = json['ngay_dang_ky_otp'];
    ngayDangKyNhacViecQuaSms = json['ngay_dang_ky_nhac_viec_qua_sms'];
    avatar = json['avatar'];
    maTonGiao = json['ma_ton_giao'];
    maDanToc = json['ma_dan_toc'];
    emailCanBo = json['email_can_bo'];
    xacThucEmail = json['xac_thuc_email'];
    ngayCapCmnd = json['ngay_cap_cmnd'];
    noiCapCmnd = json['noi_cap_cmnd'];
    ngaySinh = json['ngay_sinh'];
    maCanBoCu = json['ma_can_bo_cu'];
    lanhDaoCu = json['lanh_dao_cu'];
    vanThuCu = json['van_thu_cu'];
    maChucVuCu = json['ma_chuc_vu_cu'];
    maChucVu = json['ma_chuc_vu'];
    maToCu = json['ma_to_cu'];
    tenChucVu = json['ten_chuc_vu'];
    maChucVuKc = json['ma_chuc_vu_kc'];
    smartcaThUid = json['smartca_th_uid'];
    smartcaThPassword = json['smartca_th_password'];
    smartcaThTotp = json['smartca_th_totp'];
    ngaySinhVn = json['ngay_sinh_vn'];
    ngayCapCmndVn = json['ngay_cap_cmnd_vn'];
    pageMacDinh = json['page_mac_dinh'];
    chuoiMaLoaiKySo = json['chuoi_ma_loai_ky_so'];
    loaiKySoSim = json['loai_ky_so_sim'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_can_bo_kc'] = this.maCanBoKc;
    data['username'] = this.username;
    data['ho_va_ten_can_bo'] = this.hoVaTenCanBo;
    data['cmnd_can_bo'] = this.cmndCanBo;
    data['sdt_can_bo'] = this.sdtCanBo;
    data['di_dong_can_bo'] = this.diDongCanBo;
    data['dia_chi_can_bo'] = this.diaChiCanBo;
    data['trang_thai_can_bo'] = this.trangThaiCanBo;
    data['stt_can_bo'] = this.sttCanBo;
    data['ma_don_vi_ban_dau'] = this.maDonViBanDau;
    data['gioi_tinh'] = this.gioiTinh;
    data['ngay_dang_ky_otp'] = this.ngayDangKyOtp;
    data['ngay_dang_ky_nhac_viec_qua_sms'] = this.ngayDangKyNhacViecQuaSms;
    data['avatar'] = this.avatar;
    data['ma_ton_giao'] = this.maTonGiao;
    data['ma_dan_toc'] = this.maDanToc;
    data['email_can_bo'] = this.emailCanBo;
    data['xac_thuc_email'] = this.xacThucEmail;
    data['ngay_cap_cmnd'] = this.ngayCapCmnd;
    data['noi_cap_cmnd'] = this.noiCapCmnd;
    data['ngay_sinh'] = this.ngaySinh;
    data['ma_can_bo_cu'] = this.maCanBoCu;
    data['lanh_dao_cu'] = this.lanhDaoCu;
    data['van_thu_cu'] = this.vanThuCu;
    data['ma_chuc_vu_cu'] = this.maChucVuCu;
    data['ma_chuc_vu'] = this.maChucVu;
    data['ma_to_cu'] = this.maToCu;
    data['ten_chuc_vu'] = this.tenChucVu;
    data['ma_chuc_vu_kc'] = this.maChucVuKc;
    data['smartca_th_uid'] = this.smartcaThUid;
    data['smartca_th_password'] = this.smartcaThPassword;
    data['smartca_th_totp'] = this.smartcaThTotp;
    data['ngay_sinh_vn'] = this.ngaySinhVn;
    data['ngay_cap_cmnd_vn'] = this.ngayCapCmndVn;
    data['page_mac_dinh'] = this.pageMacDinh;
    data['chuoi_ma_loai_ky_so'] = this.chuoiMaLoaiKySo;
    data['loai_ky_so_sim'] = this.loaiKySoSim;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
