class MauChuKyModel {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailMauChuKy>? data;
  Param? param;

  MauChuKyModel(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  MauChuKyModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailMauChuKy>[];
      json['data'].forEach((v) {
        data!.add(new DetailMauChuKy.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailMauChuKy {
  double? idKySo;
  String? tenKySo;
  double? trangThai;
  double? maCtcbTao;
  String? linkCks;
  double? soThuTu;
  double? stt;

  DetailMauChuKy(
      {this.idKySo,
      this.tenKySo,
      this.trangThai,
      this.maCtcbTao,
      this.linkCks,
      this.soThuTu,
      this.stt});

  DetailMauChuKy.fromJson(Map<String, dynamic> json) {
    idKySo = json['id_ky_so'];
    tenKySo = json['ten_ky_so'];
    trangThai = json['trang_thai'];
    maCtcbTao = json['ma_ctcb_tao'];
    linkCks = json['link_cks'];
    soThuTu = json['so_thu_tu'];
    stt = json['stt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id_ky_so'] = this.idKySo;
    data['ten_ky_so'] = this.tenKySo;
    data['trang_thai'] = this.trangThai;
    data['ma_ctcb_tao'] = this.maCtcbTao;
    data['link_cks'] = this.linkCks;
    data['so_thu_tu'] = this.soThuTu;
    data['stt'] = this.stt;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
