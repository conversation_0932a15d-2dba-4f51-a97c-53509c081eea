import 'dart:convert';

class MFileSauCks {
  bool? success;
  String? message;
  List<FileSauKhiKy>? data;
  dynamic storeName;
  int? storeType;

  MFileSauCks({
    this.success,
    this.message,
    this.data,
    this.storeName,
    this.storeType,
  });

  factory MFileSauCks.fromJson(Map<String, dynamic> json) => MFileSauCks(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<FileSauKhiKy>.from(
                json["data"]!.map((x) => FileSauKhiKy.fromJson(x))),
        storeName: json["store_name"],
        storeType: json["store_type"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "store_name": storeName,
        "store_type": storeType,
      };
}

class FileSauKhiKy {
  String? pathFile;

  FileSauKhiKy({
    this.pathFile,
  });

  factory FileSauKhiKy.fromJson(Map<String, dynamic> json) => FileSauKhiKy(
        pathFile: json["path_file"],
      );

  Map<String, dynamic> toJson() => {
        "path_file": pathFile,
      };
}

// To parse this JSON data, do
//
//     final mdCheckDuLuongFile = mdCheckDuLuongFileFromJson(jsonString);

MdCheckDuLuongFile mdCheckDuLuongFileFromJson(String str) =>
    MdCheckDuLuongFile.fromJson(json.decode(str));

String mdCheckDuLuongFileToJson(MdCheckDuLuongFile data) =>
    json.encode(data.toJson());

class MdCheckDuLuongFile {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  CheckDuLuongFile? data;

  MdCheckDuLuongFile({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory MdCheckDuLuongFile.fromJson(Map<String, dynamic> json) =>
      MdCheckDuLuongFile(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? null
            : CheckDuLuongFile.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data?.toJson(),
      };
}

class CheckDuLuongFile {
  double? dlMax;
  double? dlDaSuDung;
  double? dlSauCongFile;
  double? duocLuu;
  double? dlVuot;
  double? saiThoiGian;

  CheckDuLuongFile({
    this.dlMax,
    this.dlDaSuDung,
    this.dlSauCongFile,
    this.duocLuu,
    this.dlVuot,
    this.saiThoiGian,
  });

  factory CheckDuLuongFile.fromJson(Map<String, dynamic> json) =>
      CheckDuLuongFile(
        dlMax: json["dl_max"],
        dlDaSuDung: json["dl_da_su_dung"],
        dlSauCongFile: json["dl_sau_cong_file"],
        duocLuu: json["duoc_luu"],
        dlVuot: json["dl_vuot"],
        saiThoiGian: json["sai_thoi_gian"],
      );

  Map<String, dynamic> toJson() => {
        "dl_max": dlMax,
        "dl_da_su_dung": dlDaSuDung,
        "dl_sau_cong_file": dlSauCongFile,
        "duoc_luu": duocLuu,
        "dl_vuot": dlVuot,
        "sai_thoi_gian": saiThoiGian,
      };
}
