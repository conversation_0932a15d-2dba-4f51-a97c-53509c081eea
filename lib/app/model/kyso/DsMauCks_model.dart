// To parse this JSON data, do
//
//     final dsCks = dsCksFromJson(jsonString);

import 'dart:convert';

DsCks dsCksFromJson(String str) => DsCks.fromJson(json.decode(str));

String dsCksToJson(DsCks data) => json.encode(data.toJson());

class DsCks {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsMauCks>? data;

  DsCks({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsCks.fromJson(Map<String, dynamic> json) => DsCks(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsMauCks>.from(
                json["data"]!.map((x) => DsMauCks.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsMauCks {
  double? idKySo;
  String? tenKySo;
  double? trangThai;
  double? maCtcbTao;
  String? linkCks;
  double? soThuTu;
  double? stt;

  DsMauCks({
    this.idKySo,
    this.tenKySo,
    this.trangThai,
    this.maCtcbTao,
    this.linkCks,
    this.soThuTu,
    this.stt,
  });

  factory DsMauCks.fromJson(Map<String, dynamic> json) => DsMauCks(
        idKySo: json["id_ky_so"],
        tenKySo: json["ten_ky_so"],
        trangThai: json["trang_thai"],
        maCtcbTao: json["ma_ctcb_tao"],
        linkCks: json["link_cks"],
        soThuTu: json["so_thu_tu"],
        stt: json["stt"],
      );

  Map<String, dynamic> toJson() => {
        "id_ky_so": idKySo,
        "ten_ky_so": tenKySo,
        "trang_thai": trangThai,
        "ma_ctcb_tao": maCtcbTao,
        "link_cks": linkCks,
        "so_thu_tu": soThuTu,
        "stt": stt,
      };
}

// m kysosim

class MKySoSim {
  String? url;
  String? file;
  String? fileName;
  int? maCongVan;
  String? tenChucDanh;
  String? fileHinhAnh;
  String? trangKy;
  String? domainFile;
  String? domainApi;
  int? maCtcb;
  int? maDonVi;
  String? chucNang;
  String? soDienThoai;
  String? msspProvider;
  double? llx;
  double? lly;
  double? urx;
  double? ury;

  MKySoSim(
      {this.url,
      this.file,
      this.fileName,
      this.maCongVan,
      this.tenChucDanh,
      this.fileHinhAnh,
      this.trangKy,
      this.domainFile,
      this.domainApi,
      this.maCtcb,
      this.maDonVi,
      this.chucNang,
      this.soDienThoai,
      this.msspProvider,
      this.llx,
      this.lly,
      this.urx,
      this.ury});
}

class mKySoSmartCA {
  String? Comment;
  String? DataBase64;
  String? FontColor;
  String? FontName;
  int? FontSize;
  int? FontStyle;
  String? Image;
  String? Signatures;
  int? VisibleType;
  String? accessToken;
  String? filePath;
  int? maCtCb;

  mKySoSmartCA(
      {this.Comment,
      this.DataBase64,
      this.FontColor = "000000",
      this.FontName = "Time",
      this.FontSize = 12,
      this.FontStyle = 3,
      this.Image = "",
      this.Signatures = "",
      this.VisibleType = 3,
      this.accessToken,
      this.filePath,
      this.maCtCb});
}

class mSignaTures {
  String? rectangle;
  int? page;
  mSignaTures({this.rectangle, this.page});
  Map<String, dynamic> toJson() {
    return {
      'rectangle': rectangle,
      'page': page,
      // Add more properties as needed
    };
  }
}
