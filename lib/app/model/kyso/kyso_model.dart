// To parse this JSON data, do
//
//     final getFilekySoSimApp = getFilekySoSimAppFromJson(jsonString);

import 'dart:convert';

GetFilekySoSimApp getFilekySoSimAppFromJson(String str) =>
    GetFilekySoSimApp.fromJson(json.decode(str));

String getFilekySoSimAppToJson(GetFilekySoSimApp data) =>
    json.encode(data.toJson());

class GetFilekySoSimApp {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DataKySo>? data;

  GetFilekySoSimApp({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory GetFilekySoSimApp.fromJson(Map<String, dynamic> json) =>
      GetFilekySoSimApp(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DataKySo>.from(
                json["data"]!.map((x) => DataKySo.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DataKySo {
  String? fileKySinhRa;

  DataKySo({
    this.fileKySinhRa,
  });

  factory DataKySo.fromJson(Map<String, dynamic> json) => DataKySo(
        fileKySinhRa: json["file_ky_sinh_ra"],
      );

  Map<String, dynamic> toJson() => {
        "file_ky_sinh_ra": fileKySinhRa,
      };
}

class AccessTokenSmartCa {
  bool? success;
  String? message;
  List<MTokenSmartCA>? data;
  dynamic storeName;
  int? storeType;

  AccessTokenSmartCa({
    this.success,
    this.message,
    this.data,
    this.storeName,
    this.storeType,
  });

  factory AccessTokenSmartCa.fromJson(Map<String, dynamic> json) =>
      AccessTokenSmartCa(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<MTokenSmartCA>.from(
                json["data"]!.map((x) => MTokenSmartCA.fromJson(x))),
        storeName: json["store_name"],
        storeType: json["store_type"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "store_name": storeName,
        "store_type": storeType,
      };
}

class MTokenSmartCA {
  String? smartcaAccessToken;
  double? smartcaExpire;

  MTokenSmartCA({
    this.smartcaAccessToken,
    this.smartcaExpire,
  });

  factory MTokenSmartCA.fromJson(Map<String, dynamic> json) => MTokenSmartCA(
        smartcaAccessToken: json["smartca_access_token"],
        smartcaExpire: json["smartca_expire"],
      );

  Map<String, dynamic> toJson() => {
        "smartca_access_token": smartcaAccessToken,
        "smartca_expire": smartcaExpire,
      };
}
