// To parse this JSON data, do
//
//     final vbdiChiTiet = vbdiChiTietFromJson(jsonString);

import 'dart:convert';

VbdiChiTiet vbdiChiTietFromJson(String str) =>
    VbdiChiTiet.fromJson(json.decode(str));

String vbdiChiTietToJson(VbdiChiTiet data) => json.encode(data.toJson());

class VbdiChiTiet {
  Data? data;
  String? message;
  int? maCtcbTao;
  bool? laXuLyChinh;

  VbdiChiTiet({
    this.data,
    this.message,
    this.maCtcbTao,
    this.laXuLyChinh,
  });

  factory VbdiChiTiet.fromJson(Map<String, dynamic> json) => VbdiChiTiet(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        message: json["message"],
        maCtcbTao: json["ma_ctcb_tao"],
        laXuLyChinh: json["la_xu_ly_chinh"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "message": message,
        "ma_ctcb_tao": maCtcbTao,
        "la_xu_ly_chinh": laXuLyChinh,
      };
}

class Data {
  double? maVanBanKc;
  String? trichYeu;
  dynamic soKyHieu;
  String? nguoiKy;
  dynamic ngayBanHanhOut;
  dynamic ngayBanHanh;
  dynamic ngayBanHanhVetXuLyEdoc;
  dynamic maCoQuanBanHanh;
  String? soBanPhatHanh;
  String? fileVanBan;
  String? ngayTao;
  double? maCtcbTao;
  double? maDonViTao;
  double? maDonViQuanTriTao;
  double? loaiVanBanKhiMoiTao;
  double? trangThaiVanBan;
  String? noiLuuBanChinh;
  dynamic nguoiKyVanBan;
  dynamic canBoDuyet;
  dynamic soKyHieuVbDuocPhucDap;
  dynamic ngayBanHanhVbGoc;
  dynamic donViGuiVbGoc;
  dynamic vanBanLienQuan;
  String? tenCanBoSoan;
  String? tenCoQuanBanHanh;
  double? maGoc;
  String? phongBanSoan;
  dynamic maCvLthong;
  dynamic ngonNgu;
  dynamic soTrangVb;
  dynamic fileVbPhatHanhHgg;
  double? vbCanGs;
  dynamic gsvbMaCdnvCanPd;
  dynamic gsvbTenCdnvCanPd;
  double? maVanBanDiKc;
  dynamic ngayDi;
  dynamic soDi;
  DateTime? ngayLuu;
  double? maCtcbLuu;
  dynamic ngayDuyet;
  double? maCtcbDuyet;
  dynamic soNgayXuLyChung;
  dynamic hanXuLyChung;
  dynamic noiNhan;
  String? fileVanBanBs;
  dynamic yKienNguoiSoan;
  String? chuoiDonViNhan;
  dynamic ghiChu;
  dynamic maSoVanBanDi;
  double? maDonViQuanTri;
  double? maLinhVucVanBan;
  double? maLoaiVanBan;
  double? maCapDoMat;
  double? maCapDoKhan;
  double? maQuyTrinh;
  double? maVanBanGoc;
  dynamic traLoiVanBanDen;
  double? trangThaiVanBanDi;
  dynamic maVanBanDiCha;
  double? nam;
  dynamic maCtcbVanThu;
  dynamic maCbLuuCu;
  dynamic maCbDuyetCu;
  dynamic maMaDinhDanhCu;
  dynamic maLinhVucVanBanCu;
  dynamic maLoaiVanBanCu;
  dynamic maCapDoMatCu;
  dynamic maCapDoKhanCu;
  dynamic tenFileVanBanCu;
  dynamic maSoVanBanDiCu;
  dynamic phucDapCho;
  dynamic phucDapChoHscv;
  dynamic fileCongVanCu;
  double? soUuTien;
  dynamic vanBanCongKhai;
  double? guiKemVbGiay;
  double? daXoa;
  dynamic thoiHan;
  dynamic phucDapChoList;
  dynamic tenNguoiDuyet;
  double? coKySo;
  double? coKySaoY;
  double? coKyPhuLuc;
  dynamic canBoIgate;
  double? guiEdoc;
  double? guiNoiBoIoffice;
  dynamic vblqNgoaiHeThong;
  dynamic bussinessdoctype;
  dynamic fileQtxlHgg;
  dynamic kiemTraLoiPto;
  dynamic raSoatKySoHgg;
  dynamic maPakn;
  dynamic maSoVbDi;
  dynamic tenSoVbDi;
  String? canBoSoan;
  String? canBoTao;
  String? tenLinhVucVanBan;
  String? tenLoaiVanBan;
  dynamic coQuanBanHanh;
  double? maYeuCau;
  double? maXuLyDi;
  double? maCtcbGui;
  double? xem;
  dynamic ngayBanHanhN;
  String? diDongCanBo;
  double? maHoSoIgate;
  dynamic maHoSoIgateLgsp;
  String? maDinhDanhVb;
  double? vbdiDaCoChiDao;
  dynamic noiNhanVanBan;
  double? kiemTraTheThuc;
  String? fileDinhKem;
  double? trangThaiXuLyDi;
  String? capDoKhan;

  Data({
    this.maVanBanKc,
    this.trichYeu,
    this.soKyHieu,
    this.nguoiKy,
    this.ngayBanHanhOut,
    this.ngayBanHanh,
    this.ngayBanHanhVetXuLyEdoc,
    this.maCoQuanBanHanh,
    this.soBanPhatHanh,
    this.fileVanBan,
    this.ngayTao,
    this.maCtcbTao,
    this.maDonViTao,
    this.maDonViQuanTriTao,
    this.loaiVanBanKhiMoiTao,
    this.trangThaiVanBan,
    this.noiLuuBanChinh,
    this.nguoiKyVanBan,
    this.canBoDuyet,
    this.soKyHieuVbDuocPhucDap,
    this.ngayBanHanhVbGoc,
    this.donViGuiVbGoc,
    this.vanBanLienQuan,
    this.tenCanBoSoan,
    this.tenCoQuanBanHanh,
    this.maGoc,
    this.phongBanSoan,
    this.maCvLthong,
    this.ngonNgu,
    this.soTrangVb,
    this.fileVbPhatHanhHgg,
    this.vbCanGs,
    this.gsvbMaCdnvCanPd,
    this.gsvbTenCdnvCanPd,
    this.maVanBanDiKc,
    this.ngayDi,
    this.soDi,
    this.ngayLuu,
    this.maCtcbLuu,
    this.ngayDuyet,
    this.maCtcbDuyet,
    this.soNgayXuLyChung,
    this.hanXuLyChung,
    this.noiNhan,
    this.fileVanBanBs,
    this.yKienNguoiSoan,
    this.chuoiDonViNhan,
    this.ghiChu,
    this.maSoVanBanDi,
    this.maDonViQuanTri,
    this.maLinhVucVanBan,
    this.maLoaiVanBan,
    this.maCapDoMat,
    this.maCapDoKhan,
    this.maQuyTrinh,
    this.maVanBanGoc,
    this.traLoiVanBanDen,
    this.trangThaiVanBanDi,
    this.maVanBanDiCha,
    this.nam,
    this.maCtcbVanThu,
    this.maCbLuuCu,
    this.maCbDuyetCu,
    this.maMaDinhDanhCu,
    this.maLinhVucVanBanCu,
    this.maLoaiVanBanCu,
    this.maCapDoMatCu,
    this.maCapDoKhanCu,
    this.tenFileVanBanCu,
    this.maSoVanBanDiCu,
    this.phucDapCho,
    this.phucDapChoHscv,
    this.fileCongVanCu,
    this.soUuTien,
    this.vanBanCongKhai,
    this.guiKemVbGiay,
    this.daXoa,
    this.thoiHan,
    this.phucDapChoList,
    this.tenNguoiDuyet,
    this.coKySo,
    this.coKySaoY,
    this.coKyPhuLuc,
    this.canBoIgate,
    this.guiEdoc,
    this.guiNoiBoIoffice,
    this.vblqNgoaiHeThong,
    this.bussinessdoctype,
    this.fileQtxlHgg,
    this.kiemTraLoiPto,
    this.raSoatKySoHgg,
    this.maPakn,
    this.maSoVbDi,
    this.tenSoVbDi,
    this.canBoSoan,
    this.canBoTao,
    this.tenLinhVucVanBan,
    this.tenLoaiVanBan,
    this.coQuanBanHanh,
    this.maYeuCau,
    this.maXuLyDi,
    this.maCtcbGui,
    this.xem,
    this.ngayBanHanhN,
    this.diDongCanBo,
    this.maHoSoIgate,
    this.maHoSoIgateLgsp,
    this.maDinhDanhVb,
    this.vbdiDaCoChiDao,
    this.noiNhanVanBan,
    this.kiemTraTheThuc,
    this.fileDinhKem,
    this.trangThaiXuLyDi,
    this.capDoKhan,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        maVanBanKc: json["ma_van_ban_kc"],
        trichYeu: json["trich_yeu"],
        soKyHieu: json["so_ky_hieu"],
        nguoiKy: json["nguoi_ky"],
        ngayBanHanhOut: json["ngay_ban_hanh_out"],
        ngayBanHanh: json["ngay_ban_hanh"],
        ngayBanHanhVetXuLyEdoc: json["ngay_ban_hanh_vet_xu_ly_edoc"],
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        soBanPhatHanh: json["so_ban_phat_hanh"],
        fileVanBan: json["file_van_ban"],
        ngayTao: json["ngay_tao"],
        maCtcbTao: json["ma_ctcb_tao"],
        maDonViTao: json["ma_don_vi_tao"],
        maDonViQuanTriTao: json["ma_don_vi_quan_tri_tao"],
        loaiVanBanKhiMoiTao: json["loai_van_ban_khi_moi_tao"],
        trangThaiVanBan: json["trang_thai_van_ban"],
        noiLuuBanChinh: json["noi_luu_ban_chinh"],
        nguoiKyVanBan: json["nguoi_ky_van_ban"],
        canBoDuyet: json["can_bo_duyet"],
        soKyHieuVbDuocPhucDap: json["so_ky_hieu_vb_duoc_phuc_dap"],
        ngayBanHanhVbGoc: json["ngay_ban_hanh_vb_goc"],
        donViGuiVbGoc: json["don_vi_gui_vb_goc"],
        vanBanLienQuan: json["van_ban_lien_quan"],
        tenCanBoSoan: json["ten_can_bo_soan"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        maGoc: json["ma_goc"],
        phongBanSoan: json["phong_ban_soan"],
        maCvLthong: json["ma_cv_lthong"],
        ngonNgu: json["ngon_ngu"],
        soTrangVb: json["so_trang_vb"],
        fileVbPhatHanhHgg: json["file_vb_phat_hanh_hgg"],
        vbCanGs: json["vb_can_gs"],
        gsvbMaCdnvCanPd: json["gsvb_ma_cdnv_can_pd"],
        gsvbTenCdnvCanPd: json["gsvb_ten_cdnv_can_pd"],
        maVanBanDiKc: json["ma_van_ban_di_kc"],
        ngayDi: json["ngay_di"],
        soDi: json["so_di"],
        ngayLuu:
            json["ngay_luu"] == null ? null : DateTime.parse(json["ngay_luu"]),
        maCtcbLuu: json["ma_ctcb_luu"],
        ngayDuyet: json["ngay_duyet"],
        maCtcbDuyet: json["ma_ctcb_duyet"],
        soNgayXuLyChung: json["so_ngay_xu_ly_chung"],
        hanXuLyChung: json["han_xu_ly_chung"],
        noiNhan: json["noi_nhan"],
        fileVanBanBs: json["file_van_ban_bs"],
        yKienNguoiSoan: json["y_kien_nguoi_soan"],
        chuoiDonViNhan: json["chuoi_don_vi_nhan"],
        ghiChu: json["ghi_chu"],
        maSoVanBanDi: json["ma_so_van_ban_di"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDoMat: json["ma_cap_do_mat"],
        maCapDoKhan: json["ma_cap_do_khan"],
        maQuyTrinh: json["ma_quy_trinh"],
        maVanBanGoc: json["ma_van_ban_goc"],
        traLoiVanBanDen: json["tra_loi_van_ban_den"],
        trangThaiVanBanDi: json["trang_thai_van_ban_di"],
        maVanBanDiCha: json["ma_van_ban_di_cha"],
        nam: json["nam"],
        maCtcbVanThu: json["ma_ctcb_van_thu"],
        maCbLuuCu: json["ma_cb_luu_cu"],
        maCbDuyetCu: json["ma_cb_duyet_cu"],
        maMaDinhDanhCu: json["ma_ma_dinh_danh_cu"],
        maLinhVucVanBanCu: json["ma_linh_vuc_van_ban_cu"],
        maLoaiVanBanCu: json["ma_loai_van_ban_cu"],
        maCapDoMatCu: json["ma_cap_do_mat_cu"],
        maCapDoKhanCu: json["ma_cap_do_khan_cu"],
        tenFileVanBanCu: json["ten_file_van_ban_cu"],
        maSoVanBanDiCu: json["ma_so_van_ban_di_cu"],
        phucDapCho: json["phuc_dap_cho"],
        phucDapChoHscv: json["phuc_dap_cho_hscv"],
        fileCongVanCu: json["file_cong_van_cu"],
        soUuTien: json["so_uu_tien"],
        vanBanCongKhai: json["van_ban_cong_khai"],
        guiKemVbGiay: json["gui_kem_vb_giay"],
        daXoa: json["da_xoa"],
        thoiHan: json["thoi_han"],
        phucDapChoList: json["phuc_dap_cho_list"],
        tenNguoiDuyet: json["ten_nguoi_duyet"],
        coKySo: json["co_ky_so"],
        coKySaoY: json["co_ky_sao_y"],
        coKyPhuLuc: json["co_ky_phu_luc"],
        canBoIgate: json["can_bo_igate"],
        guiEdoc: json["gui_edoc"],
        guiNoiBoIoffice: json["gui_noi_bo_ioffice"],
        vblqNgoaiHeThong: json["vblq_ngoai_he_thong"],
        bussinessdoctype: json["bussinessdoctype"],
        fileQtxlHgg: json["file_qtxl_hgg"],
        kiemTraLoiPto: json["kiem_tra_loi_pto"],
        raSoatKySoHgg: json["ra_soat_ky_so_hgg"],
        maPakn: json["ma_pakn"],
        maSoVbDi: json["ma_so_vb_di"],
        tenSoVbDi: json["ten_so_vb_di"],
        canBoSoan: json["can_bo_soan"],
        canBoTao: json["can_bo_tao"],
        tenLinhVucVanBan: json["ten_linh_vuc_van_ban"],
        tenLoaiVanBan: json["ten_loai_van_ban"],
        coQuanBanHanh: json["co_quan_ban_hanh"],
        maYeuCau: json["ma_yeu_cau"],
        maXuLyDi: json["ma_xu_ly_di"],
        maCtcbGui: json["ma_ctcb_gui"],
        xem: json["xem"],
        ngayBanHanhN: json["ngay_ban_hanh_n"],
        diDongCanBo: json["di_dong_can_bo"],
        maHoSoIgate: json["ma_ho_so_igate"],
        maHoSoIgateLgsp: json["ma_ho_so_igate_lgsp"],
        maDinhDanhVb: json["ma_dinh_danh_vb"],
        vbdiDaCoChiDao: json["vbdi_da_co_chi_dao"],
        noiNhanVanBan: json["noi_nhan_van_ban"],
        kiemTraTheThuc: json["kiem_tra_the_thuc"],
        fileDinhKem:
            json["file_dinh_kem"] == 'null' ? null : json["file_dinh_kem"],
        trangThaiXuLyDi: json["trang_thai_xu_ly_di"],
        capDoKhan: json["cap_do_khan"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_kc": maVanBanKc,
        "trich_yeu": trichYeu,
        "so_ky_hieu": soKyHieu,
        "nguoi_ky": nguoiKy,
        "ngay_ban_hanh_out": ngayBanHanhOut,
        "ngay_ban_hanh": ngayBanHanh,
        "ngay_ban_hanh_vet_xu_ly_edoc": ngayBanHanhVetXuLyEdoc,
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "so_ban_phat_hanh": soBanPhatHanh,
        "file_van_ban": fileVanBan,
        "ngay_tao": ngayTao,
        "ma_ctcb_tao": maCtcbTao,
        "ma_don_vi_tao": maDonViTao,
        "ma_don_vi_quan_tri_tao": maDonViQuanTriTao,
        "loai_van_ban_khi_moi_tao": loaiVanBanKhiMoiTao,
        "trang_thai_van_ban": trangThaiVanBan,
        "noi_luu_ban_chinh": noiLuuBanChinh,
        "nguoi_ky_van_ban": nguoiKyVanBan,
        "can_bo_duyet": canBoDuyet,
        "so_ky_hieu_vb_duoc_phuc_dap": soKyHieuVbDuocPhucDap,
        "ngay_ban_hanh_vb_goc": ngayBanHanhVbGoc,
        "don_vi_gui_vb_goc": donViGuiVbGoc,
        "van_ban_lien_quan": vanBanLienQuan,
        "ten_can_bo_soan": tenCanBoSoan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "ma_goc": maGoc,
        "phong_ban_soan": phongBanSoan,
        "ma_cv_lthong": maCvLthong,
        "ngon_ngu": ngonNgu,
        "so_trang_vb": soTrangVb,
        "file_vb_phat_hanh_hgg": fileVbPhatHanhHgg,
        "vb_can_gs": vbCanGs,
        "gsvb_ma_cdnv_can_pd": gsvbMaCdnvCanPd,
        "gsvb_ten_cdnv_can_pd": gsvbTenCdnvCanPd,
        "ma_van_ban_di_kc": maVanBanDiKc,
        "ngay_di": ngayDi,
        "so_di": soDi,
        "ngay_luu": ngayLuu?.toIso8601String(),
        "ma_ctcb_luu": maCtcbLuu,
        "ngay_duyet": ngayDuyet,
        "ma_ctcb_duyet": maCtcbDuyet,
        "so_ngay_xu_ly_chung": soNgayXuLyChung,
        "han_xu_ly_chung": hanXuLyChung,
        "noi_nhan": noiNhan,
        "file_van_ban_bs": fileVanBanBs,
        "y_kien_nguoi_soan": yKienNguoiSoan,
        "chuoi_don_vi_nhan": chuoiDonViNhan,
        "ghi_chu": ghiChu,
        "ma_so_van_ban_di": maSoVanBanDi,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do_mat": maCapDoMat,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_quy_trinh": maQuyTrinh,
        "ma_van_ban_goc": maVanBanGoc,
        "tra_loi_van_ban_den": traLoiVanBanDen,
        "trang_thai_van_ban_di": trangThaiVanBanDi,
        "ma_van_ban_di_cha": maVanBanDiCha,
        "nam": nam,
        "ma_ctcb_van_thu": maCtcbVanThu,
        "ma_cb_luu_cu": maCbLuuCu,
        "ma_cb_duyet_cu": maCbDuyetCu,
        "ma_ma_dinh_danh_cu": maMaDinhDanhCu,
        "ma_linh_vuc_van_ban_cu": maLinhVucVanBanCu,
        "ma_loai_van_ban_cu": maLoaiVanBanCu,
        "ma_cap_do_mat_cu": maCapDoMatCu,
        "ma_cap_do_khan_cu": maCapDoKhanCu,
        "ten_file_van_ban_cu": tenFileVanBanCu,
        "ma_so_van_ban_di_cu": maSoVanBanDiCu,
        "phuc_dap_cho": phucDapCho,
        "phuc_dap_cho_hscv": phucDapChoHscv,
        "file_cong_van_cu": fileCongVanCu,
        "so_uu_tien": soUuTien,
        "van_ban_cong_khai": vanBanCongKhai,
        "gui_kem_vb_giay": guiKemVbGiay,
        "da_xoa": daXoa,
        "thoi_han": thoiHan,
        "phuc_dap_cho_list": phucDapChoList,
        "ten_nguoi_duyet": tenNguoiDuyet,
        "co_ky_so": coKySo,
        "co_ky_sao_y": coKySaoY,
        "co_ky_phu_luc": coKyPhuLuc,
        "can_bo_igate": canBoIgate,
        "gui_edoc": guiEdoc,
        "gui_noi_bo_ioffice": guiNoiBoIoffice,
        "vblq_ngoai_he_thong": vblqNgoaiHeThong,
        "bussinessdoctype": bussinessdoctype,
        "file_qtxl_hgg": fileQtxlHgg,
        "kiem_tra_loi_pto": kiemTraLoiPto,
        "ra_soat_ky_so_hgg": raSoatKySoHgg,
        "ma_pakn": maPakn,
        "ma_so_vb_di": maSoVbDi,
        "ten_so_vb_di": tenSoVbDi,
        "can_bo_soan": canBoSoan,
        "can_bo_tao": canBoTao,
        "ten_linh_vuc_van_ban": tenLinhVucVanBan,
        "ten_loai_van_ban": tenLoaiVanBan,
        "co_quan_ban_hanh": coQuanBanHanh,
        "ma_yeu_cau": maYeuCau,
        "ma_xu_ly_di": maXuLyDi,
        "ma_ctcb_gui": maCtcbGui,
        "xem": xem,
        "ngay_ban_hanh_n": ngayBanHanhN,
        "di_dong_can_bo": diDongCanBo,
        "ma_ho_so_igate": maHoSoIgate,
        "ma_ho_so_igate_lgsp": maHoSoIgateLgsp,
        "ma_dinh_danh_vb": maDinhDanhVb,
        "vbdi_da_co_chi_dao": vbdiDaCoChiDao,
        "noi_nhan_van_ban": noiNhanVanBan,
        "kiem_tra_the_thuc": kiemTraTheThuc,
        "file_dinh_kem": fileDinhKem,
        "trang_thai_xu_ly_di": trangThaiXuLyDi,
        "cap_do_khan": capDoKhan,
      };
}

// To parse this JSON data, do
//
//     final dsQtxlVbdi = dsQtxlVbdiFromJson(jsonString);

DsQtxlVbdi dsQtxlVbdiFromJson(String str) =>
    DsQtxlVbdi.fromJson(json.decode(str));

String dsQtxlVbdiToJson(DsQtxlVbdi data) => json.encode(data.toJson());

class DsQtxlVbdi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<QtxlVbdi>? data;

  DsQtxlVbdi({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsQtxlVbdi.fromJson(Map<String, dynamic> json) => DsQtxlVbdi(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<QtxlVbdi>.from(
                json["data"]!.map((x) => QtxlVbdi.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class QtxlVbdi {
  DateTime? ngayNhan;
  double? maCtcbGui;
  String? canBoGui;
  String? canBoNhan;
  dynamic noiDungChuyenCtcbKhac;
  dynamic noiDungXuLy;
  String? fileDinhKem;
  dynamic fileGopY;
  dynamic vblqNgoaiHeThong;
  double? maYeuCau;
  String? tenYeuCau;
  double? maXuLyDiKc;
  dynamic hanXuLy;
  dynamic ngayXuLy;
  dynamic soNgayConLai;
  dynamic soNgayTreHan;

  QtxlVbdi({
    this.ngayNhan,
    this.maCtcbGui,
    this.canBoGui,
    this.canBoNhan,
    this.noiDungChuyenCtcbKhac,
    this.noiDungXuLy,
    this.fileDinhKem,
    this.fileGopY,
    this.vblqNgoaiHeThong,
    this.maYeuCau,
    this.tenYeuCau,
    this.maXuLyDiKc,
    this.hanXuLy,
    this.ngayXuLy,
    this.soNgayConLai,
    this.soNgayTreHan,
  });

  factory QtxlVbdi.fromJson(Map<String, dynamic> json) => QtxlVbdi(
        ngayNhan: json["ngay_nhan"] == null
            ? null
            : DateTime.parse(json["ngay_nhan"]),
        maCtcbGui: json["ma_ctcb_gui"],
        canBoGui: json["can_bo_gui"],
        canBoNhan: json["can_bo_nhan"],
        noiDungChuyenCtcbKhac: json["noi_dung_chuyen_ctcb_khac"],
        noiDungXuLy: json["noi_dung_xu_ly"],
        fileDinhKem: json["file_dinh_kem"],
        fileGopY: json["file_gop_y"],
        vblqNgoaiHeThong: json["vblq_ngoai_he_thong"],
        maYeuCau: json["ma_yeu_cau"],
        tenYeuCau: json["ten_yeu_cau"],
        maXuLyDiKc: json["ma_xu_ly_di_kc"],
        hanXuLy: json["han_xu_ly"],
        ngayXuLy: json["ngay_xu_ly"],
        soNgayConLai: json["so_ngay_con_lai"],
        soNgayTreHan: json["so_ngay_tre_han"],
      );

  Map<String, dynamic> toJson() => {
        "ngay_nhan": ngayNhan?.toIso8601String(),
        "ma_ctcb_gui": maCtcbGui,
        "can_bo_gui": canBoGui,
        "can_bo_nhan": canBoNhan,
        "noi_dung_chuyen_ctcb_khac": noiDungChuyenCtcbKhac,
        "noi_dung_xu_ly": noiDungXuLy,
        "file_dinh_kem": fileDinhKem,
        "file_gop_y": fileGopY,
        "vblq_ngoai_he_thong": vblqNgoaiHeThong,
        "ma_yeu_cau": maYeuCau,
        "ten_yeu_cau": tenYeuCau,
        "ma_xu_ly_di_kc": maXuLyDiKc,
        "han_xu_ly": hanXuLy,
        "ngay_xu_ly": ngayXuLy,
        "so_ngay_con_lai": soNgayConLai,
        "so_ngay_tre_han": soNgayTreHan,
      };
}
