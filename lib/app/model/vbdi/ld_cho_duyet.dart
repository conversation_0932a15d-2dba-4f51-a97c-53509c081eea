class LDChoDuyet {
  int? totalPage;
  int? totalRow;
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<Data>? data;
  Param? param;

  LDChoDuyet(
      {this.totalPage,
      this.totalRow,
      this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  LDChoDuyet.fromJson(Map<String, dynamic> json) {
    totalPage = json['total_page'];
    totalRow = json['total_row'];
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total_page'] = this.totalPage;
    data['total_row'] = this.totalRow;
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class Data {
  int? maVanBanDiKc;
  String? ngayDi;
  int? guiKemVbGiay;
  Null? soDi;
  String? ngayLuu;
  int? maCtcbLuu;
  Null? ngayDuyet;
  int? maCtcbDuyet;
  Null? soNgayXuLyChung;
  Null? noiNhan;
  String? fileVanBanBs;
  Null? yKienNguoiSoan;
  Null? ghiChu;
  Null? maSoVanBanDi;
  int? maDonViQuanTri;
  int? maLinhVucVanBan;
  int? maLoaiVanBan;
  int? maCapDoMat;
  int? maCapDoKhan;
  int? maQuyTrinh;
  int? maVanBanGoc;
  Null? traLoiVanBanDen;
  int? trangThaiVanBanDi;
  Null? maVanBanDiCha;
  int? nam;
  Null? noiNhanVanBan;
  int? soUuTien;
  Null? maCtcbVanThu;
  int? maXuLyDi;
  String? ngayNhan;
  int? xem;
  String? trichYeu;
  String? canBoSoan;
  Null? soKyHieu;
  String? nguoiKy;
  Null? ngayBanHanh;
  Null? maCoQuanBanHanh;
  String? soBanPhatHanh;
  String? fileVanBan;
  String? ngayTao;
  int? maCtcbTao;
  int? maDonViTao;
  int? maDonViQuanTriTao;
  int? loaiVanBanKhiMoiTao;
  int? trangThaiVanBan;
  String? noiLuuBanChinh;
  Null? nguoiKyVanBan;
  Null? canBoDuyet;
  Null? soKyHieuVbDuocPhucDap;
  Null? ngayBanHanhVbGoc;
  Null? donViGuiVbGoc;
  Null? vanBanLienQuan;
  String? tenCoQuanBanHanh;
  String? soIoffice;
  Null? hanXuLy;
  String? tenCapDoKhan;
  String? tenCapDoMat;
  int? laKySo;
  Null? phucDapCho;
  Null? maHoSoIgate;
  int? r;

  Data(
      {this.maVanBanDiKc,
      this.ngayDi,
      this.guiKemVbGiay,
      this.soDi,
      this.ngayLuu,
      this.maCtcbLuu,
      this.ngayDuyet,
      this.maCtcbDuyet,
      this.soNgayXuLyChung,
      this.noiNhan,
      this.fileVanBanBs,
      this.yKienNguoiSoan,
      this.ghiChu,
      this.maSoVanBanDi,
      this.maDonViQuanTri,
      this.maLinhVucVanBan,
      this.maLoaiVanBan,
      this.maCapDoMat,
      this.maCapDoKhan,
      this.maQuyTrinh,
      this.maVanBanGoc,
      this.traLoiVanBanDen,
      this.trangThaiVanBanDi,
      this.maVanBanDiCha,
      this.nam,
      this.noiNhanVanBan,
      this.soUuTien,
      this.maCtcbVanThu,
      this.maXuLyDi,
      this.ngayNhan,
      this.xem,
      this.trichYeu,
      this.canBoSoan,
      this.soKyHieu,
      this.nguoiKy,
      this.ngayBanHanh,
      this.maCoQuanBanHanh,
      this.soBanPhatHanh,
      this.fileVanBan,
      this.ngayTao,
      this.maCtcbTao,
      this.maDonViTao,
      this.maDonViQuanTriTao,
      this.loaiVanBanKhiMoiTao,
      this.trangThaiVanBan,
      this.noiLuuBanChinh,
      this.nguoiKyVanBan,
      this.canBoDuyet,
      this.soKyHieuVbDuocPhucDap,
      this.ngayBanHanhVbGoc,
      this.donViGuiVbGoc,
      this.vanBanLienQuan,
      this.tenCoQuanBanHanh,
      this.soIoffice,
      this.hanXuLy,
      this.tenCapDoKhan,
      this.tenCapDoMat,
      this.laKySo,
      this.phucDapCho,
      this.maHoSoIgate,
      this.r});

  Data.fromJson(Map<String, dynamic> json) {
    maVanBanDiKc = json['ma_van_ban_di_kc'];
    ngayDi = json['ngay_di'];
    guiKemVbGiay = json['gui_kem_vb_giay'];
    soDi = json['so_di'];
    ngayLuu = json['ngay_luu'];
    maCtcbLuu = json['ma_ctcb_luu'];
    ngayDuyet = json['ngay_duyet'];
    maCtcbDuyet = json['ma_ctcb_duyet'];
    soNgayXuLyChung = json['so_ngay_xu_ly_chung'];
    noiNhan = json['noi_nhan'];
    fileVanBanBs = json['file_van_ban_bs'];
    yKienNguoiSoan = json['y_kien_nguoi_soan'];
    ghiChu = json['ghi_chu'];
    maSoVanBanDi = json['ma_so_van_ban_di'];
    maDonViQuanTri = json['ma_don_vi_quan_tri'];
    maLinhVucVanBan = json['ma_linh_vuc_van_ban'];
    maLoaiVanBan = json['ma_loai_van_ban'];
    maCapDoMat = json['ma_cap_do_mat'];
    maCapDoKhan = json['ma_cap_do_khan'];
    maQuyTrinh = json['ma_quy_trinh'];
    maVanBanGoc = json['ma_van_ban_goc'];
    traLoiVanBanDen = json['tra_loi_van_ban_den'];
    trangThaiVanBanDi = json['trang_thai_van_ban_di'];
    maVanBanDiCha = json['ma_van_ban_di_cha'];
    nam = json['nam'];
    noiNhanVanBan = json['noi_nhan_van_ban'];
    soUuTien = json['so_uu_tien'];
    maCtcbVanThu = json['ma_ctcb_van_thu'];
    maXuLyDi = json['ma_xu_ly_di'];
    ngayNhan = json['ngay_nhan'];
    xem = json['xem'];
    trichYeu = json['trich_yeu'];
    canBoSoan = json['can_bo_soan'];
    soKyHieu = json['so_ky_hieu'];
    nguoiKy = json['nguoi_ky'];
    ngayBanHanh = json['ngay_ban_hanh'];
    maCoQuanBanHanh = json['ma_co_quan_ban_hanh'];
    soBanPhatHanh = json['so_ban_phat_hanh'];
    fileVanBan = json['file_van_ban'];
    ngayTao = json['ngay_tao'];
    maCtcbTao = json['ma_ctcb_tao'];
    maDonViTao = json['ma_don_vi_tao'];
    maDonViQuanTriTao = json['ma_don_vi_quan_tri_tao'];
    loaiVanBanKhiMoiTao = json['loai_van_ban_khi_moi_tao'];
    trangThaiVanBan = json['trang_thai_van_ban'];
    noiLuuBanChinh = json['noi_luu_ban_chinh'];
    nguoiKyVanBan = json['nguoi_ky_van_ban'];
    canBoDuyet = json['can_bo_duyet'];
    soKyHieuVbDuocPhucDap = json['so_ky_hieu_vb_duoc_phuc_dap'];
    ngayBanHanhVbGoc = json['ngay_ban_hanh_vb_goc'];
    donViGuiVbGoc = json['don_vi_gui_vb_goc'];
    vanBanLienQuan = json['van_ban_lien_quan'];
    tenCoQuanBanHanh = json['ten_co_quan_ban_hanh'];
    soIoffice = json['so_ioffice'];
    hanXuLy = json['han_xu_ly'];
    tenCapDoKhan = json['ten_cap_do_khan'];
    tenCapDoMat = json['ten_cap_do_mat'];
    laKySo = json['la_ky_so'];
    phucDapCho = json['phuc_dap_cho'];
    maHoSoIgate = json['ma_ho_so_igate'];
    r = json['r'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_van_ban_di_kc'] = this.maVanBanDiKc;
    data['ngay_di'] = this.ngayDi;
    data['gui_kem_vb_giay'] = this.guiKemVbGiay;
    data['so_di'] = this.soDi;
    data['ngay_luu'] = this.ngayLuu;
    data['ma_ctcb_luu'] = this.maCtcbLuu;
    data['ngay_duyet'] = this.ngayDuyet;
    data['ma_ctcb_duyet'] = this.maCtcbDuyet;
    data['so_ngay_xu_ly_chung'] = this.soNgayXuLyChung;
    data['noi_nhan'] = this.noiNhan;
    data['file_van_ban_bs'] = this.fileVanBanBs;
    data['y_kien_nguoi_soan'] = this.yKienNguoiSoan;
    data['ghi_chu'] = this.ghiChu;
    data['ma_so_van_ban_di'] = this.maSoVanBanDi;
    data['ma_don_vi_quan_tri'] = this.maDonViQuanTri;
    data['ma_linh_vuc_van_ban'] = this.maLinhVucVanBan;
    data['ma_loai_van_ban'] = this.maLoaiVanBan;
    data['ma_cap_do_mat'] = this.maCapDoMat;
    data['ma_cap_do_khan'] = this.maCapDoKhan;
    data['ma_quy_trinh'] = this.maQuyTrinh;
    data['ma_van_ban_goc'] = this.maVanBanGoc;
    data['tra_loi_van_ban_den'] = this.traLoiVanBanDen;
    data['trang_thai_van_ban_di'] = this.trangThaiVanBanDi;
    data['ma_van_ban_di_cha'] = this.maVanBanDiCha;
    data['nam'] = this.nam;
    data['noi_nhan_van_ban'] = this.noiNhanVanBan;
    data['so_uu_tien'] = this.soUuTien;
    data['ma_ctcb_van_thu'] = this.maCtcbVanThu;
    data['ma_xu_ly_di'] = this.maXuLyDi;
    data['ngay_nhan'] = this.ngayNhan;
    data['xem'] = this.xem;
    data['trich_yeu'] = this.trichYeu;
    data['can_bo_soan'] = this.canBoSoan;
    data['so_ky_hieu'] = this.soKyHieu;
    data['nguoi_ky'] = this.nguoiKy;
    data['ngay_ban_hanh'] = this.ngayBanHanh;
    data['ma_co_quan_ban_hanh'] = this.maCoQuanBanHanh;
    data['so_ban_phat_hanh'] = this.soBanPhatHanh;
    data['file_van_ban'] = this.fileVanBan;
    data['ngay_tao'] = this.ngayTao;
    data['ma_ctcb_tao'] = this.maCtcbTao;
    data['ma_don_vi_tao'] = this.maDonViTao;
    data['ma_don_vi_quan_tri_tao'] = this.maDonViQuanTriTao;
    data['loai_van_ban_khi_moi_tao'] = this.loaiVanBanKhiMoiTao;
    data['trang_thai_van_ban'] = this.trangThaiVanBan;
    data['noi_luu_ban_chinh'] = this.noiLuuBanChinh;
    data['nguoi_ky_van_ban'] = this.nguoiKyVanBan;
    data['can_bo_duyet'] = this.canBoDuyet;
    data['so_ky_hieu_vb_duoc_phuc_dap'] = this.soKyHieuVbDuocPhucDap;
    data['ngay_ban_hanh_vb_goc'] = this.ngayBanHanhVbGoc;
    data['don_vi_gui_vb_goc'] = this.donViGuiVbGoc;
    data['van_ban_lien_quan'] = this.vanBanLienQuan;
    data['ten_co_quan_ban_hanh'] = this.tenCoQuanBanHanh;
    data['so_ioffice'] = this.soIoffice;
    data['han_xu_ly'] = this.hanXuLy;
    data['ten_cap_do_khan'] = this.tenCapDoKhan;
    data['ten_cap_do_mat'] = this.tenCapDoMat;
    data['la_ky_so'] = this.laKySo;
    data['phuc_dap_cho'] = this.phucDapCho;
    data['ma_ho_so_igate'] = this.maHoSoIgate;
    data['r'] = this.r;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
