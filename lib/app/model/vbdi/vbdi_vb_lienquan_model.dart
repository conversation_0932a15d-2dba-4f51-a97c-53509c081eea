class VbdiVBLienQuanDinhKem {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DetailVbdiVBLienQuanDinhKem>? data;
  Param? param;

  VbdiVBLienQuanDinhKem(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  VbdiVBLienQuanDinhKem.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DetailVbdiVBLienQuanDinhKem>[];
      json['data'].forEach((v) {
        data!.add(new DetailVbdiVBLienQuanDinhKem.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DetailVbdiVBLienQuanDinhKem {
  double? maVanBanKc;
  String? trichYeu;
  String? soKyHieu;
  double? maVanBanLienQuan;
  String? fileVanBan;
  String? fileVanBanShow;

  DetailVbdiVBLienQuanDinhKem(
      {this.maVanBanKc,
      this.trichYeu,
      this.soKyHieu,
      this.maVanBanLienQuan,
      this.fileVanBan,
      this.fileVanBanShow});

  DetailVbdiVBLienQuanDinhKem.fromJson(Map<String, dynamic> json) {
    maVanBanKc = json['ma_van_ban_kc'];
    trichYeu = json['trich_yeu'];
    soKyHieu = json['so_ky_hieu'];
    maVanBanLienQuan = json['ma_van_ban_lien_quan'];
    fileVanBan = json['file_van_ban'];
    fileVanBanShow = json['file_van_ban_show'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ma_van_ban_kc'] = this.maVanBanKc;
    data['trich_yeu'] = this.trichYeu;
    data['so_ky_hieu'] = this.soKyHieu;
    data['ma_van_ban_lien_quan'] = this.maVanBanLienQuan;
    data['file_van_ban'] = this.fileVanBan;
    data['file_van_ban_show'] = this.fileVanBanShow;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
