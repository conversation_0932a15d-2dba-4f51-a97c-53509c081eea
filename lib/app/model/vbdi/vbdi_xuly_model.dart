import 'dart:convert';

CbDsVanThu cbDsVanThuFromJson(String str) =>
    CbDsVanThu.fromJson(json.decode(str));

String cbDsVanThuToJson(CbDsVanThu data) => json.encode(data.toJson());

class CbDsVanThu {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsVanThu>? data;

  CbDsVanThu({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory CbDsVanThu.fromJson(Map<String, dynamic> json) => CbDsVanThu(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsVanThu>.from(
                json["data"]!.map((x) => DsVanThu.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsVanThu {
  int? maCtcbKc;
  int? maCanBoKc;
  String? hoVaTenCanBo;
  String? tenDonVi;
  String? diDongCanBo;
  String? tenChucVu;
  String? emailCanBo;
  int? sttCanBo;

  DsVanThu({
    this.maCtcbKc,
    this.maCanBoKc,
    this.hoVaTenCanBo,
    this.tenDonVi,
    this.diDongCanBo,
    this.tenChucVu,
    this.emailCanBo,
    this.sttCanBo,
  });

  factory DsVanThu.fromJson(Map<String, dynamic> json) => DsVanThu(
        maCtcbKc:
            json["ma_ctcb_kc"] == null ? null : json["ma_ctcb_kc"].toInt(),
        maCanBoKc:
            json["ma_can_bo_kc"] == null ? null : json["ma_can_bo_kc"].toInt(),
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        tenDonVi: json["ten_don_vi"],
        diDongCanBo: json["di_dong_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        emailCanBo: json["email_can_bo"],
        sttCanBo:
            json["stt_can_bo"] == null ? null : json["stt_can_bo"].toInt(),
      );

  Map<String, dynamic> toJson() => {
        "ma_ctcb_kc": maCtcbKc,
        "ma_can_bo_kc": maCanBoKc,
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "ten_don_vi": tenDonVi,
        "di_dong_can_bo": diDongCanBo,
        "ten_chuc_vu": tenChucVu,
        "email_can_bo": emailCanBo,
        "stt_can_bo": sttCanBo,
      };
}

// To parse this JSON data, do
//
//     final cbDsLanhDaoKhacVbdi = cbDsLanhDaoKhacVbdiFromJson(jsonString);

CbDsLanhDaoKhacVbdi cbDsLanhDaoKhacVbdiFromJson(String str) =>
    CbDsLanhDaoKhacVbdi.fromJson(json.decode(str));

String cbDsLanhDaoKhacVbdiToJson(CbDsLanhDaoKhacVbdi data) =>
    json.encode(data.toJson());

class CbDsLanhDaoKhacVbdi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsLanhDaoKhacVbdi>? data;

  CbDsLanhDaoKhacVbdi({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory CbDsLanhDaoKhacVbdi.fromJson(Map<String, dynamic> json) =>
      CbDsLanhDaoKhacVbdi(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsLanhDaoKhacVbdi>.from(
                json["data"]!.map((x) => DsLanhDaoKhacVbdi.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsLanhDaoKhacVbdi {
  double? ma;
  double? maCanBoKc;
  String? ten;
  String? tenDonVi;
  String? diDongCanBo;
  String? tenChucVu;
  String? emailCanBo;
  double? sttCanBo;
  String? tenNhomQuyen;

  DsLanhDaoKhacVbdi({
    this.ma,
    this.maCanBoKc,
    this.ten,
    this.tenDonVi,
    this.diDongCanBo,
    this.tenChucVu,
    this.emailCanBo,
    this.sttCanBo,
    this.tenNhomQuyen,
  });

  factory DsLanhDaoKhacVbdi.fromJson(Map<String, dynamic> json) =>
      DsLanhDaoKhacVbdi(
        ma: json["ma"],
        maCanBoKc: json["ma_can_bo_kc"],
        ten: json["ten"],
        tenDonVi: json["ten_don_vi"],
        diDongCanBo: json["di_dong_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        emailCanBo: json["email_can_bo"],
        sttCanBo: json["stt_can_bo"],
        tenNhomQuyen: json["ten_nhom_quyen"],
      );

  Map<String, dynamic> toJson() => {
        "ma": ma,
        "ma_can_bo_kc": maCanBoKc,
        "ten": ten,
        "ten_don_vi": tenDonVi,
        "di_dong_can_bo": diDongCanBo,
        "ten_chuc_vu": tenChucVu,
        "email_can_bo": emailCanBo,
        "stt_can_bo": sttCanBo,
        "ten_nhom_quyen": tenNhomQuyen,
      };
}

// To parse this JSON data, do
//
//     final cbDsChuyenVienVbdi = cbDsChuyenVienVbdiFromJson(jsonString);

CbDsChuyenVienVbdi cbDsChuyenVienVbdiFromJson(String str) =>
    CbDsChuyenVienVbdi.fromJson(json.decode(str));

String cbDsChuyenVienVbdiToJson(CbDsChuyenVienVbdi data) =>
    json.encode(data.toJson());

class CbDsChuyenVienVbdi {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DsChuyenVienVbdi>? data;

  CbDsChuyenVienVbdi({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory CbDsChuyenVienVbdi.fromJson(Map<String, dynamic> json) =>
      CbDsChuyenVienVbdi(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<DsChuyenVienVbdi>.from(
                json["data"]!.map((x) => DsChuyenVienVbdi.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class DsChuyenVienVbdi {
  String? hoVaTenCanBo;
  String? tenChucVu;
  String? tenDonVi;
  double? maCtcbKc;
  String? diDongCanBo;
  String? emailCanBo;

  DsChuyenVienVbdi({
    this.hoVaTenCanBo,
    this.tenChucVu,
    this.tenDonVi,
    this.maCtcbKc,
    this.diDongCanBo,
    this.emailCanBo,
  });

  factory DsChuyenVienVbdi.fromJson(Map<String, dynamic> json) =>
      DsChuyenVienVbdi(
        hoVaTenCanBo: json["ho_va_ten_can_bo"],
        tenChucVu: json["ten_chuc_vu"],
        tenDonVi: json["ten_don_vi"],
        maCtcbKc: json["ma_ctcb_kc"],
        diDongCanBo: json["di_dong_can_bo"],
        emailCanBo: json["email_can_bo"],
      );

  Map<String, dynamic> toJson() => {
        "ho_va_ten_can_bo": hoVaTenCanBo,
        "ten_chuc_vu": tenChucVu,
        "ten_don_vi": tenDonVi,
        "ma_ctcb_kc": maCtcbKc,
        "di_dong_can_bo": diDongCanBo,
        "email_can_bo": emailCanBo,
      };
}
