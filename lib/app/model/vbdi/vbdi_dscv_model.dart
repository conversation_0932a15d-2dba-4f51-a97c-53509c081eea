// To parse this JSON data, do
//
//     final dsCvXuLyVbdi = dsCvXuLyVbdiFromJson(jsonString);

import 'dart:convert';

import 'package:intl/intl.dart';

DsCvXuLyVbdi dsCvXuLyVbdiFromJson(String str) =>
    DsCvXuLyVbdi.fromJson(json.decode(str));

String dsCvXuLyVbdiToJson(DsCvXuLyVbdi data) => json.encode(data.toJson());

class DsCvXuLyVbdi {
  int? totalPage;
  int? totalRow;
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<CvXuLyVbdi>? data;

  DsCvXuLyVbdi({
    this.totalPage,
    this.totalRow,
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory DsCvXuLyVbdi.fromJson(Map<String, dynamic> json) => DsCvXuLyVbdi(
        totalPage: json["total_page"],
        totalRow: json["total_row"],
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<CvXuLyVbdi>.from(
                json["data"]!.map((x) => CvXuLyVbdi.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_page": totalPage,
        "total_row": totalRow,
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class CvXuLyVbdi {
  double? maVanBanDiKc;
  dynamic ngayDi;
  dynamic soDi;
  DateTime? ngayLuu;
  double? maCtcbLuu;
  dynamic ngayDuyet;
  double? maCtcbDuyet;
  dynamic soNgayXuLyChung;
  dynamic noiNhan;
  String? fileVanBanBs;
  dynamic yKienNguoiSoan;
  dynamic ghiChu;
  dynamic maSoVanBanDi;
  double? maDonViQuanTri;
  double? maLinhVucVanBan;
  double? maLoaiVanBan;
  double? maCapDoMat;
  double? maCapDoKhan;
  double? maQuyTrinh;
  double? maVanBanGoc;
  dynamic traLoiVanBanDen;
  double? trangThaiVanBanDi;
  dynamic maVanBanDiCha;
  double? nam;
  dynamic maCtcbVanThu;
  double? gopY;
  double? maXuLyDi;
  String? ngayNhan;
  double? xem;
  String? trichYeu;
  String? canBoSoan;
  dynamic soKyHieu;
  String? nguoiKy;
  dynamic ngayBanHanh;
  dynamic maCoQuanBanHanh;
  String? soBanPhatHanh;
  String? fileVanBan;
  DateTime? ngayTao;
  double? maCtcbTao;
  double? maDonViTao;
  double? maDonViQuanTriTao;
  double? loaiVanBanKhiMoiTao;
  double? trangThaiVanBan;
  String? noiLuuBanChinh;
  dynamic nguoiKyVanBan;
  dynamic canBoDuyet;
  dynamic soKyHieuVbDuocPhucDap;
  dynamic ngayBanHanhVbGoc;
  dynamic donViGuiVbGoc;
  dynamic vanBanLienQuan;
  String? tenCoQuanBanHanh;
  String? soIoffice;
  String? tenCapDoKhan;
  String? tenCapDoMat;
  double? maYeuCau;
  String? phongBanSoan;
  String? tenCanBoSoan;
  double? maVanBanKc;
  double? r;

  CvXuLyVbdi({
    this.maVanBanDiKc,
    this.ngayDi,
    this.soDi,
    this.ngayLuu,
    this.maCtcbLuu,
    this.ngayDuyet,
    this.maCtcbDuyet,
    this.soNgayXuLyChung,
    this.noiNhan,
    this.fileVanBanBs,
    this.yKienNguoiSoan,
    this.ghiChu,
    this.maSoVanBanDi,
    this.maDonViQuanTri,
    this.maLinhVucVanBan,
    this.maLoaiVanBan,
    this.maCapDoMat,
    this.maCapDoKhan,
    this.maQuyTrinh,
    this.maVanBanGoc,
    this.traLoiVanBanDen,
    this.trangThaiVanBanDi,
    this.maVanBanDiCha,
    this.nam,
    this.maCtcbVanThu,
    this.gopY,
    this.maXuLyDi,
    this.ngayNhan,
    this.xem,
    this.trichYeu,
    this.canBoSoan,
    this.soKyHieu,
    this.nguoiKy,
    this.ngayBanHanh,
    this.maCoQuanBanHanh,
    this.soBanPhatHanh,
    this.fileVanBan,
    this.ngayTao,
    this.maCtcbTao,
    this.maDonViTao,
    this.maDonViQuanTriTao,
    this.loaiVanBanKhiMoiTao,
    this.trangThaiVanBan,
    this.noiLuuBanChinh,
    this.nguoiKyVanBan,
    this.canBoDuyet,
    this.soKyHieuVbDuocPhucDap,
    this.ngayBanHanhVbGoc,
    this.donViGuiVbGoc,
    this.vanBanLienQuan,
    this.tenCoQuanBanHanh,
    this.soIoffice,
    this.tenCapDoKhan,
    this.tenCapDoMat,
    this.maYeuCau,
    this.phongBanSoan,
    this.tenCanBoSoan,
    this.maVanBanKc,
    this.r,
  });

  factory CvXuLyVbdi.fromJson(Map<String, dynamic> json) => CvXuLyVbdi(
        maVanBanDiKc: json["ma_van_ban_di_kc"],
        ngayDi: json["ngay_di"],
        soDi: json["so_di"],
        ngayLuu:
            json["ngay_luu"] == null ? null : DateTime.parse(json["ngay_luu"]),
        maCtcbLuu: json["ma_ctcb_luu"],
        ngayDuyet: json["ngay_duyet"],
        maCtcbDuyet: json["ma_ctcb_duyet"],
        soNgayXuLyChung: json["so_ngay_xu_ly_chung"],
        noiNhan: json["noi_nhan"],
        fileVanBanBs: json["file_van_ban_bs"],
        yKienNguoiSoan: json["y_kien_nguoi_soan"],
        ghiChu: json["ghi_chu"],
        maSoVanBanDi: json["ma_so_van_ban_di"],
        maDonViQuanTri: json["ma_don_vi_quan_tri"],
        maLinhVucVanBan: json["ma_linh_vuc_van_ban"],
        maLoaiVanBan: json["ma_loai_van_ban"],
        maCapDoMat: json["ma_cap_do_mat"],
        maCapDoKhan: json["ma_cap_do_khan"],
        maQuyTrinh: json["ma_quy_trinh"],
        maVanBanGoc: json["ma_van_ban_goc"],
        traLoiVanBanDen: json["tra_loi_van_ban_den"],
        trangThaiVanBanDi: json["trang_thai_van_ban_di"],
        maVanBanDiCha: json["ma_van_ban_di_cha"],
        nam: json["nam"],
        maCtcbVanThu: json["ma_ctcb_van_thu"],
        gopY: json["gop_y"],
        maXuLyDi: json["ma_xu_ly_di"],
        ngayNhan: json["ngay_nhan"] == null
            ? null
            : DateFormat('dd/MM/yyyy HH:mm:ss')
                .format(DateTime.parse(json["ngay_nhan"])),
        xem: json["xem"],
        trichYeu: json["trich_yeu"],
        canBoSoan: json["can_bo_soan"],
        soKyHieu: json["so_ky_hieu"],
        nguoiKy: json["nguoi_ky"],
        ngayBanHanh: json["ngay_ban_hanh"],
        maCoQuanBanHanh: json["ma_co_quan_ban_hanh"],
        soBanPhatHanh: json["so_ban_phat_hanh"],
        fileVanBan: json["file_van_ban"],
        ngayTao:
            json["ngay_tao"] == null ? null : DateTime.parse(json["ngay_tao"]),
        maCtcbTao: json["ma_ctcb_tao"],
        maDonViTao: json["ma_don_vi_tao"],
        maDonViQuanTriTao: json["ma_don_vi_quan_tri_tao"],
        loaiVanBanKhiMoiTao: json["loai_van_ban_khi_moi_tao"],
        trangThaiVanBan: json["trang_thai_van_ban"],
        noiLuuBanChinh: json["noi_luu_ban_chinh"],
        nguoiKyVanBan: json["nguoi_ky_van_ban"],
        canBoDuyet: json["can_bo_duyet"],
        soKyHieuVbDuocPhucDap: json["so_ky_hieu_vb_duoc_phuc_dap"],
        ngayBanHanhVbGoc: json["ngay_ban_hanh_vb_goc"],
        donViGuiVbGoc: json["don_vi_gui_vb_goc"],
        vanBanLienQuan: json["van_ban_lien_quan"],
        tenCoQuanBanHanh: json["ten_co_quan_ban_hanh"],
        soIoffice: json["so_ioffice"],
        tenCapDoKhan: json["ten_cap_do_khan"],
        tenCapDoMat: json["ten_cap_do_mat"],
        maYeuCau: json["ma_yeu_cau"],
        phongBanSoan: json["phong_ban_soan"],
        tenCanBoSoan: json["ten_can_bo_soan"],
        maVanBanKc: json["ma_van_ban_kc"],
        r: json["r"],
      );

  Map<String, dynamic> toJson() => {
        "ma_van_ban_di_kc": maVanBanDiKc,
        "ngay_di": ngayDi,
        "so_di": soDi,
        "ngay_luu": ngayLuu?.toIso8601String(),
        "ma_ctcb_luu": maCtcbLuu,
        "ngay_duyet": ngayDuyet,
        "ma_ctcb_duyet": maCtcbDuyet,
        "so_ngay_xu_ly_chung": soNgayXuLyChung,
        "noi_nhan": noiNhan,
        "file_van_ban_bs": fileVanBanBs,
        "y_kien_nguoi_soan": yKienNguoiSoan,
        "ghi_chu": ghiChu,
        "ma_so_van_ban_di": maSoVanBanDi,
        "ma_don_vi_quan_tri": maDonViQuanTri,
        "ma_linh_vuc_van_ban": maLinhVucVanBan,
        "ma_loai_van_ban": maLoaiVanBan,
        "ma_cap_do_mat": maCapDoMat,
        "ma_cap_do_khan": maCapDoKhan,
        "ma_quy_trinh": maQuyTrinh,
        "ma_van_ban_goc": maVanBanGoc,
        "tra_loi_van_ban_den": traLoiVanBanDen,
        "trang_thai_van_ban_di": trangThaiVanBanDi,
        "ma_van_ban_di_cha": maVanBanDiCha,
        "nam": nam,
        "ma_ctcb_van_thu": maCtcbVanThu,
        "gop_y": gopY,
        "ma_xu_ly_di": maXuLyDi,
        "ngay_nhan": ngayNhan,
        "xem": xem,
        "trich_yeu": trichYeu,
        "can_bo_soan": canBoSoan,
        "so_ky_hieu": soKyHieu,
        "nguoi_ky": nguoiKy,
        "ngay_ban_hanh": ngayBanHanh,
        "ma_co_quan_ban_hanh": maCoQuanBanHanh,
        "so_ban_phat_hanh": soBanPhatHanh,
        "file_van_ban": fileVanBan,
        "ngay_tao": ngayTao?.toIso8601String(),
        "ma_ctcb_tao": maCtcbTao,
        "ma_don_vi_tao": maDonViTao,
        "ma_don_vi_quan_tri_tao": maDonViQuanTriTao,
        "loai_van_ban_khi_moi_tao": loaiVanBanKhiMoiTao,
        "trang_thai_van_ban": trangThaiVanBan,
        "noi_luu_ban_chinh": noiLuuBanChinh,
        "nguoi_ky_van_ban": nguoiKyVanBan,
        "can_bo_duyet": canBoDuyet,
        "so_ky_hieu_vb_duoc_phuc_dap": soKyHieuVbDuocPhucDap,
        "ngay_ban_hanh_vb_goc": ngayBanHanhVbGoc,
        "don_vi_gui_vb_goc": donViGuiVbGoc,
        "van_ban_lien_quan": vanBanLienQuan,
        "ten_co_quan_ban_hanh": tenCoQuanBanHanh,
        "so_ioffice": soIoffice,
        "ten_cap_do_khan": tenCapDoKhan,
        "ten_cap_do_mat": tenCapDoMat,
        "ma_yeu_cau": maYeuCau,
        "phong_ban_soan": phongBanSoan,
        "ten_can_bo_soan": tenCanBoSoan,
        "ma_van_ban_kc": maVanBanKc,
        "r": r,
      };
}
