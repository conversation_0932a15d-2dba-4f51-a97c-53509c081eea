class ModelThamSoSSO {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<DataThamSoSSO>? data;
  Param? param;

  ModelThamSoSSO(
      {this.success,
      this.message,
      this.storeName,
      this.storeType,
      this.data,
      this.param});

  ModelThamSoSSO.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    storeName = json['store_name'];
    storeType = json['store_type'];
    if (json['data'] != null) {
      data = <DataThamSoSSO>[];
      json['data'].forEach((v) {
        data!.add(new DataThamSoSSO.fromJson(v));
      });
    }
    param = json['param'] != null ? new Param.fromJson(json['param']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    data['store_name'] = this.storeName;
    data['store_type'] = this.storeType;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.param != null) {
      data['param'] = this.param!.toJson();
    }
    return data;
  }
}

class DataThamSoSSO {
  String? giaTriMacDinh;

  DataThamSoSSO({this.giaTriMacDinh});

  DataThamSoSSO.fromJson(Map<String, dynamic> json) {
    giaTriMacDinh = json['gia_tri_mac_dinh'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['gia_tri_mac_dinh'] = this.giaTriMacDinh;
    return data;
  }
}

class Param {
  List<String>? parameternames;

  Param({this.parameternames});

  Param.fromJson(Map<String, dynamic> json) {
    parameternames = json['parameternames'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['parameternames'] = this.parameternames;
    return data;
  }
}
