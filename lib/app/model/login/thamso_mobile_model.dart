// To parse this JSON data, do
//
//     final thamSoMobile = thamSoMobileFromJson(jsonString);

import 'dart:convert';

ThamSoMobile thamSoMobileFromJson(String str) =>
    ThamSoMobile.fromJson(json.decode(str));

String thamSoMobileToJson(ThamSoMobile data) => json.encode(data.toJson());

class ThamSoMobile {
  bool? success;
  String? message;
  String? storeName;
  int? storeType;
  List<TsMobile>? data;

  ThamSoMobile({
    this.success,
    this.message,
    this.storeName,
    this.storeType,
    this.data,
  });

  factory ThamSoMobile.fromJson(Map<String, dynamic> json) => ThamSoMobile(
        success: json["success"],
        message: json["message"],
        storeName: json["store_name"],
        storeType: json["store_type"],
        data: json["data"] == null
            ? []
            : List<TsMobile>.from(
                json["data"]!.map((x) => TsMobile.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "store_name": storeName,
        "store_type": storeType,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class TsMobile {
  String? maThamSo;
  String? tenThamSo;
  String? giaTriThamSo;
  String? moTaThamSo;

  TsMobile({
    this.maThamSo,
    this.tenThamSo,
    this.giaTriThamSo,
    this.moTaThamSo,
  });

  factory TsMobile.fromJson(Map<String, dynamic> json) => TsMobile(
        maThamSo: json["ma_tham_so"],
        tenThamSo: json["ten_tham_so"],
        giaTriThamSo: json["gia_tri_tham_so"],
        moTaThamSo: json["mo_ta_tham_so"],
      );

  Map<String, dynamic> toJson() => {
        "ma_tham_so": maThamSo,
        "ten_tham_so": tenThamSo,
        "gia_tri_tham_so": giaTriThamSo,
        "mo_ta_tham_so": moTaThamSo,
      };
}
