// To parse this JSON data, do
//
//     final dsNhacViecCb = dsNhacViecCbFromJson(jsonString);

import 'dart:convert';

List<DsNhacViecCb> dsNhacViecCbFromJson(String str) => List<DsNhacViecCb>.from(
    json.decode(str).map((x) => DsNhacViecCb.fromJson(x)));

String dsNhacViecCbToJson(List<DsNhacViecCb> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DsNhacViecCb {
  String parentName;
  List<String> parentKey;
  String color;
  List<Child> childs;

  DsNhacViecCb({
    required this.parentName,
    required this.parentKey,
    required this.color,
    required this.childs,
  });

  factory DsNhacViecCb.fromJson(Map<String, dynamic> json) => DsNhacViec<PERSON>b(
        parentName: json["parent_name"],
        parentKey: List<String>.from(json["parent_key"].map((x) => x)),
        color: json["color"],
        childs: List<Child>.from(json["childs"].map((x) => Child.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "parent_name": parentName,
        "parent_key": List<dynamic>.from(parentKey.map((x) => x)),
        "color": color,
        "childs": List<dynamic>.from(childs.map((x) => x.toJson())),
      };
}

class Child {
  String url;
  String chucNang;
  String tenHienThi;
  String icon;
  int? tong;

  Child({
    required this.url,
    required this.chucNang,
    required this.tenHienThi,
    required this.icon,
    required this.tong,
  });

  factory Child.fromJson(Map<String, dynamic> json) => Child(
        url: json["url"],
        chucNang: json["chuc_nang"],
        tenHienThi: json["ten_hien_thi"],
        icon: json["icon"],
        tong: json["tong"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "chuc_nang": chucNang,
        "ten_hien_thi": tenHienThi,
        "icon": icon,
        "tong": tong,
      };
}

class ItemNhacViec {
  String key;
  String tenNhacViec;
  int indexNhacViec;
  String iconSvg;

  ItemNhacViec({
    required this.key,
    required this.tenNhacViec,
    required this.indexNhacViec,
    required this.iconSvg,
  });
  factory ItemNhacViec.fromJson(Map<String, dynamic> json) => ItemNhacViec(
        key: json["key"],
        tenNhacViec: json["tenNhacViec"],
        indexNhacViec: json["indexNhacViec"],
        iconSvg: json["iconSvg"],
      );

  Map<String, dynamic> toJson() => {
        "Key": key,
        "tenNhacViec": tenNhacViec,
        "indexNhacViec": indexNhacViec,
        "iconSvg": iconSvg,
      };
}
