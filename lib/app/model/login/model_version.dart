class ModelVersion {
  List<Versions>? versions;

  ModelVersion({this.versions});

  ModelVersion.fromJson(Map<String, dynamic> json) {
    if (json['versions'] != null) {
      versions = <Versions>[];
      json['versions'].forEach((v) {
        versions!.add(new Versions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.versions != null) {
      data['versions'] = this.versions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Versions {
  String? android;
  String? ios;
  String? description;

  Versions({this.android, this.ios, this.description});

  Versions.fromJson(Map<String, dynamic> json) {
    android = json['android'];
    ios = json['ios'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['android'] = this.android;
    data['ios'] = this.ios;
    data['description'] = this.description;
    return data;
  }
}
