import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class ThemeHelper {
  final _getStorage = GetStorage();
  bool loadTheme() => _getStorage.read(GetStorageKey.isDarkMode) ?? false;

  ThemeMode get them => loadTheme() ? ThemeMode.dark : ThemeMode.light;

  saveTheme(bool isDarkMode) =>
      {_getStorage.write(GetStorageKey.isDarkMode, isDarkMode)};

  void swithTheme() {
    Get.changeThemeMode(loadTheme() ? ThemeMode.light : ThemeMode.dark);
    saveTheme(!loadTheme());
  }
}
