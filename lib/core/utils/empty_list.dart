import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class EmptyList {
  static void ShowEmptyBox() {
    Get.defaultDialog(
        title: "<PERSON>hông có dữ liệu!",
        titleStyle:
            const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
        content: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset("lottie/emptyBox.json", height: 200, width: 200),
          ],
        )));

    Future.delayed(const Duration(seconds: 1), () {
      Get.back(); // Close the dialog
    });
  }
}
