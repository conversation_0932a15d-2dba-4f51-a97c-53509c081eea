import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class InputDateTime {
  static void showcustomDatePicker(
      {required Rx<DateTime?> selectTime, required DateTime initDateTime}) {
    DateTime selectedDate = DateTime.now();
    showDatePicker(
      context: Get.context!,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      locale: const Locale('vi', 'VN'),
    ).then((value) {
      if (value != null) {
        selectTime.value = value;
        String formattedDate = DateFormat.yMMMMd('vi_VN').format(selectedDate);
      }
    });
  }
}
