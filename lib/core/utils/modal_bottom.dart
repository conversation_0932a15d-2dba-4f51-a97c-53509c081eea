import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';

class CustomModalButton {
  static void modalButton({
    required BuildContext? context,
    required List<Widget> listItem,
    required VoidCallback sendMethod,
    String? titleBtnSend = 'Gửi',
    String? titleBtnCancel = 'Chọn lại',
    required title,
  }) {
    showModalBottomSheet(
      backgroundColor: Colors.grey[200],
      context: context!,
      isScrollControlled: true,
      useRootNavigator: true,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.95,
          child: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10))),
            margin: const EdgeInsets.all(10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                Expanded(
                  flex: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Text(title,
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  color: AppColor.blackColor,
                                )),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: ListView(
                    shrinkWrap: true,
                    children: listItem,
                    // Add more list items as needed
                  ),
                ),
                Expanded(
                  flex: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          OutlinedButton.icon(
                              onPressed: sendMethod,
                              style: ButtonStyle(
                                side: MaterialStateProperty.all(
                                    const BorderSide(color: Colors.blue)),
                                shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(30.0))),
                              ),
                              icon: const Icon(Icons.check, color: Colors.blue),
                              label: Text(titleBtnSend ?? "Gửi")),
                          OutlinedButton.icon(
                              style: ButtonStyle(
                                side: MaterialStateProperty.all(
                                    const BorderSide(color: Colors.red)),
                                shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(30.0))),
                              ),
                              onPressed: () {
                                Get.back();
                              },
                              icon: const Icon(Icons.close, color: Colors.red),
                              label: Text(
                                titleBtnCancel ?? "Chọn lại",
                                style: const TextStyle(color: Colors.red),
                              ))
                        ]),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
