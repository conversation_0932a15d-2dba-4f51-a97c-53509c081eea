import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';

class CustomModalShowItem {
  static void modalShowItem({
    required BuildContext? context,
    required List<Widget> listItem,
    required title,
  }) {
    showModalBottomSheet(
      backgroundColor: Colors.grey[200],
      context: context!,
      isScrollControlled: true,
      useRootNavigator: true,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.95,
          child: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10))),
            margin: EdgeInsets.all(10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                Expanded(
                  flex: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Text(title,
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  color: AppColor.blackColor,
                                )),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: ListView(
                    shrinkWrap: true,
                    children: listItem,
                    // Add more list items as needed
                  ),
                ),
                Expanded(
                  flex: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: OutlinedButton.icon(
                        style: ButtonStyle(
                          side: MaterialStateProperty.all(
                              BorderSide(color: Colors.red)),
                          shape: MaterialStateProperty.all(
                              RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30.0))),
                        ),
                        onPressed: () {
                          Get.back();
                        },
                        icon: Icon(Icons.close, color: Colors.red),
                        label: Text(
                          "Đóng",
                          style: TextStyle(color: Colors.red),
                        )),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
