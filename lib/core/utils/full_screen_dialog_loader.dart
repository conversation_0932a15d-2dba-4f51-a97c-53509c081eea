import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';

class FullScreenDialogLoader {
  static void showDialog() {
    Get.dialog(
      WillPopScope(
          child: Center(
            child:
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              SpinKitCircle(
                  color: Get.isDarkMode
                      ? AppColor.yellowColor
                      : AppColor.blueAccentColor,
                  size: 100),
              // IconButton(
              //     onPressed: () {
              //       Get.back();
              //     },
              //     icon: const Icon(
              //       Icons.cancel,
              //       color: Colors.red,
              //     ))
            ]),
          ),
          onWillPop: () => Future.value(false)),
      barrierDismissible: false,
      barrierColor:
          Theme.of(Get.context!).scaffoldBackgroundColor.withOpacity(0.3),
      useSafeArea: true,
    );
  }

  static void cancleDialog() {
    Get.back();
  }
}
