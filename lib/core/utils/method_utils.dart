import 'dart:convert';
import 'dart:io';
import 'package:dart_des/dart_des.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/auth_model.dart';
import 'package:vnpt_ioffice_camau/app/model/login/ds_nhacviec_canbo.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_butpheld_model.dart';
import 'package:vnpt_ioffice_camau/core/utils/week_range.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';

import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:crypto/crypto.dart';
import 'package:vnpt_ioffice_camau/global_widget/custom_tree.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';

class MethodUntils {
  // hàm trừ tháng tính từ ngày hiện tại
  static DateTime subtractMonths(DateTime dateTime, int months) {
    int year = dateTime.year;
    int month = dateTime.month;
    int day = dateTime.day;

    if (month <= months) {
      int remainingMonths = months - month;
      int yearsToSubtract = (remainingMonths / 12).floor() + 1;
      year -= yearsToSubtract;
      month = 12 - (remainingMonths % 12);
    } else {
      month -= months;
    }

    return DateTime(year, month, day);
  }

// lây số nhắc việc theo parentKet và url
  static int getNvNew(String parentKey, String url, List<DsNhacViecCb> listNv) {
    int tongTemp = 0;
    for (var item in listNv) {
      if (item.parentKey.isNotEmpty && item.parentKey.contains(parentKey)) {
        var arrchilds = item.childs;
        for (var itemChilds in arrchilds) {
          if (itemChilds.url == url) {
            if (itemChilds.tong! > 0) {
              tongTemp = itemChilds.tong!;
            }
          }
        }
      }
    }
    return tongTemp;
  }

  // hàm format người nhận từ bút phê lãnh đạo

  static String getChuoiNguoiNhan(List<TenNguoiNhan> dsNguoiNhan) {
    List<String> arrayNguoiNhan = [];
    for (var item in dsNguoiNhan) {
      String tenYeuCau = "";
      switch (item.maYeuCau) {
        case 1:
          tenYeuCau = " (XĐB)";
          break;
        case 2:
          tenYeuCau = " (XLC)";
          break;
        case 3:
          tenYeuCau = " (PHXL)";
          break;
        default:
          tenYeuCau = "";
          break;
      }
      arrayNguoiNhan
          .add("${item.tenDonViNhan}(${item.hoVaTenCanBoNhan}$tenYeuCau)");
    }
    return arrayNguoiNhan.join(", ");
  }

  // get fileName
  static String getFileName(String path) {
    if (path.isNotEmpty) {
      try {
        String filename = path.substring(path.lastIndexOf("__"));
        String filenameTemp = filename.toLowerCase();
        int indexam = filenameTemp.indexOf("am");
        indexam = indexam != -1 ? indexam : 99999;
        int indexpm = filenameTemp.indexOf("pm");
        indexpm = indexpm != -1 ? indexpm : 99999;
        int index = indexam < indexpm ? indexam : indexpm;
        if (indexam == 99999 && indexpm == 99999) {
          index = -1;
        }
        filename = filename.substring(index + 3);
        // filename.length > 30
        //     ? (filename =
        //         filename.replaceRange(10, filename.indexOf('.'), '...'))
        //     : filename;
        return filename;
      } catch (error) {
        return "";
      }
    }
    return "";
  }

  static String getFormatFileName(String pathFile) {
    String fileName = "";
    fileName = pathFile.length > 30
        ? (pathFile =
            pathFile.replaceRange(20, pathFile.lastIndexOf('.') - 1, '...'))
        : pathFile;

    return fileName;
  }

  static String getViewFileDomain(String domainFile) {
    return "$domainFile/mobile-view/pdfviewer/Web/Viewer.html?file=";
  }

  static String getViewFilePdfDomain(
      String domainApi, String domainFile, String pathFile) {
    return '$domainApi/mobile-view/pdf-sign-viewer/index.html?file=$domainFile/api/file-manage/read-file-not-login/$pathFile/view/view-documen';
  }

  // encrytp
  static String enCryptPathFile(String toEncrypt, String key, bool useHashing) {
    if (toEncrypt.isNotEmpty && toEncrypt != null) {
      List<int> toEncryptArray = utf8.encode(toEncrypt);
      List<int> keyArray;

      if (useHashing) {
        var hashedKey = md5.convert(utf8.encode(key));
        var hashedKeyWords = hashedKey.bytes.toList();
        keyArray = hashedKeyWords;
      } else {
        keyArray = utf8.encode(key).sublist(0, 24);
      }
      try {
        final encrypted = DES3(
            key: keyArray,
            mode: DESMode.ECB,
            paddingType: DESPaddingType.PKCS7);
        var result = encrypted.encrypt(toEncryptArray);
        var result2 = Uri.encodeFull(base64.encode(result));
        return result2;
      } catch (exception) {
        print(exception);
      }
    }
    return "";
  }

// decrypt
  static String deCryptPathFile(String toDecrypt, String key, bool useHashing) {
    if (toDecrypt.isNotEmpty) {
      List<int> toDecryptArray = utf8.encode(toDecrypt);
      List<int> keyArray;

      if (useHashing) {
        var hashedKey = md5.convert(utf8.encode(key));
        var hashedKeyWords = hashedKey.bytes.toList();
        keyArray = hashedKeyWords;
      } else {
        keyArray = utf8.encode(key).sublist(0, 24);
      }
      try {
        final encrypted = DES3(
            key: keyArray,
            mode: DESMode.ECB,
            paddingType: DESPaddingType.PKCS7);
        var result = encrypted.decrypt(toDecryptArray);

        return result.toString();
      } catch (exception) {
        print(exception);
      }
    }
    return "";
  }

  static String getUrlFile(String path, String domainFile) {
    if (path.isNotEmpty) {
      String fileName = getFileName(path);
      String url2 = "";
      path = enCryptPathFile(path, AppString.serectKey, true);
      var url =
          "$domainFile/api/file-manage/read-base/$fileName?type=view&path=$path";
      url = Uri.encodeFull(url);
      return url;
    }
    return "";
  }

  static List<FileBase> getFileChiTietTTDH(List<String> listFile) {
    List<FileBase> arrayFileBase = [];
    var _getStorge = GetStorage();
    String domainFile = _getStorge.read(GetStorageKey.domainFile);
    try {
      for (var item in listFile) {
        String fileName = getFileName(item);
        String extentionFile = fileName.split(".").removeLast().toLowerCase();
        switch (extentionFile) {
          case "pdf":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                colorIcon: AppColor.darkRedColor,
                iconFile: Icons.picture_as_pdf_outlined,
                fileName: fileName));
            break;
          case "doc":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                colorIcon: AppColor.helpBlue,
                iconFile: Icons.description,
                fileName: fileName));
            break;
          case "docx":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                colorIcon: AppColor.helpBlue,
                iconFile: Icons.description,
                fileName: fileName));
            break;
          case "xls":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                colorIcon: Color.fromARGB(255, 4, 179, 94),
                iconFile: Icons.poll_outlined,
                fileName: fileName));
            break;
          case "png":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.image,
                fileName: fileName));
            break;
          case "jpg":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.image,
                fileName: fileName));
            break;
          case "jpeg":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.image,
                fileName: fileName));
            break;
          case "rar":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.folder_zip,
                fileName: fileName));
            break;
          case "zip":
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.folder_zip,
                fileName: fileName));
            break;
          default:
            arrayFileBase.add(FileBase(
                urlViewFile: getUrlFile(item, domainFile),
                iconFile: Icons.file_present,
                fileName: fileName));
        }
      }
    } catch (exception) {}
    return arrayFileBase;
  }

  static TreeNode listToTree(List<Map<String, String>> nodes) {
    Map<String, TreeNode> nodeMap = {};
    for (var node in nodes) {
      String id = node['id']!;
      String value = node['value']!;
      nodeMap[id] = TreeNode(
          id: id, parentId: node['parent'], label: value!, children: []);
    }

    for (var node2 in nodes) {
      String id = node2['id']!;
      String parentId = node2['parent']!;
      TreeNode treeNode = nodeMap[id]!;
      if (parentId == '#') {
        nodeMap['#'] = treeNode;
      } else {
        TreeNode treeParent = nodeMap[parentId]!;
        treeParent.children.add(treeNode);
      }
    }

    return nodeMap['#']!;
  }

  static TreeNodes listToTrees(List<Map<String, String>> nodes) {
    Map<String, TreeNodes> nodeMap = {};
    var root = TreeNodes(title: "", children: [], expanded: true);
    int countRout = 0;
    for (var node in nodes) {
      String id = node['id']!;
      String value = node['value']!;
      nodeMap[id] = TreeNodes(
          title: value!, children: [], id: id, parentId: node['parent']);
    }
    for (var node2 in nodes) {
      String id = node2['id']!;
      String parentId = node2['parent']!;
      TreeNodes treeNodes = nodeMap[id]!;
      if (parentId == '#') {
        nodeMap['#'] = treeNodes;
        treeNodes.expanded.value = true;
        countRout++;
      } else {
        TreeNodes treeParent = nodeMap[parentId]!;
        treeParent.children.add(treeNodes);
      }
    }
    if (countRout > 1) {
      // nhiều root
      nodeMap.forEach((key, value) {
        if (value.parentId == '#' && key != '#') {
          value.expanded.value = false;
          root.children.add(value);
        }
      });
      root.hidenRoot.value = true;
      return root;
    } else {
      return nodeMap['#']!;
    }
  }

  // xoá dấu tin nhắn
  static String xoaDau(String str) {
    str = str.replaceAll(RegExp(r'[àáạảãâầấậẩẫăằắặẳẵ]'), 'a');
    str = str.replaceAll(RegExp(r'[èéẹẻẽêềếệểễ]'), 'e');
    str = str.replaceAll(RegExp(r'[ìíịỉĩ]'), 'i');
    str = str.replaceAll(RegExp(r'[òóọỏõôồốộổỗơờớợởỡ]'), 'o');
    str = str.replaceAll(RegExp(r'[ùúụủũưừứựửữ]'), 'u');
    str = str.replaceAll(RegExp(r'[ỳýỵỷỹ]'), 'y');
    str = str.replaceAll(RegExp(r'đ'), 'd');
    str = str.replaceAll(RegExp(r'[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]'), 'A');
    str = str.replaceAll(RegExp(r'[ÈÉẸẺẼÊỀẾỆỂỄ]'), 'E');
    str = str.replaceAll(RegExp(r'[ÌÍỊỈĨ]'), 'I');
    str = str.replaceAll(RegExp(r'[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]'), 'O');
    str = str.replaceAll(RegExp(r'[ÙÚỤỦŨƯỪỨỰỬỮ]'), 'U');
    str = str.replaceAll(RegExp(r'[ỲÝỴỶỸ]'), 'Y');
    str = str.replaceAll(RegExp(r'Đ'), 'D');
    return str;
  }

  // xoá trùng phân từ trong list
  List<T> removeDuplicates<T>(List<T> list) {
    List<T> uniqueList = [];
    for (var item in list) {
      if (!uniqueList.contains(item)) {
        uniqueList.add(item);
      }
    }
    return uniqueList;
  }

  // lấy danh sách năm
  List<int> generateYearList(int numberOfYears) {
    int currentYear = DateTime.now().year;
    List<int> years = [];

    for (int i = 0; i < numberOfYears; i++) {
      years.add(currentYear - i);
    }

    return years;
  }

  //
  static String getNowTime() {
    DateTime now = DateTime.now();
    String iso8601String = now.toIso8601String();
    return iso8601String;
  }

  // xử lý chuỗi HTML
  static Map<String, String> entities = {
    'amp': '&',
    'apos': '\'',
    'lt': '<',
    'gt': '>',
    'quot': '"',
    'nbsp': '\xa0',
  };

  static RegExp entityPattern = RegExp(r'&([a-z]+);', caseSensitive: false);

  static String htmlDecode(String text) {
    if (text != "" && text != null) {
      while (text.indexOf("<br>") != -1) {
        text = text.replaceFirst("<br>", "\n");
      }
      return text.replaceAllMapped(entityPattern, (match) {
        String entity = match.group(1)!.toLowerCase();
        if (entities.containsKey(entity)) {
          return entities[entity]!;
        }
        return match.group(0)!;
      });
    }
    return text;
  }

  static String htmlEncode(String text) {
    if (text != "" && text != null) {
      return text.replaceAll("\n", "<br>");
    }
    return text;
  }

  static String removeHTMLTag(String text) {
    try {
      RegExp regex = RegExp(r'(<([^>]+)>)', caseSensitive: false);
      text = htmlDecode(text);
      String result = text.replaceAll(regex, '');
      return result.trim();
    } catch (e) {
      return text;
    }
  }

  static String removeNoiDungTTDH(String text) {
    if (text != "" && text != null) {
      text = removeHTMLTag(text);
      while (text.indexOf("--") != -1) {
        text = text.replaceFirst("--", "-");
      }
    }
    return text;
  }

  static String replaceAll(String text, String text1, String text2) {
    if (text != "" && text != null) {
      while (text.indexOf(text1) != -1) {
        text = text.replaceFirst(text1, text2);
      }
    }
    return text;
  }

  static String b64EncodeUnicode(String input) {
    String encodedUri = Uri.encodeComponent(input);
    RegExp exp = RegExp(r'%([0-9A-F]{2})');
    String replaced = encodedUri.replaceAllMapped(exp, (Match match) {
      String hex = match.group(1)!;
      int charCode = int.parse(hex, radix: 16);
      return String.fromCharCode(charCode);
    });
    return base64Encode(utf8.encode(replaced));
  }

  static List<WeekRange> getWeeksInYear(int year, {int numberdays = 1}) {
    List<WeekRange> weeks = [];
    int tuan = 1;
    // Ngày đầu tiên của năm
    DateTime firstDayOfYear = DateTime(year, 1, 1);

    // Tính tuần đầu tiên có thể bắt đầu trước ngày 1/1 nếu ngày 1/1 không phải thứ Hai
    int firstDayOfWeek = firstDayOfYear.weekday;
    DateTime startOfFirstWeek =
        firstDayOfYear.subtract(Duration(days: firstDayOfWeek - 1));

    // Ngày đầu tiên của tuần hiện tại
    DateTime currentStartOfWeek = startOfFirstWeek;

    while (currentStartOfWeek.year == year ||
        (currentStartOfWeek.year == year - 1 &&
            currentStartOfWeek.month == 12)) {
      // Ngày cuối cùng của tuần hiện tại
      DateTime currentEndOfWeek = currentStartOfWeek.add(Duration(days: 6));

      // Nếu ngày cuối cùng của tuần hiện tại vượt qua năm hiện tại, thì giới hạn nó trong năm hiện tại
      if (currentEndOfWeek.year != year) {
        currentEndOfWeek = DateTime(year, 12, 31);
      }

      // Thêm tuần vào danh sách
      weeks.add(WeekRange(currentStartOfWeek, currentEndOfWeek, tuan++));

      // Chuyển sang tuần tiếp theo
      currentStartOfWeek = currentEndOfWeek.add(Duration(days: numberdays));
    }

    return weeks;
  }

  static String getWeekdayName(DateTime date) {
    List<String> weekdays = [
      "Chủ Nhật",
      "Thứ Hai",
      "Thứ Ba",
      "Thứ Tư",
      "Thứ Năm",
      "Thứ Sáu",
      "Thứ Bảy"
    ];
    int weekdayIndex = (date.weekday % 7);
    return weekdays[weekdayIndex];
  }

  static Map<K, List<T>> groupBy<T, K>(
      List<T> list, K Function(T) keyFunction) {
    Map<K, List<T>> map = {};

    for (var element in list) {
      var key = keyFunction(element);
      if (!map.containsKey(key)) {
        map[key] = [];
      }
      map[key]!.add(element);
    }

    return map;
  }
}
