import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';

class CustomModalCks {
  static void modalCks({
    required BuildContext? context,
    required List<Widget> listItem,
  }) {
    showModalBottomSheet(
      backgroundColor: Colors.grey[200],
      context: context!,
      isScrollControlled: true,
      useRootNavigator: true,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.95,
          child: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10))),
            margin: const EdgeInsets.all(10),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                Expanded(
                  flex: 0,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: Text("Ký số vị trí",
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium!
                                .copyWith(
                                  color: AppColor.blackColor,
                                )),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(5),
                        child: Text("Danh sách mẫu chữ ký",
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall!
                                .copyWith(
                                  color: AppColor.blackColor,
                                )),
                      ),
                      const Padding(
                        padding: EdgeInsets.all(5),
                        child: Text(
                          "(Ấn vào chữ ký để bất đầu ký)",
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: ListView(
                    shrinkWrap: true,
                    children: listItem,
                    // Add more list items as needed
                  ),
                ),
                Expanded(
                  flex: 0,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          OutlinedButton.icon(
                              style: ButtonStyle(
                                side: MaterialStateProperty.all(
                                    const BorderSide(color: Colors.red)),
                                shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(30.0))),
                              ),
                              onPressed: () {
                                Get.back();
                              },
                              icon: const Icon(Icons.close, color: Colors.red),
                              label: const Text(
                                "Đóng",
                                style: TextStyle(color: Colors.red),
                              ))
                        ]),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
