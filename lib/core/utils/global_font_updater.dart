import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';

/// Helper class để tạo TextStyle với cỡ chữ động cho toàn project
class GlobalFontUpdater {
  
  /// Thay thế GoogleFonts.inter() với FontSizeHelper
  static TextStyle inter({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
  }) {
    return FontSizeHelper.getTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
    );
  }

  /// Thay thế GoogleFonts.roboto() với FontSizeHelper
  static TextStyle roboto({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
  }) {
    return FontSizeHelper.getTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      decoration: decoration,
    );
  }

  /// TextStyle cho tiêu đề lớn
  static TextStyle heading1({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getTextStyle(
      fontSize: FontSizeHelper.titleFontSize + 4,
      fontWeight: fontWeight ?? FontWeight.w700,
      color: color,
    );
  }

  /// TextStyle cho tiêu đề vừa
  static TextStyle heading2({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getTitleStyle(
      fontWeight: fontWeight ?? FontWeight.w600,
      color: color,
    );
  }

  /// TextStyle cho tiêu đề nhỏ
  static TextStyle heading3({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getTextStyle(
      fontSize: FontSizeHelper.currentFontSize + 1,
      fontWeight: fontWeight ?? FontWeight.w600,
      color: color,
    );
  }

  /// TextStyle cho body text
  static TextStyle bodyText({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getTextStyle(
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color,
    );
  }

  /// TextStyle cho subtitle
  static TextStyle subtitle({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getSubtitleStyle(
      fontWeight: fontWeight ?? FontWeight.w500,
      color: color,
    );
  }

  /// TextStyle cho caption
  static TextStyle caption({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getCaptionStyle(
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color,
    );
  }

  /// TextStyle cho button
  static TextStyle button({
    Color? color,
    FontWeight? fontWeight,
  }) {
    return FontSizeHelper.getButtonStyle(
      fontWeight: fontWeight ?? FontWeight.w500,
      color: color,
    );
  }

  /// Wrapper cho Text widget với cỡ chữ động
  static Widget dynamicText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool softWrap = true,
  }) {
    return Builder(
      builder: (context) {
        return Text(
          text,
          style: style ?? FontSizeHelper.getTextStyle(),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
          softWrap: softWrap,
        );
      },
    );
  }

  /// Wrapper cho RichText với cỡ chữ động
  static Widget dynamicRichText(
    List<TextSpan> children, {
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool softWrap = true,
  }) {
    return Builder(
      builder: (context) {
        return RichText(
          text: TextSpan(
            style: FontSizeHelper.getTextStyle(),
            children: children,
          ),
          textAlign: textAlign ?? TextAlign.start,
          maxLines: maxLines,
          overflow: overflow ?? TextOverflow.clip,
          softWrap: softWrap,
        );
      },
    );
  }
}

/// Extension để dễ dàng convert TextStyle hiện tại
extension TextStyleDynamic on TextStyle {
  /// Convert TextStyle hiện tại thành dynamic font size
  TextStyle get dynamic {
    return copyWith(fontSize: FontSizeHelper.currentFontSize);
  }

  /// Convert thành title size
  TextStyle get titleSize {
    return copyWith(fontSize: FontSizeHelper.titleFontSize);
  }

  /// Convert thành subtitle size
  TextStyle get subtitleSize {
    return copyWith(fontSize: FontSizeHelper.subtitleFontSize);
  }

  /// Convert thành caption size
  TextStyle get captionSize {
    return copyWith(fontSize: FontSizeHelper.captionFontSize);
  }
}
