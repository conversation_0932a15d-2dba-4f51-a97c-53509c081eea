import 'package:flutter/material.dart';

class AppColor {
  // Light Theme Color
  static const lightPrimaryBackgroundColor = Color(0XFFf6f7fa);
  static const lightSecondaryBackgroundColor = whiteColor;
  static var lightPrimaryColor = blueAccentColor;
  static var lightSecondaryColor = blueAccentColor;

// Dark Theme Color
  static const darkPrimaryBackgroundColor = Color(0xff141A31);
  static const darkSecondaryBackgroundColor = Color(0xff1E2746);
  static var darkPrimaryColor = blueAccentColor;
  static var darkSecondaryColor = yellowColor;
  // common color
  static var blueAccentColor = Color(0xFF0066FF);
  static const yellowColor = Colors.yellow;
  static const whiteColor = Color(0xFFFFFFFF);
  static const blackColor = Color(0xFF000000);
  static const greyColor = Colors.grey;
  static const darkRedColor = Color(0xFFB00020);
  // snackbar color
  static const Color helpBlue = Color(0xff3282B8);
  static const Color failureRed = Color(0xffc72c41);
  static const Color successGreen = Color.fromARGB(0, 25, 135, 84);
  static const Color warningYellow = Color(0xffFCA652);
}
