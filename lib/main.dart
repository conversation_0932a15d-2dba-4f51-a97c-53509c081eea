import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/firebase_api.dart';
import 'package:vnpt_ioffice_camau/firebase_options.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_binding.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';
import 'modules/controllers/network/network_binding.dart';

void main() async {
  NetworkBinding().dependencies();
  await GetStorage.init();
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await FireBaseApi().initNotification();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]).then((_) {
    runApp(GetMaterialApp(
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: [
        const Locale('vi', 'VN'), // Vietnamese
        // Add more locales if needed
      ],
      debugShowCheckedModeBanner: false,
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routers,
      initialBinding: SetupBinding(),
      navigatorObservers: [GetObserver((_) {}, Get.routing)],
    ));
  });
}
