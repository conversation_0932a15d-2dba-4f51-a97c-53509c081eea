import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/danh_ba_controlller.dart';

class DanhBaView extends GetView<DanhBaControlller> {
  const DanhBaView({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(bottom: 5, left: 10, right: 5, top: 10),
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 2),
                  child: SizedBox(
                    width: Get.width - 100,
                    height: 50,
                    child: TextF<PERSON><PERSON><PERSON>(
                      controller: controller.searchDanhBa,
                      onSaved: (value) {
                        controller.searchDanhBa.text = value!;
                      },
                      validator: (value) {
                        if (value!.isEmpty) {
                          return '';
                        }
                        return null;
                      },
                      decoration: const InputDecoration(
                        labelText: 'Nhập từ khoá tìm kiếm...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5.0),
                  child: Container(
                    height: 50,
                    width: 70,
                    decoration: BoxDecoration(
                      color: AppColor
                          .blueAccentColor, // Set the background color here
                      borderRadius: BorderRadius.circular(
                          10), // Optional: add border radius
                    ),
                    child: IconButton(
                      color: Colors.white,
                      onPressed: () {
                        controller.searchCanBo();
                      },
                      icon: const Icon(Icons.search),
                    ),
                  ),
                )
              ],
            ),
          ),
          Expanded(
              child: controller.obx(
            (danhBa) => ListView.builder(
              itemCount: danhBa!.length,
              itemBuilder: (context, index) {
                return InkWell(
                  onTap: () {
                    controller.makePhoneCall(
                        'tel:+84${danhBa[index].dataPhone!.substring(1)}');
                  },
                  child: Column(
                    children: [
                      ListTile(
                        title: Text(
                          '${danhBa[index].dataTenCanBo} - ${danhBa[index].dataPhone}',
                          style: FontSizeHelper.getTextStyle( color: Colors.black),
                        ),
                        subtitle: RichText(
                          text: TextSpan(
                            style: TextStyle(color: Colors.grey, fontSize: 20),
                            children: <TextSpan>[
                              TextSpan(
                                  text:
                                      'Email: ${danhBa[index].dataEmail ?? "Rỗng"}\n',
                                  style: FontSizeHelper.getTextStyle()),
                              TextSpan(
                                  text:
                                      'Chức vụ: ${danhBa[index].dataChucVu ?? "Rỗng"}',
                                  style: FontSizeHelper.getTextStyle()),
                            ],
                          ),
                        ),
                        dense: true,
                      ),
                      const Divider(
                        height: 1,
                        color: Colors.grey,
                      )
                    ],
                  ),
                );
              },
            ),
            onEmpty: Container(
              child: Center(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Lottie.asset("lottie/emptyBox.json", height: 200, width: 200),
                  const Text("Không có dữ liệu!")
                ],
              )),
            ),
            onLoading: SpinKitCircle(
              color: AppColor.blueAccentColor,
            ),
            onError: (error) {
              return Center(child: Text(error.toString()));
            },
          ))
        ],
      ),
    );
  }
}
