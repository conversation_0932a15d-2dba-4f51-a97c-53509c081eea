import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/vbnb_controller.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/views/vbnb/item_list_vbnb.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/item_listdagui_vbnb.dart';

class VanBanNoiBo extends GetView<VanBanNoiBoController> {
  HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 240,
              height: 45,
              child: TabBar(
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      5.0,
                    ),
                    color: AppColor.blueAccentColor,
                  ),
                  unselectedLabelColor: Colors.black,
                  padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                  labelColor: AppColor.whiteColor,
                  controller: controller.tabController,
                  onTap: (value) {
                    controller.changeIndexTab(value);
                  },
                  tabs: [
                    Obx(() => Tab(
                        icon: (homeController.nvVbNoiBo == 0)
                            ? const Text("Đã nhận")
                            : badges.Badge(
                                badgeStyle: const badges.BadgeStyle(
                                  badgeColor: Colors.red,
                                ),
                                position: badges.BadgePosition.topEnd(
                                    top: -20, end: -12),
                                badgeContent: Text(
                                  homeController.nvVbNoiBo.toString(),
                                  style: const TextStyle(color: Colors.white),
                                ),
                                child: const Text("Đã nhận"),
                              ))),
                    const Tab(text: "Đã gửi")
                  ]),
            ),
          ],
        ),
        Expanded(
            child: TabBarView(
                controller: controller.tabController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
              Container(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Obx(
                          () => SizedBox(
                            height: 50,
                            width: 170,
                            child: RadioListTile<int>(
                              title: Text(
                                'Chưa xem',
                                style: GoogleFonts.roboto(fontSize: 15),
                              ),
                              value: 0,
                              dense: true,
                              groupValue: controller.selectedVanBanNoiBo.value,
                              onChanged: (int? value) {
                                controller.changeSelectedVbnb(value!);
                              },
                            ),
                          ),
                        ),
                        Obx(() => SizedBox(
                              height: 50,
                              width: 150,
                              child: RadioListTile<int>(
                                title: Text(
                                  'Đã xem',
                                  style: GoogleFonts.roboto(fontSize: 15),
                                ),
                                value: 1,
                                dense: true,
                                groupValue:
                                    controller.selectedVanBanNoiBo.value,
                                onChanged: (int? value) {
                                  controller.changeSelectedVbnb(value!);
                                },
                              ),
                            )),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: SizedBox(
                        height: 45,
                        child: TextFormField(
                            cursorColor:
                                const Color.fromARGB(255, 242, 237, 237),
                            style: const TextStyle(color: AppColor.blackColor),
                            onFieldSubmitted: (value) {
                              controller.setKeySearch(value);
                            },
                            decoration: const InputDecoration(
                                errorStyle: TextStyle(color: AppColor.helpBlue),
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(
                                      width: 1, color: AppColor.helpBlue),
                                ),
                                labelStyle: TextStyle(color: AppColor.helpBlue),
                                focusColor: AppColor.blackColor,
                                prefixIcon: Icon(
                                  Icons.search_outlined,
                                  color: AppColor.helpBlue,
                                  size: 20,
                                ),
                                hintText: "Nhập từ khoá tìm kiếm...",
                                hintStyle: TextStyle(fontSize: 13))),
                      ),
                    ),
                    Expanded(
                      child: Container(
                          color: AppColor.whiteColor,
                          child: controller.obx(
                              (listVbNbDaNhan) => controller
                                          .indexTabGobal.value !=
                                      0
                                  ? const Text("")
                                  : RefreshIndicator(
                                      onRefresh: () =>
                                          controller.loadDanhSachVbnbDaNhan(),
                                      child: ListView.builder(
                                          controller: controller
                                              .scrollerControllerDsVbnbDaNhan,
                                          physics:
                                              const AlwaysScrollableScrollPhysics(),
                                          itemCount: controller
                                              .danhSachVbNbDaNhan.length,
                                          itemBuilder: ((context, index) =>
                                              ItemListVbnb(
                                                trichYeu: listVbNbDaNhan![index]
                                                        .trichYeu ??
                                                    "",
                                                so: listVbNbDaNhan![index]
                                                        .soHieu ??
                                                    "",
                                                nguoiGui: listVbNbDaNhan![index]
                                                    .tenCanBoGui!,
                                                isXem: listVbNbDaNhan![index]
                                                    .xem!
                                                    .toInt(),
                                                donViGui: listVbNbDaNhan![index]
                                                        .tenDonViQuanTri ??
                                                    "",
                                                ngayNhan: DateFormat(
                                                            'dd/MM/yyyy hh:ss')
                                                        .format(listVbNbDaNhan![
                                                                index]
                                                            .ngayGui!)
                                                        .toString() ??
                                                    "",
                                                onClickItem: () {
                                                  controller.onPressPageDetail(
                                                      listVbNbDaNhan![index]);
                                                },
                                              ))),
                                    ),
                              onLoading: SpinKitCircle(
                                color: AppColor.blueAccentColor,
                              ),
                              onEmpty: Container(
                                child: Center(
                                    child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Lottie.asset("lottie/emptyBox.json",
                                        height: 200, width: 200),
                                    const Text("Không tìm thấy dữ liệu!")
                                  ],
                                )),
                              ))),
                    )
                  ],
                ),
              ),
              Container(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: SizedBox(
                        height: 45,
                        child: TextFormField(
                            cursorColor:
                                const Color.fromARGB(255, 242, 237, 237),
                            style: const TextStyle(color: AppColor.blackColor),
                            onFieldSubmitted: (value) {
                              controller.setKeySearchDaGui(value);
                            },
                            decoration: const InputDecoration(
                                errorStyle: TextStyle(color: AppColor.helpBlue),
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(
                                      width: 1, color: AppColor.helpBlue),
                                ),
                                labelStyle: TextStyle(color: AppColor.helpBlue),
                                focusColor: AppColor.blackColor,
                                prefixIcon: Icon(
                                  Icons.search_outlined,
                                  color: AppColor.helpBlue,
                                  size: 20,
                                ),
                                hintText: "Nhập từ khoá tìm kiếm...",
                                hintStyle: TextStyle(fontSize: 13))),
                      ),
                    ),
                    Expanded(
                      child: Container(
                          color: AppColor.whiteColor,
                          child: controller.obx(
                              (listVbNbDaGui) => controller
                                          .indexTabGobal.value !=
                                      1
                                  ? const Text("")
                                  : RefreshIndicator(
                                      onRefresh: () =>
                                          controller.loadDanhSachVbnbDaGui(),
                                      child: ListView.builder(
                                          controller: controller
                                              .scrollerControllerDsVbnbDaGui,
                                          physics:
                                              const AlwaysScrollableScrollPhysics(),
                                          itemCount: controller
                                              .danhsachVbnbDaGui.length,
                                          itemBuilder: ((context, index) {
                                            if (controller.danhsachVbnbDaGui ==
                                                null) {
                                            } else {
                                              return ItemListDaGuiVbnb(
                                                trichYeu: listVbNbDaGui![index]
                                                        .trichYeu ??
                                                    "",
                                                so: listVbNbDaGui![index]
                                                        .soHieu ??
                                                    "Rỗng",
                                                xuatXu: listVbNbDaGui![index]
                                                        .noiLuu ??
                                                    "Rỗng",
                                                ngayNhan: DateFormat(
                                                            'dd/MM/yyyy hh:ss')
                                                        .format(listVbNbDaGui![
                                                                index]
                                                            .ngayGui!)
                                                        .toString() ??
                                                    "",
                                                onClickItem: () {
                                                  controller.onPressPageDetail(
                                                      listVbNbDaGui![index]);
                                                },
                                              );
                                            }
                                          })),
                                    ),
                              onLoading: SpinKitCircle(
                                color: AppColor.blueAccentColor,
                              ),
                              onEmpty: Container(
                                child: Center(
                                    child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Lottie.asset("lottie/emptyBox.json",
                                        height: 200, width: 200),
                                    const Text("Không tìm thấy dữ liệu!")
                                  ],
                                )),
                              ))),
                    )
                  ],
                ),
              ),
            ]))
      ],
    ));
  }
}
