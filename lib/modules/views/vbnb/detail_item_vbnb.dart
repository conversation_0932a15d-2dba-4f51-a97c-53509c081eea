import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/chitiet_vbnb_controller.dart';

class DetailItemVbnb extends GetView<ChiTietVbnbController> {
  final SetupController setupController = Get.find();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      child: Obx(
        () => (controller.isLoadData.value == false)
            ? Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: WillPopScope(
                    child: Center(
                      child: SpinKitFadingCircle(
                          color: Get.isDarkMode
                              ? AppColor.yellowColor
                              : AppColor.blueAccentColor,
                          size: 40),
                    ),
                    onWillPop: () => Future.value(false)),
              )
            : Container(
                child: Column(
                  children: [
                    Card(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: "Trích yếu: ",
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                  text: (controller.item.value.trichYeu == null)
                                      ? ""
                                      : controller.item.value?.trichYeu,
                                  style: const TextStyle(
                                      fontWeight:
                                          FontWeight.bold), // Phong cách in đậm
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Cơ quan ban hành: ',
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      controller.item.value.tenCoQuanBanHanh ??
                                          "",
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red), // Phong cách in đậm
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Ngày văn bản: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.item.value?.tenLoaiVanBan)
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Lĩnh vực: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      controller.item.value.tenLinhVucVanBan ??
                                          "",
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Loại: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.item.value.tenLoaiVanBan ??
                                        "",
                                    style: const TextStyle(
                                        color: AppColor.helpBlue))
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Ngày lưu: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.item.value.ngayLuu == null
                                        ? ""
                                        : DateFormat('dd/MM/yyyy').format(
                                            controller.item.value!.ngayLuu!))
                              ],
                            ),
                          ),
                        ),

                        Container(
                          child: ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              children: List.generate(
                                  (controller.item.value?.srcVanBan == null)
                                      ? 0
                                      : MethodUntils.getFileChiTietTTDH(
                                              controller.item.value.srcVanBan!
                                                  .split(":"))
                                          .length, (index) {
                                return ListTile(
                                  title: GestureDetector(
                                    onTap: () {
                                      ModalViewFileOnline.ViewFileOnline(
                                          tenFile:
                                              MethodUntils.getFileChiTietTTDH(
                                                      controller.item.value!
                                                          .srcVanBan!
                                                          .split(":"))[index]
                                                  .fileName!,
                                          item: controller
                                              .item.value!.srcVanBan!
                                              .split(":")[index],
                                          path: MethodUntils.getFileChiTietTTDH(
                                                  controller
                                                      .item.value!.srcVanBan!
                                                      .split(":"))[index]
                                              .urlViewFile!);
                                      // setupController.openFile(
                                      //     url: MethodUntils.getFileChiTietTTDH(
                                      //             controller
                                      //                 .item.value!.srcVanBan!
                                      //                 .split(":"))[index]
                                      //         .urlViewFile!,
                                      //     fileName: MethodUntils.getFileName(
                                      //         controller.item.value!.srcVanBan!
                                      //             .split(":")[index]
                                      //             .toString()));
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          MethodUntils.getFileChiTietTTDH(
                                                  controller
                                                      .item.value!.srcVanBan!
                                                      .split(":"))[index]
                                              .iconFile,
                                          color:
                                              MethodUntils.getFileChiTietTTDH(
                                                      controller.item.value!
                                                          .srcVanBan!
                                                          .split(":"))[index]
                                                  .colorIcon,
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 8.0),
                                          child: Text(
                                              MethodUntils.getFormatFileName(
                                                  MethodUntils.getFileName(
                                                      controller.item.value!
                                                          .srcVanBan!
                                                          .split(":")[index]
                                                          .toString())),
                                              style: const TextStyle(
                                                  color: AppColor.blackColor,
                                                  fontSize: 13)),
                                        ),
                                      ],
                                    ),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Visibility(
                                        visible: (controller.indexTab == 0),
                                        child: GestureDetector(
                                            onTap: () {
                                              controller.onChuyenPageKySo(
                                                  controller
                                                      .item.value.srcVanBan!
                                                      .split(":")[index]
                                                      .toString(),
                                                  0);
                                            },
                                            child: const Icon(Icons.edit,
                                                color: Colors.blue)),
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(left: 8.0),
                                        child: Visibility(
                                          visible: (controller.indexTab == 0),
                                          child: GestureDetector(
                                              onTap: () {
                                                controller.onChuyenPageKySo(
                                                    controller
                                                        .item.value.srcVanBan
                                                        .split(":")[index]
                                                        .toString(),
                                                    1);
                                              },
                                              child: const Icon(Icons.storage)),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              })),
                        ),
                        const Padding(
                          padding: EdgeInsets.only(bottom: 10),
                        ),
                        const Divider(
                          height: 2,
                          color: AppColor.greyColor,
                        ),
                        // chuyên viên
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElevatedButton(
                              onPressed: () {
                                controller
                                    .onPressPageXuLy(controller.item.value!);
                                // Button onPressed callback
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(5),
                                ),
                              ),
                              child: const Text(
                                'Chuyển',
                                style: TextStyle(
                                    fontSize: 14, color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const Text("Tổng hợp ý kiến xử lý",
                              style: TextStyle(
                                  color: AppColor.blackColor,
                                  fontWeight: FontWeight.w600)),
                          const Padding(padding: EdgeInsets.only(bottom: 10)),
                          const Divider(
                            height: 2,
                            color: AppColor.greyColor,
                          ),
                          Obx(
                            () => Column(
                              children: controller.listQtxlVbnb
                                  .map((qtxl) => (controller
                                              .renderNodeChildQtxl(qtxl)
                                              .length >
                                          0)
                                      ? Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Text(
                                                "Người gửi: ${qtxl.tenCanBoNhan}",
                                              ),
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  const Text("Người nhận: "),
                                                  Expanded(
                                                    flex: 3,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: controller
                                                          .renderNodeChildQtxl(
                                                              qtxl),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const Divider(
                                                color: Colors.grey,
                                              )
                                            ])
                                      : const Text(""))
                                  .toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    ));
  }
}
