import 'package:flutter/material.dart';

class ItemListDaGuiVbnb extends StatelessWidget {
  final String trichYeu;
  final String so;
  final String xuatXu;
  final String ngayNhan;
  final VoidCallback onClickItem;
  const ItemListDaGuiVbnb(
      {super.key,
      required this.trichYeu,
      required this.so,
      required this.xuatXu,
      required this.ngay<PERSON>han,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GestureDetector(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(width: 0.3))),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            trichYeu,
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Số: $so",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Xuất xứ: $xuatXu",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                        ])),
              ),
              Flexible(
                flex: 1,
                fit: FlexFit.tight,
                child: Container(
                    child: Center(
                  child: Text(ngayNhan, style: const TextStyle(fontSize: 13.0)),
                )),
              ),
            ],
          ),
        ),
        onTap: onClickItem,
      ),
    );
  }
}
