import 'package:flutter/material.dart';

class ItemListVbnb extends StatelessWidget {
  final String trichYeu;
  final String so;
  final String nguoiGui;
  final String donViGui;
  final String ngayNhan;
  final int? isXem;
  final VoidCallback onClickItem;
  const ItemListVbnb(
      {super.key,
      required this.trichYeu,
      required this.so,
      required this.nguoiGui,
      this.isXem,
      required this.donViGui,
      required this.ngay<PERSON>han,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GestureDetector(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(width: 0.3))),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (isXem == 0)
                              ? Text(trich<PERSON>eu,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14.0,
                                  ))
                              : Text(
                                  trichYeu,
                                ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Số: $so",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Người gửi: $nguoiGui",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Đơn vị gửi: $donViGui",
                            style: const TextStyle(fontSize: 13.0),
                          )
                        ])),
              ),
              Flexible(
                flex: 1,
                fit: FlexFit.tight,
                child: Container(
                    child: Center(
                  child: Text(ngayNhan, style: const TextStyle(fontSize: 13.0)),
                )),
              ),
            ],
          ),
        ),
        onTap: onClickItem,
      ),
    );
  }
}
