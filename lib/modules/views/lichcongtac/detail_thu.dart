import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lct_detail_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/item_thu.dart';

class DetailThuView extends GetView<LctDetailController> {
  const DetailThuView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        child: SingleChildScrollView(
          child: controller.obx(
              (state) => Column(
                  children: controller.lctDetailThu
                      .map(
                        (e) => ItemThu(
                            ngay: DateFormat('MM/dd/yyyy HH:mm:ss')
                                .parse(e.ngayThuc<PERSON>ien!),
                            data: e.data!),
                      )
                      .toList()),
              onEmpty: Container(
                height: Get.height - 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Lottie.asset("lottie/emptyBox.json",
                          height: 200, width: 200),
                      const Text("Không tìm thấy dữ liệu!")
                    ],
                  ),
                ),
              ),
              onLoading: Container(
                height: Get.height - 200,
                child: Center(
                  child: SpinKitFadingCircle(
                    color: AppColor.blueAccentColor,
                  ),
                ),
              )),
        ),
      ),
    );
  }
}
