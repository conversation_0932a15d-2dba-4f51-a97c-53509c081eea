import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/hotro/widgets/unit_tree_selector.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/staff_model.dart';

class ResetMatKhauNguoiDung extends GetView<ResetMkNguoiDungController> {
  const ResetMatKhauNguoiDung({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Chọn đơn vị
                  _buildUnitSelector(),
                  const SizedBox(height: 24),

                  // Danh sách cán bộ
                  Obx(() => _buildStaffList()),
                ],
              ),
            ),

            // Unit tree dialog
            Obx(() => controller.showUnitTreeDialog.value
                ? UnitTreeSelector(controller: controller)
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitSelector() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_tree,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Chọn đơn vị',
                style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Selected unit display
          Obx(() => GestureDetector(
                onTap: () => controller.showUnitSelector(),
                child: Container(
                  width: Get.width,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.grey[50],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        controller.selectedUnitId.value == 0
                            ? Icons.business_outlined
                            : Icons.business,
                        color: controller.selectedUnitId.value == 0
                            ? Colors.grey[500]
                            : AppColor.blueAccentColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          controller.selectedUnitId.value == 0
                              ? '-- Nhấn để chọn đơn vị --'
                              : controller.selectedUnitName.value,
                          style: FontSizeHelper.getTextStyle( color: controller.selectedUnitId.value == 0
                                ? Colors.grey[600]
                                : Colors.grey[800],
                            fontWeight: controller.selectedUnitId.value == 0
                                ? FontWeight.w400
                                : FontWeight.w500,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey[500],
                        size: 20,
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildStaffList() {
    if (controller.selectedUnitId.value == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColor.blueAccentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.people,
                  color: AppColor.blueAccentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Danh sách cán bộ',
                      style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w700,
                        color: Colors.grey[800],
                      ),
                    ),
                    Obx(() => Text(
                          controller.staffSearchQuery.value.isEmpty
                              ? '${controller.staffList.length} cán bộ trong đơn vị'
                              : '${controller.filteredStaffList.length} / ${controller.staffList.length} cán bộ',
                          style: FontSizeHelper.getTextStyle( color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        )),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColor.blueAccentColor,
                      AppColor.blueAccentColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColor.blueAccentColor.withOpacity(0.3),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Obx(() => Text(
                      controller.staffSearchQuery.value.isEmpty
                          ? '${controller.staffList.length}'
                          : '${controller.filteredStaffList.length}',
                      style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    )),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Search input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[50],
            ),
            child: TextField(
              onChanged: (value) => controller.searchStaff(value),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm cán bộ theo tên, username, chức vụ...',
                hintStyle: FontSizeHelper.getTextStyle(color: Colors.grey[500],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey[500],
                  size: 20,
                ),
                suffixIcon:
                    Obx(() => controller.staffSearchQuery.value.isNotEmpty
                        ? IconButton(
                            onPressed: () => controller.clearStaffSearch(),
                            icon: Icon(
                              Icons.clear,
                              color: Colors.grey[500],
                              size: 20,
                            ),
                          )
                        : const SizedBox.shrink()),
              ),
              style: FontSizeHelper.getTextStyle( color: Colors.grey[800],
              ),
            ),
          ),
          const SizedBox(height: 16),

          if (controller.isLoadingStaff.value)
            _buildLoadingState()
          else if (controller.filteredStaffList.isEmpty)
            _buildEmptySearchState()
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.filteredStaffList.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                final staff = controller.filteredStaffList[index];
                return _buildStaffItem(staff);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildStaffItem(StaffModel staff) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Có thể thêm action khi tap vào item
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Thông tin cán bộ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Tên và chức vụ
                      Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Text(
                              staff.hoVaTenCanBo,
                              style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w600,
                                color: Colors.grey[800],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                        ],
                      ),
                      const SizedBox(height: 6),

                      // Username và SĐT
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  Icons.account_circle_outlined,
                                  size: 14,
                                  color: Colors.grey[500],
                                ),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    staff.username,
                                    style: FontSizeHelper.getTextStyle( color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (staff.hasPhoneNumber()) ...[
                            const SizedBox(width: 8),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.phone_outlined,
                                  size: 14,
                                  color: Colors.grey[500],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  staff.getPhoneNumber(),
                                  style: FontSizeHelper.getTextStyle( color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),

                // Reset button compact hơn
                Obx(() => _buildResetButton(staff)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResetButton(StaffModel staff) {
    final isResetting = controller.isResetting.value &&
        controller.resettingStaffId.value == staff.maCtcbKc.toInt();

    return SizedBox(
      height: 36,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: isResetting ? Colors.grey[300] : Colors.red[500],
          foregroundColor: isResetting ? Colors.grey[600] : Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(18),
          ),
          elevation: isResetting ? 0 : 2,
          shadowColor: Colors.red.withOpacity(0.3),
        ),
        onPressed: isResetting ? null : () => controller.resetPassword(staff),
        child: isResetting
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      'Đang xử lý...',
                      style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.lock_reset, size: 14),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      'Reset',
                      style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEmptySearchState() {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              shape: BoxShape.circle,
            ),
            child: Icon(
              controller.staffSearchQuery.value.isEmpty
                  ? Icons.people_outline
                  : Icons.search_off,
              size: 48,
              color: controller.staffSearchQuery.value.isEmpty
                  ? Colors.grey[400]
                  : Colors.orange[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            controller.staffSearchQuery.value.isEmpty
                ? 'Chưa có cán bộ'
                : 'Không tìm thấy kết quả',
            style: FontSizeHelper.getTextStyle( color: Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.staffSearchQuery.value.isEmpty
                ? 'Đơn vị này chưa có cán bộ nào\nhoặc chưa được phân quyền'
                : 'Không tìm thấy cán bộ nào phù hợp\nvới từ khóa "${controller.staffSearchQuery.value}"',
            textAlign: TextAlign.center,
            style: FontSizeHelper.getTextStyle( color: Colors.grey[500],
              fontWeight: FontWeight.w500,
              height: 1.4,
            ),
          ),
          if (controller.staffSearchQuery.value.isNotEmpty) ...[
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () => controller.clearStaffSearch(),
              icon: const Icon(Icons.clear, size: 16),
              label: Text(
                'Xóa bộ lọc',
                style: FontSizeHelper.getTextStyle( fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(48),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColor.blueAccentColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(AppColor.blueAccentColor),
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Đang tải danh sách cán bộ...',
            style: FontSizeHelper.getTextStyle( color: Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Vui lòng chờ trong giây lát',
            style: FontSizeHelper.getTextStyle( color: Colors.grey[500],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
