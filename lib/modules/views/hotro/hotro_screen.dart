import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/hotro_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/hotro/widgets/unit_tree_selector_hotro.dart';

class HoTroNguoiDung extends GetView<HoTroController> {
  const HoTroNguoiDung({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Chọn đơn vị
                _buildUnitSelector(),
                const SizedBox(height: 24),

                // T<PERSON><PERSON> kiếm
                _buildSearchSection(),
                const SizedBox(height: 16),

                // Danh sách văn bản
                Obx(() => _buildDocumentList()),
              ],
            ),
          ),

          // Unit tree dialog
          Obx(() => controller.showUnitTreeDialog.value
              ? UnitTreeSelectorHotro(controller: controller)
              : const SizedBox.shrink()),
        ],
      ),
    );
  }

  Widget _buildUnitSelector() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Obx(() => Text(
                    'Chọn đơn vị',
                    style: FontSizeHelper.getTitleStyle(
                      color: Colors.grey[800],
                    ),
                  )),
            ],
          ),
          const SizedBox(height: 16),

          // Unit selector button
          GestureDetector(
            onTap: () => controller.showUnitSelector(),
            child: Container(
              width: Get.width,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Obx(() => Text(
                          controller.selectedUnitName.value.isEmpty
                              ? '-- Chọn đơn vị --'
                              : controller.selectedUnitName.value,
                          style: FontSizeHelper.getTextStyle(
                            color: controller.selectedUnitName.value.isEmpty
                                ? Colors.grey[600]
                                : Colors.grey[800],
                            fontWeight:
                                controller.selectedUnitName.value.isEmpty
                                    ? FontWeight.normal
                                    : FontWeight.w500,
                          ),
                        )),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.search,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Obx(() => Text(
                    'Tìm kiếm văn bản',
                    style: FontSizeHelper.getTextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  )),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller.searchController,
                  decoration: InputDecoration(
                    hintText: 'Nhập ký hiệu văn bản (VD: 01/QĐ-UBND)',
                    hintStyle:
                        FontSizeHelper.getTextStyle(color: Colors.grey[500]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColor.blueAccentColor),
                    ),
                    prefixIcon: const Icon(Icons.article_outlined),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  onSubmitted: (_) => controller.searchDocuments(),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.blueAccentColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: controller.isSearching.value
                    ? null
                    : controller.searchDocuments,
                icon: controller.isSearching.value
                    ? const SpinKitThreeBounce(color: Colors.white, size: 16)
                    : const Icon(Icons.search, size: 20),
                label: Text(
                  controller.isSearching.value ? 'Đang tìm...' : 'Tìm kiếm',
                  style:
                      FontSizeHelper.getTextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentList() {
    if (controller.documentList.isEmpty &&
        controller.selectedUnitId.value != 0 &&
        !controller.isSearching.value) {
      return _buildEmptyState();
    }

    if (controller.documentList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Obx(() => Text(
                    'Danh sách văn bản (${controller.documentList.length} văn bản)',
                    style: FontSizeHelper.getTextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  )),
            ],
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.documentList.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final document = controller.documentList[index];
              return _buildDocumentItem(document);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(Map<String, dynamic> document) {
    bool canRecall = document['trangThai'] != 'Đã thu hồi';
    bool isCurrentlyRecalling = controller.isRecalling.value &&
        controller.recallingDocId == document['id'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[200]!),
        borderRadius: BorderRadius.circular(12),
        color: canRecall ? Colors.white : Colors.grey[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header với ký hiệu và trạng thái
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColor.blueAccentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Obx(() => Text(
                      document['kyHieu'],
                      style: FontSizeHelper.getTextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColor.blueAccentColor,
                      ),
                    )),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: canRecall ? Colors.green[100] : Colors.red[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Obx(() => Text(
                      document['trangThai'],
                      style: FontSizeHelper.getTextStyle(
                        fontWeight: FontWeight.w500,
                        color: canRecall ? Colors.green[700] : Colors.red[700],
                      ),
                    )),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Tiêu đề
          Obx(() => Text(
                document['tieuDe'],
                style: FontSizeHelper.getTextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              )),
          const SizedBox(height: 8),

          // Thông tin chi tiết
          Row(
            children: [
              _buildInfoChip(Icons.category, document['loaiVanBan']),
              const SizedBox(width: 12),
              _buildInfoChip(Icons.calendar_today, document['ngayBanHanh']),
            ],
          ),
          const SizedBox(height: 8),
          _buildInfoChip(Icons.person, document['nguoiKy']),
          const SizedBox(height: 16),

          // Button thu hồi
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isCurrentlyRecalling || !canRecall
                      ? Colors.grey[300]
                      : Colors.red[600],
                  foregroundColor: isCurrentlyRecalling || !canRecall
                      ? Colors.grey[600]
                      : Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: isCurrentlyRecalling || !canRecall
                    ? null
                    : () => controller.recallDocument(document),
                icon: isCurrentlyRecalling
                    ? const SpinKitThreeBounce(color: Colors.grey, size: 16)
                    : Icon(
                        canRecall ? Icons.cancel : Icons.check_circle,
                        size: 16,
                      ),
                label: Text(
                  isCurrentlyRecalling
                      ? 'Đang thu hồi...'
                      : canRecall
                          ? 'Thu hồi'
                          : 'Đã thu hồi',
                  style: FontSizeHelper.getTextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Obx(() => Text(
              text,
              style: FontSizeHelper.getTextStyle(
                color: Colors.grey[600],
              ),
            )),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Obx(() => Text(
                'Không tìm thấy văn bản',
                style: FontSizeHelper.getTextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              )),
          const SizedBox(height: 8),
          Obx(() => Text(
                'Hãy thử tìm kiếm với ký hiệu khác',
                style: FontSizeHelper.getTextStyle(
                  color: Colors.grey[400],
                ),
                textAlign: TextAlign.center,
              )),
        ],
      ),
    );
  }
}
