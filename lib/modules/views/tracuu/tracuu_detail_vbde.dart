import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_detail_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class TraCuuDetailVbde extends GetView<TraCuuDetailController> {
  final SetupController setupController = Get.find();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      child: Obx(
        () => (controller.isLoadData.value == false)
            ? Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: WillPopScope(
                    child: Center(
                      child: SpinKitFadingCircle(
                          color: Get.isDarkMode
                              ? AppColor.yellowColor
                              : AppColor.blueAccentColor,
                          size: 40),
                    ),
                    onWillPop: () => Future.value(false)),
              )
            : Column(
                children: [
                  Container(
                    child: Column(
                      children: [
                        Card(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: "Trích yếu: ",
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetailVbde.value.trichYeu
                                          .toString(),
                                      style: const TextStyle(
                                          fontWeight: FontWeight
                                              .bold), // Phong cách in đậm
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Số ký hiệu: ',
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetailVbde.value.soKyHieu,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color:
                                              Colors.red), // Phong cách in đậm
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Loại văn bản: ',
                                  style: DefaultTextStyle.of(context)
                                      .style, // Sử dụng phong cách mặc định
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller
                                            .dataDetailVbde.value!.tenLoaiVanBan
                                        // Phong cách in đậm
                                        )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Số bản phát hành: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: controller
                                          .dataDetailVbde.value!.soBanPhatHanh,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'CQBH: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetailVbde.value
                                            .tenCoQuanBanHanh,
                                        style: const TextStyle(
                                            color: AppColor.helpBlue))
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Ngày lưu: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller.dataDetailVbde.value
                                                    ?.ngayLuu ==
                                                null
                                            ? ""
                                            : DateFormat('dd/MM/yyyy').format(
                                                controller.dataDetailVbde.value
                                                    .ngayLuu!))
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Định danh: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: controller
                                            .dataDetailVbde.value.maDinhDanhVb)
                                  ],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 15, top: 10),
                              child: RichText(
                                text: TextSpan(
                                  text: 'Vai trò: ',
                                  style: DefaultTextStyle.of(context).style,
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: (controller.dataDetailVbde.value
                                                    .maYeuCau ==
                                                1
                                            ? ("Phối hợp xử lý")
                                            : (controller.dataDetailVbde.value
                                                        .maYeuCau ==
                                                    2)
                                                ? ("Xử lý chính")
                                                : ("Xem để biết")),
                                        style: const TextStyle(
                                            color: AppColor.helpBlue))
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              child: ListView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  children: List.generate(
                                      (controller.dataDetailVbde.value
                                                  ?.fileVanBan ==
                                              null)
                                          ? 0
                                          : MethodUntils.getFileChiTietTTDH(
                                                  controller.dataDetailVbde
                                                      .value.fileVanBan!
                                                      .split(":"))
                                              .length, (index) {
                                    return ListTile(
                                      title: GestureDetector(
                                        onTap: () {
                                          ModalViewFileOnline.ViewFileOnline(
                                              tenFile: MethodUntils.getFileName(
                                                  controller.dataDetailVbde
                                                      .value.fileVanBan!
                                                      .split(":")[index]
                                                      .toString()),
                                              path:
                                                  MethodUntils.getFileChiTietTTDH(
                                                          controller
                                                              .dataDetailVbde
                                                              .value
                                                              .fileVanBan!
                                                              .split(
                                                                  ":"))[index]
                                                      .urlViewFile!,
                                              item: controller.dataDetailVbde
                                                  .value.fileVanBan!
                                                  .split(":")[index]);
                                          // setupController.openFile(
                                          //     url: MethodUntils
                                          //             .getFileChiTietTTDH(
                                          //                 controller
                                          //                     .dataDetailVbde
                                          //                     .value
                                          //                     .fileVanBan!
                                          //                     .split(
                                          //                         ":"))[index]
                                          //         .urlViewFile!,
                                          //     fileName:
                                          //         MethodUntils.getFileName(
                                          //             controller.dataDetailVbde
                                          //                 .value.fileVanBan!
                                          //                 .split(":")[index]
                                          //                 .toString()));
                                        },
                                        child: Row(
                                          children: [
                                            Icon(
                                              MethodUntils.getFileChiTietTTDH(
                                                      controller.dataDetailVbde
                                                          .value.fileVanBan!
                                                          .split(":"))[index]
                                                  .iconFile,
                                              color: MethodUntils
                                                      .getFileChiTietTTDH(
                                                          controller
                                                              .dataDetailVbde
                                                              .value
                                                              .fileVanBan!
                                                              .split(
                                                                  ":"))[index]
                                                  .colorIcon,
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 8.0),
                                              child: Text(
                                                  MethodUntils.getFileName(
                                                      controller.dataDetailVbde
                                                          .value.fileVanBan!
                                                          .split(":")[index]
                                                          .toString()),
                                                  style: const TextStyle(
                                                      color:
                                                          AppColor.blackColor,
                                                      fontSize: 13)),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  })),
                            ),
                            const Padding(
                              padding: EdgeInsets.only(bottom: 10),
                            ),
                            const Divider(
                              height: 2,
                              color: AppColor.greyColor,
                            ),
                          ],
                        )),
                        Visibility(
                          visible: controller.dsButphe.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.all(10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                const Text("Tổng hợp ý kiến xử lý",
                                    style: TextStyle(
                                        color: AppColor.blackColor,
                                        fontWeight: FontWeight.w600)),
                                const Padding(
                                    padding: EdgeInsets.only(bottom: 10)),
                                Obx(() => Column(
                                    children: controller.dsButphe
                                        .map((butPhe) => Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  const SizedBox(
                                                    height: 5,
                                                  ),
                                                  Text(
                                                    "${butPhe.hoVaTenCanBo} - ${butPhe.tenDonVi} (${butPhe.ngayNhan})",
                                                    style: const TextStyle(
                                                        color:
                                                            AppColor.helpBlue),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(5),
                                                    child: Text(
                                                        "Chuyển: ${MethodUntils.getChuoiNguoiNhan(butPhe.tenNguoiNhan!)}"),
                                                  ),
                                                  (butPhe.noiDungChuyen == null)
                                                      ? const Text("")
                                                      : Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(5),
                                                          child: Text(
                                                              "Nội dung: ${butPhe.noiDungChuyen}"),
                                                        ),
                                                  const Divider(
                                                    color: Colors.grey,
                                                  )
                                                ]))
                                        .toList()))
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    ));
  }
}
