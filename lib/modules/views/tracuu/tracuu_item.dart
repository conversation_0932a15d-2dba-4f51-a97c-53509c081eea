import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';

class ItemTraCuuVanBan extends StatelessWidget {
  final String trichYeu;
  final String sokyhieu;
  final String ngayBanHanh;
  final String loaiVanBan;
  final String nguoiky;
  final VoidCallback onClickItem;
  const ItemTraCuuVanBan(
      {super.key,
      required this.trichYeu,
      required this.sokyhieu,
      required this.ngayBanHanh,
      required this.loaiVanBan,
      required this.nguoiky,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(width: 0.3))),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Obx(() => Obx(() => Obx(() => Text(trichYeu, style: FontSizeHelper.getTextStyle(
                              fontWeight: FontWeight.bold,
                            ))))),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Obx(() => Obx(() => Obx(() => Text(sokyhieu, style: FontSizeHelper.getTextStyle(
                              fontWeight: FontWeight.bold,
                            ))))),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Obx(() => Text(
                              "Ngày ban hanh: $ngayBanHanh",
                              style: FontSizeHelper.getCaptionStyle(),
                            )),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Obx(() => Text(
                              "Loại: $loaiVanBan",
                              style: FontSizeHelper.getCaptionStyle(),
                            )),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Obx(() => Text(
                              "Người ký: $nguoiky",
                              style: FontSizeHelper.getCaptionStyle(),
                            )),
                      ])),
            ),
          ],
        ),
      ),
      onTap: onClickItem,
    );
  }
}
