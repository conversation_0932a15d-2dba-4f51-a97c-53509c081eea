import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/login/login_controller.dart';

class Login extends GetView<LoginController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: OrientationBuilder(
          builder: (BuildContext context, Orientation orientation) {
        return Container(
          height: orientation == Orientation.landscape
              ? MediaQuery.of(context).size.width
              : MediaQuery.of(context).size.height,
          decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage('images/backgroundCMU.jpg'),
                fit: BoxFit.cover),
          ),
          child: SingleChildScrollView(
            child: Form(
              key: controller.loginFormKey,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20.0, 20, 20, 0),
                child: Column(
                  children: [
                    const SizedBox(height: 40),
                    Image.asset(
                      'images/loginLogo.png',
                      width: 150,
                      height: 150,
                      fit: BoxFit.cover,
                    ),
                    const SizedBox(height: 40),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(10),
                                  topRight: Radius.circular(10))),
                          context: context,
                          builder: (BuildContext context) {
                            return Container(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Obx(() => Obx(() => Text("Chọn đơn vị", style: FontSizeHelper.getTitleStyle(
                                            color: AppColor.blackColor,
                                            fontWeight: FontWeight.bold)))),
                                  ),
                                  const Divider(color: AppColor.helpBlue),
                                  ListTile(
                                    leading: const Icon(
                                      Icons.location_on,
                                      color: AppColor.darkRedColor,
                                    ),
                                    title: Obx(() => Obx(() => Text('Cà Mau', style: FontSizeHelper.getTextStyle(
                                            color: AppColor.blackColor)))),
                                    onTap: () {
                                      controller.changeDomain(0);
                                    },
                                  ),
                                  ListTile(
                                    leading: const Icon(Icons.location_on,
                                        color: AppColor.darkRedColor),
                                    title: Obx(() => Text(
                                          'Cà Mau 1',
                                          style: FontSizeHelper.getTextStyle(
                                              color: AppColor.blackColor),
                                        )),
                                    onTap: () {
                                      controller.changeDomain(1);
                                    },
                                  ),
                                ],
                              ),
                            );
                          },
                        );
                      },
                      child: Container(
                        height: 60,
                        padding: const EdgeInsets.only(left: 20, right: 20),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: AppColor.whiteColor, width: 1),
                            borderRadius: BorderRadius.circular(30)),
                        child: Row(children: [
                          const Icon(
                            Icons.dns_outlined,
                            color: AppColor.whiteColor,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Obx(() => Text(
                                  controller.nameDomain.value,
                                  style: FontSizeHelper.getTextStyle(
                                      color: AppColor.whiteColor),
                                )),
                          ),
                        ]),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Obx(() => TextFormField(
                        style: FontSizeHelper.getTextStyle(color: Colors.white),
                        decoration: InputDecoration(
                            enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(30),
                                borderSide: const BorderSide(
                                    width: 1, color: Colors.white)),
                            focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    width: 1, color: Colors.white),
                                borderRadius: BorderRadius.circular(30)),
                            errorBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    width: 1, color: Colors.white),
                                borderRadius: BorderRadius.circular(30)),
                            focusedErrorBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    width: 1, color: Colors.white),
                                borderRadius: BorderRadius.circular(30)),
                            errorStyle: const TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    width: 1, color: Colors.white),
                                borderRadius: BorderRadius.circular(30)),
                            hintText: 'Tài khoản',
                            hintStyle: FontSizeHelper.getTextStyle(
                                color: Colors.white),
                            prefixIcon: const Icon(
                                Icons.account_circle_outlined,
                                color: Colors.white)),
                        controller: controller.usernameController,
                        validator: (value) {
                          return controller.validateUserName(value!);
                        },
                        onSaved: (value) {
                          controller.username = value!;
                        })),
                    const SizedBox(height: 20),
                    TextFormField(
                      cursorColor: Colors.white,
                      style: FontSizeHelper.getTextStyle(color: Colors.white),
                      decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                  width: 1, color: Colors.white),
                              borderRadius: BorderRadius.circular(30)),
                          focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                  width: 1, color: Colors.white),
                              borderRadius: BorderRadius.circular(30)),
                          errorBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                  width: 1, color: Colors.white),
                              borderRadius: BorderRadius.circular(30)),
                          focusedErrorBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                  width: 1, color: Colors.white),
                              borderRadius: BorderRadius.circular(30)),
                          errorStyle: const TextStyle(color: Colors.white),
                          border: OutlineInputBorder(
                              borderSide: const BorderSide(
                                  width: 1, color: Colors.white),
                              borderRadius: BorderRadius.circular(30)),
                          hintText: "Mật khẩu",
                          hintStyle:
                              FontSizeHelper.getTextStyle(color: Colors.white),
                          prefixIcon: const Icon(Icons.lock_outline,
                              color: Colors.white)),
                      controller: controller.passwordController,
                      obscureText: true,
                      validator: (value) {
                        return controller.validatePassword(value!);
                      },
                      onSaved: (value) {
                        controller.password = value!;
                      },
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Row(
                      children: [
                        Obx(() => Checkbox(
                              activeColor: Colors.grey,
                              checkColor: Colors.white,
                              side: const BorderSide(
                                  color: Colors.white, width: 1.0),
                              value: controller.isRememberLogin.value,
                              onChanged: ((value) {
                                controller.changeRememberlogin(value!);
                              }),
                            )),
                        Obx(() => Text(
                              "Ghi nhớ đăng nhập",
                              style: FontSizeHelper.getTextStyle(
                                  color: Colors.white),
                            ))
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: AppColor.blueAccentColor,
                            minimumSize: Size(
                                (MediaQuery.of(context).size.width - 40), 50),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30))),
                        onPressed: () {
                          controller.getDanhsachcanbo();
                        },
                        child: Obx(() => Text(
                              "ĐĂNG NHẬP",
                              style: FontSizeHelper.getButtonStyle(
                                  color: Colors.white),
                            ))),
                    const SizedBox(
                      height: 10,
                    ),
                    Obx(
                      () => controller.isShowLoginSso.value
                          ? ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColor.blueAccentColor,
                                  minimumSize: Size(
                                      (MediaQuery.of(context).size.width - 40),
                                      50),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(30))),
                              onPressed: () {
                                controller.LoginSSO();
                              },
                              child: Obx(() => Text(
                                    "ĐĂNG NHẬP SSO",
                                    style: FontSizeHelper.getButtonStyle(
                                        color: Colors.white),
                                  )))
                          : const SizedBox(
                              height: 10,
                            ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}
