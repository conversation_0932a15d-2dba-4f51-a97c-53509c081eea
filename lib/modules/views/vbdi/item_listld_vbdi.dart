import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';

class ItemListLdVbdi extends StatelessWidget {
  final String trichYeu;
  final String coQuanBanHanh;
  final String ngayNhan;
  final String doKhan;
  final String doMat;
  final int? isXem;
  final VoidCallback onClickItem;
  const ItemListLdVbdi(
      {super.key,
      required this.trichYeu,
      required this.do<PERSON>han,
      required this.doMat,
      this.isXem,
      required this.coQuanBanHanh,
      required this.ngayNhan,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GestureDetector(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(width: 0.3))),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (isXem == null)
                              ? Obx(() => Obx(() => Obx(() => Text(trichYeu, style: FontSizeHelper.getTextStyle(
                                    fontWeight: FontWeight.bold,
                                  )))))
                              : Obx(() => Text(
                                    trichYeu,
                                    style: FontSizeHelper.getTextStyle(),
                                  )),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Obx(() => Text(
                                "CQBH: $coQuanBanHanh",
                                style: FontSizeHelper.getCaptionStyle(),
                              )),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Obx(() => Text(
                                "Độ khẩn: $doKhan",
                                style: FontSizeHelper.getCaptionStyle(),
                              )),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Obx(() => Text(
                                "Độ mật: $doMat",
                                style: FontSizeHelper.getCaptionStyle(),
                              ))
                        ])),
              ),
              Flexible(
                flex: 1,
                fit: FlexFit.tight,
                child: Container(
                    child: Center(
                  child: Obx(() =>
                      Obx(() => Obx(() => Text(ngayNhan, style: FontSizeHelper.getCaptionStyle())))),
                )),
              ),
            ],
          ),
        ),
        onTap: onClickItem,
      ),
    );
  }
}
