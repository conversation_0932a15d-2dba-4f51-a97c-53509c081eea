import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';

import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dsld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/item_listld_vbdi.dart';

class ChoDuyetVbdi extends GetView<DuyetLDVbdiController> {
  final HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: SizedBox(
                  width: 650,
                  child: TabBar(
                      padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                      labelColor: AppColor.helpBlue,
                      controller: controller.tabController,
                      onTap: (value) {
                        controller.onChangeTab(value);
                      },
                      tabs: [
                        Obx(() => Tab(
                            icon: (homeController.nvVbdiChoDuyet == 0)
                                ? const Text("Chờ duyệt")
                                : badges.Badge(
                                    badgeStyle: const badges.BadgeStyle(
                                      badgeColor: Colors.red,
                                    ),
                                    position: badges.BadgePosition.topEnd(
                                        top: -20, end: -12),
                                    badgeContent: Text(
                                      homeController.nvVbdiChoDuyet.toString(),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                    child: const Text("Chờ duyệt"),
                                  ))),
                        const Tab(text: "Uỷ quyền"),
                        const Tab(text: "Đã chuyển văn thư"),
                        const Tab(text: "Đã phát hành"),
                      ]),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                      cursorColor: const Color.fromARGB(255, 242, 237, 237),
                      style: const TextStyle(color: AppColor.blackColor),
                      onFieldSubmitted: ((value) {
                        controller.setSearchKey(value);
                      }),
                      decoration: const InputDecoration(
                          errorStyle: TextStyle(color: AppColor.helpBlue),
                          border: OutlineInputBorder(
                            borderSide:
                                BorderSide(width: 1, color: AppColor.helpBlue),
                          ),
                          labelStyle: TextStyle(color: AppColor.helpBlue),
                          focusColor: AppColor.blackColor,
                          prefixIcon: Icon(
                            Icons.search_outlined,
                            color: AppColor.helpBlue,
                            size: 20,
                          ),
                          hintText: "Nhập nội dung tìm kiếm...",
                          hintStyle: TextStyle(color: Colors.grey))),
                ),
              )
            ]),
          ),
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsldChoDuyet) => RefreshIndicator(
                              onRefresh: () => controller.loadChoDuyetVbdi(),
                              child: ListView.builder(
                                  controller:
                                      controller.scrollerControllerChoDuyet,
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  itemCount: controller.ListChoDuyetVbdi.length,
                                  itemBuilder: ((context, index) =>
                                      ItemListLdVbdi(
                                        trichYeu:
                                            dsldChoDuyet![index].trichYeu ?? "",
                                        isXem:
                                            dsldChoDuyet![index].xem!.toInt() ??
                                                0,
                                        doMat:
                                            dsldChoDuyet![index].tenCapDoMat!,
                                        doKhan:
                                            dsldChoDuyet![index].tenCapDoKhan!,
                                        coQuanBanHanh: dsldChoDuyet![index]
                                                .tenCoQuanBanHanh ??
                                            "",
                                        ngayNhan:
                                            dsldChoDuyet![index].ngayNhan ?? "",
                                        onClickItem: () {
                                          controller.onDetailVbdi(
                                              dsldChoDuyet![index]
                                                  .maVanBanDiKc!
                                                  .toInt(),
                                              dsldChoDuyet![index]
                                                  .maXuLyDi!
                                                  .toInt(),
                                              controller.indexTabGobal.value);
                                        },
                                      ))),
                            ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsUyQuyen) => controller.indexTabGobal.value != 1
                            ? const Text("")
                            : ListView.builder(
                                controller:
                                    controller.scrollerControllerUyQuyen,
                                physics: const AlwaysScrollableScrollPhysics(),
                                itemCount: controller.UyQuyen.length,
                                itemBuilder: ((context, index) =>
                                    ItemListLdVbdi(
                                      trichYeu:
                                          dsUyQuyen![index].trichYeu ?? "",
                                      isXem:
                                          dsUyQuyen![index].xem!.toInt() ?? 0,
                                      doMat: dsUyQuyen![index].tenCapDoMat!,
                                      doKhan: dsUyQuyen![index].tenCapDoKhan!,
                                      coQuanBanHanh:
                                          dsUyQuyen![index].tenCoQuanBanHanh ??
                                              "",
                                      ngayNhan: dsUyQuyen![index].ngayDi ?? "",
                                      onClickItem: () {
                                        controller.onDetailVbdi(
                                            dsUyQuyen![index]
                                                .maVanBanDiKc!
                                                .toInt(),
                                            dsUyQuyen![index].maXuLyDi!.toInt(),
                                            controller.indexTabGobal.value);
                                      },
                                    ))),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsDaChuyenVt) => RefreshIndicator(
                              onRefresh: () => controller.loadDaChuyenVt(),
                              child: ListView.builder(
                                  controller:
                                      controller.scrollerControllerDaChuyenVt,
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  itemCount: controller.daChuyenVanThu.length,
                                  itemBuilder: ((context, index) =>
                                      ItemListLdVbdi(
                                        trichYeu:
                                            dsDaChuyenVt![index].trichYeu ?? "",
                                        isXem:
                                            dsDaChuyenVt![index].xem!.toInt() ??
                                                0,
                                        doMat:
                                            dsDaChuyenVt![index].tenCapDoMat!,
                                        doKhan:
                                            dsDaChuyenVt![index].tenCapDoKhan!,
                                        coQuanBanHanh: dsDaChuyenVt![index]
                                                .tenCoQuanBanHanh ??
                                            "",
                                        ngayNhan:
                                            dsDaChuyenVt![index].ngayDi ?? "",
                                        onClickItem: () {
                                          controller.onDetailVbdi(
                                              dsDaChuyenVt![index]
                                                  .maVanBanDiKc!
                                                  .toInt(),
                                              dsDaChuyenVt![index]
                                                  .maXuLyDi!
                                                  .toInt(),
                                              controller.indexTabGobal.value);
                                        },
                                      ))),
                            ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsDaPhatHanh) => controller.indexTabGobal.value != 3
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () async =>
                                    controller.loadDaPhatHanh(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerDaPhatHanh,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.daPhatHanh.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListLdVbdi(
                                          trichYeu:
                                              dsDaPhatHanh![index].trichYeu ??
                                                  "",
                                          isXem: dsDaPhatHanh![index]
                                                  .xem!
                                                  .toInt() ??
                                              0,
                                          doMat:
                                              dsDaPhatHanh![index].tenCapDoMat!,
                                          doKhan: dsDaPhatHanh![index]
                                              .tenCapDoKhan!,
                                          coQuanBanHanh: dsDaPhatHanh![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayNhan:
                                              dsDaPhatHanh![index].ngayDi ?? "",
                                          onClickItem: () {
                                            controller.onDetailVbdi(
                                                dsDaPhatHanh![index]
                                                    .maVanBanDiKc!
                                                    .toInt(),
                                                dsDaPhatHanh![index]
                                                    .maXuLyDi!
                                                    .toInt(),
                                                controller.indexTabGobal.value);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
