import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ModalWebViewCa extends GetView<KySoController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          foregroundColor: Colors.white,
          title: Text(
            controller.fileNameKySo!.value.toString(),
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: AppColor.blueAccentColor,
          actions: [
            IconButton(
                onPressed: () => controller.showListMauCks(1),
                icon: const Icon(Icons.key_sharp, color: Colors.white))
          ]),
      body: Column(
        children: [
          Container(
            color: Colors.black,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                    style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all<Color>(Colors.green)),
                    onPressed: () {
                      controller.onPressActionKs("pressAdd");
                    },
                    child: const Text(
                      "Chọn vị trí",
                      style: TextStyle(color: Colors.white),
                    )),
                Padding(
                  padding: const EdgeInsets.only(left: 5, right: 5),
                  child: ElevatedButton(
                      style: ButtonStyle(
                          backgroundColor:
                              MaterialStateProperty.all<Color>(Colors.red)),
                      onPressed: () {
                        controller.postMessage("pressDelete");
                      },
                      child: const Text(
                        "Xoá",
                        style: TextStyle(color: Colors.white),
                      )),
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all<Color>(Colors.blue)),
                    onPressed: () {
                      controller.postMessage("pressSign");
                    },
                    child: const Text("Ký số vị trí",
                        style: TextStyle(color: Colors.white)))
              ],
            ),
          ),
          Expanded(
            child: WebViewWidget(
                controller: controller.webViewController
                  ..setJavaScriptMode(JavaScriptMode.unrestricted)
                  ..loadRequest(Uri.parse(controller.urlViewKyso.value))
                  ..addJavaScriptChannel("flutterToWebView",
                      onMessageReceived: (JavaScriptMessage message) {
                    controller.onMessageChannelKs(message);
                  })),
          ),
        ],
      ),
    );
  }
}
