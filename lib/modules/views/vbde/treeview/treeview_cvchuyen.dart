import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/global_widget/custom_tree.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulycv_vbde_controller.dart';

import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';

class TreeViewDsCbChuyenVbde extends StatelessWidget {
  final XuLyCvVbdeController xulyCvController;
  TreeViewDsCbChuyenVbde({super.key, required this.xulyCvController});

  Widget _buildTreeNodes(TreeNodes nodes) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            nodes.expanded.value = !nodes.expanded.value;
          },
          child: Row(
            children: [
              if (nodes.expanded.value && nodes.children.isNotEmpty)
                const Expanded(
                    flex: 0,
                    child: Icon(
                      Icons.indeterminate_check_box,
                      color: Colors.grey,
                    ))
              else if (nodes.children.isEmpty)
                Expanded(flex: 0, child: Container())
              else
                const Expanded(
                    flex: 0,
                    child: Icon(
                      Icons.add_box,
                      color: Colors.grey,
                    )),
              Expanded(flex: 1, child: Text(nodes.title)),
              Expanded(
                flex: 0,
                child: Row(
                  children: [
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        side: const BorderSide(color: Colors.red),
                        value: xulyCvController.selectedXlc.contains(nodes.id),
                        onChanged: (value) {
                          xulyCvController.checkChoseXlc(
                              nodes.id.toString(), value!);
                        },
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        value:
                            xulyCvController.selectedPhArrr.contains(nodes.id),
                        side: const BorderSide(color: Colors.blue),
                        onChanged: (value) {
                          xulyCvController.checkAllPhxl(
                              nodes.id.toString(), value!);
                        },
                      ),
                    ),
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Checkbox(
                        value:
                            xulyCvController.selectedXDBArr.contains(nodes.id),
                        onChanged: (value) {
                          xulyCvController.checkAllxdb(
                              nodes.id.toString(), value!);
                        },
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        if (nodes.expanded.value)
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: nodes.children.length,
              itemBuilder: (context, index) =>
                  Obx(() => _buildTreeNodes(nodes.children[index])),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => xulyCvController.treeDsCbChuyenVbde.value.id == null
        ? Container(
            child: Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Lottie.asset("lottie/emptyBox.json", height: 200, width: 200),
                const Text("Không có cán bộ!")
              ],
            )),
          )
        : _buildTreeNodes(xulyCvController.treeDsCbChuyenVbde.value));
  }
}
