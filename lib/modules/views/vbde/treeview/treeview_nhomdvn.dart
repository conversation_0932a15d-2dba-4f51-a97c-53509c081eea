import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/global_widget/custom_tree.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';

import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';

class TreeViewNhomDVN extends StatelessWidget {
  final XuLyLdVbdeController xulyController;
  TreeViewNhomDVN({super.key, required this.xulyController});

  Widget _buildTreeNodes(TreeNodes nodes) {
    return Column(
      children: [
        nodes.hidenRoot.value
            ? Container()
            : GestureDetector(
                onTap: () {
                  nodes.expanded.value = !nodes.expanded.value;
                },
                child: Row(
                  children: [
                    if (nodes.expanded.value && nodes.children.isNotEmpty)
                      const Expanded(
                          flex: 0,
                          child: Icon(
                            Icons.indeterminate_check_box,
                            color: Colors.grey,
                          ))
                    else if (nodes.children.isEmpty)
                      Expanded(flex: 0, child: Container())
                    else
                      const Expanded(
                          flex: 0,
                          child: Icon(
                            Icons.add_box,
                            color: Colors.grey,
                          )),
                    Expanded(flex: 1, child: Text(nodes.title)),
                    Expanded(
                      flex: 0,
                      child: Row(
                        children: [
                          nodes.id!.contains('CB')
                              ? SizedBox(
                                  height: 40,
                                  width: 40,
                                  child: Checkbox(
                                    side: const BorderSide(color: Colors.red),
                                    value: xulyController.selectedXlc
                                        .contains(nodes.id),
                                    onChanged: (value) {
                                      xulyController.checkChoseXlc(
                                          nodes.id.toString(), value!);
                                    },
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            height: 40,
                            width: 40,
                            child: Checkbox(
                              value: xulyController.selectedPhArrr
                                  .contains(nodes.id),
                              side: const BorderSide(color: Colors.blue),
                              onChanged: (value) {
                                xulyController.checkAllPhxl(
                                    nodes.id.toString(), value!);
                              },
                            ),
                          ),
                          SizedBox(
                            height: 40,
                            width: 40,
                            child: Checkbox(
                              value: xulyController.selectedXDBArr
                                  .contains(nodes.id),
                              onChanged: (value) {
                                xulyController.checkAllxdb(
                                    nodes.id.toString(), value!);
                              },
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
        if (nodes.expanded.value)
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: nodes.children.length,
              itemBuilder: (context, index) =>
                  Obx(() => _buildTreeNodes(nodes.children[index])),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => xulyController.treeNhomDVN.value.children.isEmpty
        ? Container(
            child: Center(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Lottie.asset("lottie/emptyBox.json", height: 200, width: 200),
                const Text("Không có nhóm đơn vị!")
              ],
            )),
          )
        : _buildTreeNodes(xulyController.treeNhomDVN.value));
  }
}
