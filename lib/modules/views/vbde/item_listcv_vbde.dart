import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';

class ItemListCvVbde extends StatelessWidget {
  final String trichYeu;
  final String soKyHieu;
  final String coQuanBanHanh;
  final String ngayVanBanDen;
  final int vaiTro;
  final DateTime? isXem;
  final VoidCallback onClickItem;
  const ItemListCvVbde(
      {super.key,
      required this.trichYeu,
      required this.soKyHieu,
      this.isXem,
      required this.vaiTro,
      required this.coQuanBanHanh,
      required this.ngayVanBanDen,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GestureDetector(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(width: 0.3))),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (isXem == null)
                              ? Obx(() => Obx(() => Obx(() => Text(trichYeu, style: FontSizeHelper.getTextStyle(
                                    fontWeight: FontWeight.bold,
                                  )))))
                              : Obx(() => Text(
                                    trichYeu,
                                    style: FontSizeHelper.getTextStyle(),
                                  )),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Obx(() => Text("Số: $soKyHieu",
                              style: (isXem == null)
                                  ? FontSizeHelper.getTextStyle(
                                      fontWeight: FontWeight.bold,
                                    )
                                  : FontSizeHelper.getTextStyle(
                                      fontWeight: FontWeight.normal,
                                    ))),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Obx(() => Text(
                                "CQBH: $coQuanBanHanh",
                                style: FontSizeHelper.getCaptionStyle(),
                              )),
                          (vaiTro == 2)
                              ? RichText(
                                  text: TextSpan(
                                    text: 'Vai trò: ',
                                    style: DefaultTextStyle.of(context)
                                        .style, // Sử dụng phong cách mặc định
                                    children: <TextSpan>[
                                      TextSpan(
                                        text: "Xử lý chính",
                                        style: const TextStyle(
                                            color: Colors.red,
                                            fontWeight: FontWeight
                                                .bold), // Phong cách in đậm
                                      )
                                    ],
                                  ),
                                )
                              : (vaiTro == 3)
                                  ? RichText(
                                      text: TextSpan(
                                        text: 'Vai trò: ',
                                        style: DefaultTextStyle.of(context)
                                            .style, // Sử dụng phong cách mặc định
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "Phối hợp",
                                            style: const TextStyle(
                                                color: Colors.blue,
                                                fontWeight: FontWeight
                                                    .bold), // Phong cách in đậm
                                          )
                                        ],
                                      ),
                                    )
                                  : (vaiTro == 5)
                                      ? RichText(
                                          text: TextSpan(
                                          text: 'Vai trò: ',
                                          style: DefaultTextStyle.of(context)
                                              .style, // Sử dụng phong cách mặc định
                                          children: <TextSpan>[
                                            TextSpan(
                                              text: "Được trả lại",
                                              style: const TextStyle(
                                                  color: Colors.orangeAccent,
                                                  fontWeight: FontWeight
                                                      .bold), // Phong cách in đậm
                                            )
                                          ],
                                        ))
                                      : RichText(
                                          text: TextSpan(
                                            text: 'Vai trò: ',
                                            style: DefaultTextStyle.of(context)
                                                .style, // Sử dụng phong cách mặc định
                                            children: <TextSpan>[
                                              TextSpan(
                                                text: "Xem để biết",
                                                style: const TextStyle(
                                                    color: Colors.green,
                                                    fontWeight: FontWeight
                                                        .bold), // Phong cách in đậm
                                              )
                                            ],
                                          ),
                                        )
                        ])),
              ),
              Flexible(
                flex: 1,
                fit: FlexFit.tight,
                child: Container(
                    child: Center(
                  child: Obx(() => Obx(() => Obx(() => Text(ngayVanBanDen, style: FontSizeHelper.getCaptionStyle())))),
                )),
              ),
            ],
          ),
        ),
        onTap: onClickItem,
      ),
    );
  }
}
