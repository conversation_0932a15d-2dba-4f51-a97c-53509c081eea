import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';

class HoanTatVbde extends GetView<XuLyLdVbdeController> {
  const HoanTatVbde({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
              padding: const EdgeInsets.only(left: 10, top: 10),
              child: Text("Ý kiến xử lý")),
          Padding(
            padding: const EdgeInsets.all(10),
            child: TextFormField(
              maxLines: 10,
              keyboardType: TextInputType.multiline,
              controller: controller.yKienInputController,
              onSaved: (value) {
                controller.yKienInputController.text = value!;
              },
              validator: (value) {
                if (value!.isEmpty) {
                  return '';
                }
                return null;
              },
              decoration: const InputDecoration(
                labelText: '',
                border: OutlineInputBorder(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
