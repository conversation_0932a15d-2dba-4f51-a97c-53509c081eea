import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/input_datetime.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulycv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/treeview/treeview_cvchuyen.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/treeview/treeview_nhomdvn_cv.dart';

class CvChuyenXLVbde extends GetView<XuLyCvVbdeController> {
  @override
  Widget build(BuildContext context) {
    DateTime initdataTime = DateTime.now();
    return Container(
        child: Column(
      children: [
        Row(
          children: [
            const Text("Trạng thái"),
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.all(5),
                child: GestureDetector(
                  onTap: () {
                    showModalBottomSheet(
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10))),
                      context: context,
                      builder: (BuildContext context) {
                        return Container(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(5),
                                child: Text("Chọn trạng thái",
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall!
                                        .copyWith(
                                          color: AppColor.greyColor,
                                        )),
                              ),
                              const Divider(color: AppColor.helpBlue),
                              ListTile(
                                title: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text('Đang xử lý',
                                        style: TextStyle(
                                            color: AppColor.helpBlue)),
                                  ],
                                ),
                                onTap: () {
                                  controller.onChangeTrangThai(0);
                                },
                              ),
                              ListTile(
                                title: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'Đã hoàn tất',
                                      style:
                                          TextStyle(color: AppColor.helpBlue),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  controller.onChangeTrangThai(1);
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.greyColor, width: 1),
                        borderRadius: BorderRadius.circular(5)),
                    child: Row(children: [
                      Expanded(
                        flex: 5,
                        child: Obx(() => Text(
                              "${controller.trangThaiXuLy.value}",
                              style: const TextStyle(
                                  color: AppColor.blackColor, fontSize: 15.0),
                            )),
                      ),
                      const Expanded(
                        flex: 1,
                        child: Icon(
                          size: 30.0,
                          Icons.arrow_drop_down,
                          color: Colors.blue,
                        ),
                      )
                    ]),
                  ),
                ),
              ),
            ),
          ],
        ),
        Row(
          children: [
            const Text("Hạn xử lý"),
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.only(left: 10, right: 5, bottom: 5),
                child: Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColor.greyColor, width: 1),
                        borderRadius: BorderRadius.circular(5)),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              InputDateTime.showcustomDatePicker(
                                  selectTime: controller.selectDateTime,
                                  initDateTime: initdataTime);
                            },
                            child: Obx(
                              () => Text(
                                controller.hanxuly,
                                style: const TextStyle(
                                  color: AppColor.greyColor,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          TextButton.icon(
                            onPressed: () {
                              controller.clearDateTime();
                            },
                            icon: const Icon(
                              Icons.delete,
                              color: Colors.red,
                            ),
                            label: const Text(""),
                          )
                        ])),
              ),
            ),
          ],
        ),
        Row(children: [
          const Text("Ý kiến"),
          Expanded(
            flex: 1,
            child: Padding(
              padding: const EdgeInsets.only(left: 30, right: 5.0),
              child: SizedBox(
                child: TextFormField(
                  maxLines: 2,
                  keyboardType: TextInputType.multiline,
                  decoration: const InputDecoration(
                    hintText: 'Nhập ý kiến...',
                    border: OutlineInputBorder(),
                  ),
                  controller: controller.yKienInputController,
                  onSaved: (value) {
                    controller.yKienInputController.text = value!;
                  },
                ),
              ),
            ),
          ),
        ]),
        Padding(
          padding: const EdgeInsets.only(left: 58),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Obx(() => Checkbox(
                  value: controller.isSms.value,
                  onChanged: (value) {
                    controller.changeIsSms(value!);
                  })),
              const Text("Sms"),
            ],
          ),
        ),
        Container(
            color: AppColor.blueAccentColor,
            child: TabBar(
                indicatorColor: Colors.white,
                unselectedLabelColor: Colors.grey,
                labelColor: AppColor.whiteColor,
                controller: controller.tabTreeController,
                tabs: controller.tabsTree)),
        Expanded(
          child: TabBarView(
            controller: controller.tabTreeController,
            children: [
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(5),
                      color: Colors.grey[200],
                      child: Row(
                        children: const [
                          Expanded(
                              flex: 1, child: Center(child: Text("Họ tên"))),
                          Expanded(flex: 0, child: Text("XLC      PH    Xem")),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: TreeViewDsCbChuyenVbde(
                            xulyCvController: controller,
                          ),
                        ))
                  ],
                ),
              ),
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(5),
                      color: Colors.grey[200],
                      child: Row(
                        children: const [
                          Expanded(
                              flex: 1, child: Center(child: Text("Họ tên"))),
                          Expanded(flex: 0, child: Text("XLC      PH    Xem")),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: TreeViewNhomDVNCv(xulyController: controller),
                        ))
                  ],
                ),
              ),
            ],
          ),
        )
      ],
    ));
  }
}
