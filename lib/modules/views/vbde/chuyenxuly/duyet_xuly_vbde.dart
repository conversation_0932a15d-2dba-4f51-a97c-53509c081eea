import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/input_datetime.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/treeview/treeview_cdtt.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/treeview/treeview_duyet.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/treeview/treeview_nhomdvn.dart';

import '../../../../global_widget/custom_tree.dart';

class DuyetXuLyVbde extends GetView<XuLyLdVbdeController> {
  @override
  Widget build(BuildContext context) {
    DateTime initdataTime = DateTime.now();
    return Container(
      child: Column(children: [
        Padding(
          padding: const EdgeInsets.only(left: 5, top: 5),
          child: Row(
            children: [
              const Text("Hạn xử lý"),
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.only(left: 5, right: 5),
                  child: Container(
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: AppColor.greyColor, width: 1),
                          borderRadius: BorderRadius.circular(5)),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                InputDateTime.showcustomDatePicker(
                                    selectTime: controller.selectDateTime,
                                    initDateTime: initdataTime);
                              },
                              child: Obx(
                                () => Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    controller.hanxuly,
                                    style: const TextStyle(
                                      color: AppColor.greyColor,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () {
                                controller.clearDateTime();
                              },
                              icon: const Icon(
                                Icons.delete,
                                color: Colors.red,
                              ),
                              label: const Text(""),
                            )
                          ])),
                ),
              )
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 5, top: 5),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            const Padding(
              padding: EdgeInsets.only(right: 25.0),
              child: Text("Ý kiến"),
            ),
            Expanded(
              flex: 5,
              child: Padding(
                padding: const EdgeInsets.only(right: 5.0),
                child: SizedBox(
                  child: TextFormField(
                    maxLines: 2,
                    keyboardType: TextInputType.multiline,
                    decoration: const InputDecoration(
                      hintText: 'Nhập văn bản',
                      border: OutlineInputBorder(),
                    ),
                    controller: controller.yKienLDDInputController,
                    onSaved: (value) {
                      controller.yKienLDDInputController.text = value!;
                    },
                    validator: (value) {
                      if (value!.isEmpty) {
                        return '';
                      }
                      return null;
                    },
                  ),
                ),
              ),
            ),
          ]),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 58),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Obx(() => Checkbox(
                  value: controller.isSms.value,
                  onChanged: (value) {
                    controller.changeIsSms(value!);
                  })),
              const Text("Sms"),
              Obx(() => Checkbox(
                  value: controller.isCanPhucDap.value,
                  onChanged: (value) {
                    controller.changeIsCanPhucDap(value!);
                  })),
              const Text("Cần phúc đáp"),
            ],
          ),
        ),
        Container(
            color: AppColor.blueAccentColor,
            child: TabBar(
                indicatorColor: Colors.white,
                unselectedLabelColor: Colors.grey,
                labelColor: AppColor.whiteColor,
                controller: controller.tabTreeController,
                tabs: controller.tabsTree)),
        Expanded(
          child: TabBarView(
            controller: controller.tabTreeController,
            children: [
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(5),
                      color: Colors.grey,
                      child: const Row(
                        children: [
                          Expanded(
                              flex: 1, child: Center(child: Text("Họ tên"))),
                          Expanded(flex: 1, child: Text("XLC      PH    Xem")),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: TreeViewDuyet(
                            xulyController: controller,
                          ),
                        ))
                  ],
                ),
              ),
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(5),
                      color: Colors.grey,
                      child: const Row(
                        children: [
                          Expanded(
                              flex: 1, child: Center(child: Text("Họ tên"))),
                          Expanded(flex: 1, child: Text("XLC      PH    Xem")),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: TreeViewCDTT(
                            xulyController: controller,
                          ),
                        ))
                  ],
                ),
              ),
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(5),
                      color: Colors.grey,
                      child: const Row(
                        children: [
                          Expanded(
                              flex: 1, child: Center(child: Text("Họ tên"))),
                          Expanded(flex: 1, child: Text("XLC      PH    Xem")),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: TreeViewNhomDVN(
                            xulyController: controller,
                          ),
                        ))
                  ],
                ),
              ),
            ],
          ),
        )
      ]),
    );
  }
}
