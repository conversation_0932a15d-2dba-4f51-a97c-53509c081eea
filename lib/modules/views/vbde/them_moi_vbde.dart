import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyvt_vbde_controller.dart';

class ThemMoiVbdeVt extends GetView<XuLyVtVbdeController> {
  ThemMoiVbdeVt({super.key});
  final SetupController setupController = Get.find();
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(
        () => (controller.isLoadData.value == false)
            ? Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: WillPopScope(
                    child: Center(
                      child: SpinKitFadingCircle(
                          color: Get.isDarkMode
                              ? AppColor.yellowColor
                              : AppColor.blueAccentColor,
                          size: 40),
                    ),
                    onWillPop: () => Future.value(false)),
              )
            : Card(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                  child: Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Số ký hiệu",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 19.0)),
                          controller: controller.inputSoKyHieu,
                          onSaved: (value) {},
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Trích yếu",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 19.0)),
                          maxLines: 3,
                          controller: controller.inputTrichYeu,
                          onSaved: (value) {},
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: "Loại văn bản",
                                  labelStyle: TextStyle(
                                      color: AppColor.greyColor,
                                      fontSize: 18.0),
                                  suffixIcon: Padding(
                                      padding:
                                          EdgeInsetsDirectional.only(top: 20),
                                      child: Icon(Icons
                                          .arrow_drop_down) // myIcon is a 48px-wide widget.
                                      ),
                                ),
                                controller: controller.inputLoaiVanBan,
                                onTap: () {
                                  showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10),
                                            topRight: Radius.circular(10))),
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: <Widget>[
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text("Chọn loại văn bản",
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleSmall!
                                                      .copyWith(
                                                          color: AppColor
                                                              .greyColor,
                                                          fontWeight:
                                                              FontWeight.bold)),
                                            ),
                                            const Divider(
                                                color: AppColor.greyColor),
                                            Expanded(
                                              child: ListView.builder(
                                                itemCount: controller
                                                    .DmLoaiVanBan.length,
                                                itemBuilder: (context, index) {
                                                  final dsDmLoaiVb = controller
                                                      .DmLoaiVanBan[index];
                                                  return Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        GestureDetector(
                                                          child: Text(
                                                              "${dsDmLoaiVb.tenLoaiVanBan}",
                                                              style: const TextStyle(
                                                                  color: AppColor
                                                                      .helpBlue)),
                                                          onTap: () {
                                                            controller
                                                                .changeLoaiVanBan(
                                                                    dsDmLoaiVb);
                                                          },
                                                        ),
                                                        const Divider(
                                                            color: AppColor
                                                                .greyColor),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                    label: Text("Số phát hành"),
                                    labelStyle: TextStyle(
                                        color: AppColor.greyColor,
                                        fontSize: 18.0)),
                                controller: controller.inputSoPhatHanh,
                                onSaved: (value) {},
                              ),
                            )
                          ],
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: "Lĩnh vực văn bản",
                            labelStyle: TextStyle(
                                color: AppColor.greyColor, fontSize: 18.0),
                            suffixIcon: Padding(
                                padding: EdgeInsetsDirectional.only(top: 20),
                                child: Icon(Icons
                                    .arrow_drop_down) // myIcon is a 48px-wide widget.
                                ),
                          ),
                          controller: controller.inputLinhVuc,
                          onTap: () {
                            showModalBottomSheet(
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10),
                                      topRight: Radius.circular(10))),
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: <Widget>[
                                      Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text("Chọn lĩnh vực văn bản",
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(
                                                    color: AppColor.greyColor,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      const Divider(color: AppColor.greyColor),
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount:
                                              controller.dmLinhVuc.length,
                                          itemBuilder: (context, index) {
                                            final dsLinhVuc =
                                                controller.dmLinhVuc[index];
                                            return Container(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  GestureDetector(
                                                    child: Text(
                                                        "${dsLinhVuc.tenLinhVucVanBan}",
                                                        style: const TextStyle(
                                                            color: AppColor
                                                                .helpBlue)),
                                                    onTap: () {
                                                      controller.changeLinhVuc(
                                                          dsLinhVuc);
                                                    },
                                                  ),
                                                  const Divider(
                                                      color:
                                                          AppColor.greyColor),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: "Sổ văn bản",
                            labelStyle: TextStyle(
                                color: AppColor.greyColor, fontSize: 18.0),
                            suffixIcon: Padding(
                                padding: EdgeInsetsDirectional.only(top: 20),
                                child: Icon(Icons
                                    .arrow_drop_down) // myIcon is a 48px-wide widget.
                                ),
                          ),
                          controller: controller.inputSoVanBan,
                          onTap: () {
                            showModalBottomSheet(
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10),
                                      topRight: Radius.circular(10))),
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: <Widget>[
                                      Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text("Chọn sổ văn bản",
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(
                                                    color: AppColor.greyColor,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      const Divider(color: AppColor.greyColor),
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount: controller.DmSoVbde.length,
                                          itemBuilder: (context, index) {
                                            final dsDmSoVbde = controller
                                                .DmSoVbde[index].tenSoVbDen;
                                            return Container(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  GestureDetector(
                                                    child: Text(
                                                        dsDmSoVbde.toString(),
                                                        style: const TextStyle(
                                                            color: AppColor
                                                                .helpBlue)),
                                                    onTap: () {
                                                      controller
                                                          .laySoDenBySoVanBan(
                                                              controller
                                                                  .DmSoVbde[
                                                                      index]
                                                                  .maSoVbDenKc!
                                                                  .toInt(),
                                                              dsDmSoVbde
                                                                  .toString());
                                                    },
                                                  ),
                                                  const Divider(
                                                      color:
                                                          AppColor.greyColor),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  labelText: "Năm",
                                  labelStyle: TextStyle(
                                      color: AppColor.greyColor,
                                      fontSize: 18.0),
                                  suffixIcon: Padding(
                                      padding:
                                          EdgeInsetsDirectional.only(top: 20),
                                      child: Icon(Icons
                                          .arrow_drop_down) // myIcon is a 48px-wide widget.
                                      ),
                                ),
                                controller: controller.inputNam,
                                onTap: () {
                                  showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10),
                                            topRight: Radius.circular(10))),
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: <Widget>[
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text("Chọn sổ văn bản",
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleSmall!
                                                      .copyWith(
                                                          color: AppColor
                                                              .greyColor,
                                                          fontWeight:
                                                              FontWeight.bold)),
                                            ),
                                            const Divider(
                                                color: AppColor.greyColor),
                                            Expanded(
                                              child: ListView.builder(
                                                itemCount:
                                                    controller.dsNam.length,
                                                itemBuilder: (context, index) {
                                                  final nam =
                                                      controller.dsNam[index];

                                                  return Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        GestureDetector(
                                                          child: Text(
                                                              nam.toString(),
                                                              style: const TextStyle(
                                                                  color: AppColor
                                                                      .helpBlue)),
                                                          onTap: () {
                                                            controller
                                                                .changeNam(nam);
                                                          },
                                                        ),
                                                        const Divider(
                                                            color: AppColor
                                                                .greyColor),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                  label: Text("Số đến"),
                                  labelStyle: TextStyle(
                                      color: AppColor.greyColor,
                                      fontSize: 18.0),
                                  suffixIcon: Padding(
                                      padding:
                                          EdgeInsetsDirectional.only(top: 20),
                                      child: Icon(Icons
                                          .arrow_drop_down) // myIcon is a 48px-wide widget.
                                      ),
                                ),
                                controller: controller.inputSoDen,
                                onSaved: (value) {},
                              ),
                            )
                          ],
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Cơ quan ban hành",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 18.0)),
                          controller: controller.inputCQBH,
                          onSaved: (value) {},
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Người ký",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 18.0)),
                          controller: controller.inputNguoiKy,
                          onSaved: (value) {},
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                    labelText: "Cấp độ khẩn",
                                    labelStyle: TextStyle(
                                        color: AppColor.greyColor,
                                        fontSize: 18.0),
                                    suffixIcon: Padding(
                                        padding:
                                            EdgeInsetsDirectional.only(top: 20),
                                        child: Icon(Icons
                                            .arrow_drop_down) // myIcon is a 48px-wide widget.
                                        )),
                                controller: controller.inputCapDoKhan,
                                onTap: () {
                                  showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10),
                                            topRight: Radius.circular(10))),
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: <Widget>[
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text("Chọn cấp độ khẩn",
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleSmall!
                                                      .copyWith(
                                                          color: AppColor
                                                              .greyColor,
                                                          fontWeight:
                                                              FontWeight.bold)),
                                            ),
                                            const Divider(
                                                color: AppColor.greyColor),
                                            Expanded(
                                              child: ListView.builder(
                                                itemCount: controller
                                                    .DmCapDoKhan.length,
                                                itemBuilder: (context, index) {
                                                  final tenCapDoKhan =
                                                      controller
                                                          .DmCapDoKhan[index]
                                                          .tenCapDoKhan;
                                                  return Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        GestureDetector(
                                                          child: Text(
                                                              tenCapDoKhan
                                                                  .toString(),
                                                              style: const TextStyle(
                                                                  color: AppColor
                                                                      .helpBlue)),
                                                          onTap: () {
                                                            controller
                                                                .changeCapDoKhan(
                                                                    controller
                                                                            .DmCapDoKhan[
                                                                        index]);
                                                          },
                                                        ),
                                                        const Divider(
                                                            color: AppColor
                                                                .greyColor),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                decoration: const InputDecoration(
                                    label: Text("Cấp độ mật"),
                                    labelStyle: TextStyle(
                                        color: AppColor.greyColor,
                                        fontSize: 18.0),
                                    suffixIcon: Padding(
                                        padding:
                                            EdgeInsetsDirectional.only(top: 20),
                                        child: Icon(Icons
                                            .arrow_drop_down) // myIcon is a 48px-wide widget.
                                        )),
                                controller: controller.inputCapDoMat,
                                onTap: () {
                                  showModalBottomSheet(
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(10),
                                            topRight: Radius.circular(10))),
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: <Widget>[
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text("Chọn cấp độ mật",
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .titleSmall!
                                                      .copyWith(
                                                          color: AppColor
                                                              .greyColor,
                                                          fontWeight:
                                                              FontWeight.bold)),
                                            ),
                                            const Divider(
                                                color: AppColor.greyColor),
                                            Expanded(
                                              child: ListView.builder(
                                                itemCount: controller
                                                    .dmCapDoMat.length,
                                                itemBuilder: (context, index) {
                                                  final tenCapDoMat = controller
                                                      .dmCapDoMat[index]
                                                      .tenCapDoMat;
                                                  return Container(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        GestureDetector(
                                                          child: Text(
                                                              tenCapDoMat
                                                                  .toString(),
                                                              style: const TextStyle(
                                                                  color: AppColor
                                                                      .helpBlue)),
                                                          onTap: () {
                                                            controller
                                                                .changeCapDoMat(
                                                                    controller
                                                                            .dmCapDoMat[
                                                                        index]);
                                                          },
                                                        ),
                                                        const Divider(
                                                            color: AppColor
                                                                .greyColor),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          ],
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Lãnh đạo duyệt",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 18.0),
                              suffixIcon: Padding(
                                  padding: EdgeInsetsDirectional.only(top: 20),
                                  child: Icon(Icons
                                      .arrow_drop_down) // myIcon is a 48px-wide widget.
                                  )),
                          controller: controller.inputLanhDaoDuyet,
                          onTap: () {
                            showModalBottomSheet(
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(10),
                                      topRight: Radius.circular(10))),
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: <Widget>[
                                      Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Text("Chọn lãnh đạo duyệt",
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleSmall!
                                                .copyWith(
                                                    color: AppColor.greyColor,
                                                    fontWeight:
                                                        FontWeight.bold)),
                                      ),
                                      const Divider(color: AppColor.greyColor),
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount: controller
                                              .dsLanhDaoDuyetVbde.length,
                                          itemBuilder: (context, index) {
                                            final tenLanhDao = controller
                                                .dsLanhDaoDuyetVbde[index].ten;
                                            return Container(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  GestureDetector(
                                                    child: Text(
                                                        tenLanhDao.toString(),
                                                        style: const TextStyle(
                                                            color: AppColor
                                                                .helpBlue)),
                                                    onTap: () {
                                                      controller
                                                          .changeLanhDaoDuyetVbde(
                                                              controller
                                                                      .dsLanhDaoDuyetVbde[
                                                                  index]);
                                                    },
                                                  ),
                                                  const Divider(
                                                      color:
                                                          AppColor.greyColor),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Nơi lưu bản chính",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 18.0)),
                          controller: controller.inputNoiLuuBanChinh,
                          onSaved: (value) {},
                        ),
                        TextFormField(
                          decoration: const InputDecoration(
                              labelText: "Ghi chú",
                              labelStyle: TextStyle(
                                  color: AppColor.greyColor, fontSize: 18.0)),
                          controller: controller.inputGhiChu,
                          onSaved: (value) {},
                        ),
                        const Divider(
                          height: 2,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        const Text("Tệp tin đính kèm:",
                            style: TextStyle(
                                color: AppColor.greyColor, fontSize: 15.0)),
                        Container(
                          child: ListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              children: List.generate(
                                  (controller.item.fileVanBan != null)
                                      ? MethodUntils.getFileChiTietTTDH(
                                              controller.item.fileVanBan!
                                                  .split(":"))
                                          .length
                                      : 0, (index) {
                                return GestureDetector(
                                    onTap: () {
                                      ModalViewFileOnline.ViewFileOnline(
                                          tenFile: MethodUntils.getFileName(
                                              controller.item.fileVanBan!
                                                  .split(":")[index]
                                                  .toString()),
                                          path: MethodUntils.getFileChiTietTTDH(
                                                  controller.item.fileVanBan!
                                                      .split(":"))[index]
                                              .urlViewFile!,
                                          item: controller.item.fileVanBan!
                                              .split(":")[index]);
                                      // setupController.openFile(
                                      //     url: MethodUntils.getFileChiTietTTDH(
                                      //             controller.item.fileVanBan!
                                      //                 .split(":"))[index]
                                      //         .urlViewFile!,
                                      //     fileName: MethodUntils.getFileName(
                                      //         controller.item.fileVanBan!
                                      //             .split(":")[index]
                                      //             .toString()));
                                    },
                                    child: ListTile(
                                      leading: Icon(
                                        MethodUntils.getFileChiTietTTDH(
                                                controller.item.fileVanBan!
                                                    .split(":"))[index]
                                            .iconFile,
                                        color: MethodUntils.getFileChiTietTTDH(
                                                controller.item.fileVanBan!
                                                    .split(":"))[index]
                                            .colorIcon,
                                        size: 20,
                                      ),
                                      title: Text(
                                        MethodUntils.getFileName(controller
                                            .item.fileVanBan!
                                            .split(":")[index]
                                            .toString()),
                                        style: const TextStyle(
                                            color: AppColor.blackColor,
                                            fontSize: 12),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ));
                              })),
                        ),
                        Visibility(
                          visible:
                              controller.dsVanBanLienQuan.value!.isNotEmpty,
                          child: const Text(
                            "Văn bản liên quan:",
                            style: TextStyle(
                                color: AppColor.greyColor, fontSize: 15.0),
                          ),
                        ),
                        Container(
                          child: Obx(
                            () => ListView(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                children: List.generate(
                                    controller.dsVanBanLienQuan.value!.isEmpty
                                        ? 0
                                        : MethodUntils.getFileChiTietTTDH(
                                                controller
                                                    .dsVanBanLienQuan.value!
                                                    .split(":"))
                                            .length, (index) {
                                  return ListTile(
                                    leading: Icon(
                                      MethodUntils.getFileChiTietTTDH(controller
                                              .dsVanBanLienQuan.value!
                                              .split(":"))[index]
                                          .iconFile,
                                      color: MethodUntils.getFileChiTietTTDH(
                                              controller.dsVanBanLienQuan.value!
                                                  .split(":"))[index]
                                          .colorIcon,
                                    ),
                                    title: GestureDetector(
                                      onTap: () {
                                        ModalViewFileOnline.ViewFileOnline(
                                            tenFile:
                                                MethodUntils.getFileChiTietTTDH(
                                                        controller
                                                            .dsVanBanLienQuan
                                                            .value!
                                                            .split(":"))[index]
                                                    .fileName!,
                                            item: controller
                                                .dsVanBanLienQuan.value!
                                                .split(":")[index],
                                            path:
                                                MethodUntils.getFileChiTietTTDH(
                                                        controller
                                                            .dsVanBanLienQuan
                                                            .value!
                                                            .split(":"))[index]
                                                    .urlViewFile!);
                                        // setupController.openFile(
                                        //     url:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .urlViewFile!,
                                        //     fileName:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .fileName!,
                                        //     indexFile: index);
                                      },
                                      child: Text(
                                        MethodUntils.getFileName(controller
                                            .dsVanBanLienQuan.value!
                                            .split(":")[index]
                                            .toString()),
                                        style: const TextStyle(
                                            color: AppColor.blackColor,
                                            fontSize: 13),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  );
                                })),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  Obx(() => Checkbox(
                                      value: controller.isSms.value,
                                      onChanged: (value) {
                                        controller.onChangeIsSms(value);
                                      })),
                                  const Text("Sms")
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Visibility(
                                visible: controller.indexTab == 0,
                                child: Row(
                                  children: [
                                    Obx(() => Checkbox(
                                        value: controller.isKemVanBanGiay.value,
                                        onChanged: (value) {
                                          controller
                                              .onChangeKemVanBanGiay(value);
                                        })),
                                    const Text("Kèm văn bản giấy")
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                        const Divider(
                          height: 2,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Visibility(
                              visible: controller.indexTab == 0,
                              child: ElevatedButton(
                                onPressed: () {
                                  controller.onLuuTam();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Lưu chờ chuyển',
                                  style: FontSizeHelper.getTextStyle(, color: Colors.white),
                                ),
                              ),
                            ),
                            Visibility(
                              visible: controller.indexTab == 0,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 5.0),
                                child: ElevatedButton(
                                  onPressed: () {
                                    controller.onGuiLanhDao();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: const Text(
                                    'Chuyển LĐ duyệt',
                                    style: FontSizeHelper.getTextStyle(, color: Colors.white),
                                  ),
                                ),
                              ),
                            ),
                            Visibility(
                              visible: controller.indexTab == 1,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 5.0),
                                child: ElevatedButton(
                                  onPressed: () {
                                    controller.onGuiLanhDao();
                                    // Button onPressed callback
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: const Text(
                                    'chuyển Lãnh đạo duyệt',
                                    style: FontSizeHelper.getTextStyle(, color: Colors.white),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Visibility(
                                visible: controller.indexTab == 0,
                                child: ElevatedButton(
                                  onPressed: () {
                                    controller.onHuy();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: const Text(
                                    'Huỷ',
                                    style: FontSizeHelper.getTextStyle(, color: Colors.white),
                                  ),
                                ),
                              ),
                            ]),
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}
