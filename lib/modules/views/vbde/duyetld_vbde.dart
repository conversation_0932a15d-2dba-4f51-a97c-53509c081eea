import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsld_vbde_controller.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/views/vbde/item_listld_vbde.dart';

class DuyetVbde extends GetView<DuyetVbdeController> {
  final HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(children: [
              TabBar(
                  padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
                  labelColor: AppColor.helpBlue,
                  controller: controller.tabController,
                  onTap: (value) {
                    controller.onChangeTab(value);
                  },
                  tabs: [
                    Obx(() => Tab(
                        icon: (homeController.nvChoDuyet == 0)
                            ? Text(
                                "Chờ duyệt",
                                style: FontSizeHelper.getTextStyle(),
                              )
                            : badges.Badge(
                                badgeStyle: const badges.BadgeStyle(
                                  badgeColor: Colors.red,
                                ),
                                position: badges.BadgePosition.topEnd(
                                    top: -20, end: -12),
                                badgeContent: Text(
                                  homeController.nvChoDuyet.toString(),
                                  style:
                                      FontSizeHelper.getTextStyle(color: Colors.white),
                                ),
                                child: Text(
                                  "Chờ duyệt",
                                  style: FontSizeHelper.getTextStyle(),
                                ),
                              ))),
                    const Tab(text: "Uỷ quyền"),
                    const Tab(text: "Đã duyệt"),
                  ]),
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: SizedBox(
                  height: 45,
                  child: TextFormField(
                      cursorColor: const Color.fromARGB(255, 242, 237, 237),
                      style: const TextStyle(color: AppColor.blackColor),
                      controller: controller.searchController,
                      onFieldSubmitted: (newValue) {
                        controller.setSearchKeyWord(newValue);
                      },
                      decoration: const InputDecoration(
                          errorStyle: TextStyle(color: AppColor.helpBlue),
                          border: OutlineInputBorder(
                            borderSide:
                                BorderSide(width: 1, color: AppColor.helpBlue),
                          ),
                          labelStyle: TextStyle(color: AppColor.helpBlue),
                          focusColor: AppColor.blackColor,
                          prefixIcon: Icon(
                            Icons.search_outlined,
                            color: AppColor.helpBlue,
                            size: 20,
                          ),
                          hintText: "Nhập nội dung tìm kiếm...",
                          hintStyle: FontSizeHelper.getTextStyle())),
                ),
              )
            ]),
          ),
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsldChoDuyet) => controller.indexTabGobal.value != 0
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.getDsChoDuyet(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerChoDuyet,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.dsChoDuyet.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVbde(
                                          trichYeu:
                                              dsldChoDuyet![index].trichYeu ??
                                                  "",
                                          soKyHieu:
                                              dsldChoDuyet![index].soKyHieu ??
                                                  "",
                                          isXem: dsldChoDuyet![index]
                                              .ngayXem
                                              .toString(),
                                          coQuanBanHanh: dsldChoDuyet![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayVanBanDen:
                                              dsldChoDuyet![index].ngayDen ??
                                                  "",
                                          onClickItem: () {
                                            controller.onDetailVbde(
                                                dsldChoDuyet![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsUyDuyet) => controller.indexTabGobal.value != 1
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () =>
                                    controller.getDsLdUyQuyenDuyet(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerDaDuyet,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.dsUyDuyet.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVbde(
                                          trichYeu:
                                              dsUyDuyet![index].trichYeu ?? "",
                                          soKyHieu:
                                              dsUyDuyet![index].soKyHieu ?? "",
                                          isXem: dsUyDuyet![index]
                                              .ngayXem
                                              .toString(),
                                          coQuanBanHanh: dsUyDuyet![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayVanBanDen:
                                              dsUyDuyet![index].ngayDen ?? "",
                                          onClickItem: () {
                                            controller.onDetailVbde(
                                                dsUyDuyet![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
                Container(
                    color: AppColor.whiteColor,
                    child: controller.obx(
                        (dsldDaDuyet) => controller.indexTabGobal.value != 2
                            ? const Text("")
                            : RefreshIndicator(
                                onRefresh: () => controller.getDsDaChuyen(),
                                child: ListView.builder(
                                    controller:
                                        controller.scrollerControllerDaDuyet,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: controller.dsDaDuyet.length,
                                    itemBuilder: ((context, index) =>
                                        ItemListVbde(
                                          trichYeu:
                                              dsldDaDuyet![index].trichYeu ??
                                                  "",
                                          soKyHieu:
                                              dsldDaDuyet![index].soKyHieu ??
                                                  "",
                                          isXem: dsldDaDuyet![index]
                                              .ngayXem
                                              .toString(),
                                          coQuanBanHanh: dsldDaDuyet![index]
                                                  .tenCoQuanBanHanh ??
                                              "",
                                          ngayVanBanDen:
                                              dsldDaDuyet![index].ngayDen ?? "",
                                          onClickItem: () {
                                            controller.onDetailVbde(
                                                dsldDaDuyet![index]);
                                          },
                                        ))),
                              ),
                        onLoading: SpinKitCircle(
                          color: AppColor.blueAccentColor,
                        ),
                        onEmpty: Container(
                          child: Center(
                              child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Lottie.asset("lottie/emptyBox.json",
                                  height: 200, width: 200),
                              const Text("Không tìm thấy dữ liệu!")
                            ],
                          )),
                        ))),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
