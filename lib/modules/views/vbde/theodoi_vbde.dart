import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/theidoi_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/item_listld_vbde.dart';

class VbdeTheoDoiView extends GetView<VbdeTheoDoiController> {
  final HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(children: [
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: Si<PERSON><PERSON><PERSON>(
                  height: 45,
                  child: Text<PERSON><PERSON><PERSON><PERSON>(
                      cursorColor: const Color.fromARGB(255, 242, 237, 237),
                      style: const TextStyle(color: AppColor.blackColor),
                      controller: controller.searchController,
                      onFieldSubmitted: (newValue) {
                        controller.setSearchKeyWord(newValue);
                      },
                      decoration: const InputDecoration(
                          errorStyle: TextStyle(color: AppColor.helpBlue),
                          border: OutlineInputBorder(
                            borderSide:
                                BorderSide(width: 1, color: AppColor.helpBlue),
                          ),
                          labelStyle: TextStyle(color: AppColor.helpBlue),
                          focusColor: AppColor.blackColor,
                          prefixIcon: Icon(
                            Icons.search_outlined,
                            color: AppColor.helpBlue,
                            size: 20,
                          ),
                          hintText: "Nhập nội dung tìm kiếm...",
                          hintStyle: FontSizeHelper.getTextStyle())),
                ),
              )
            ]),
          ),
          Expanded(
            child: Container(
                color: AppColor.whiteColor,
                child: controller.obx(
                    (dsldChoDuyet) => ListView.builder(
                        controller: controller.scrollerControllerTheoDoiVbde,
                        physics: const BouncingScrollPhysics(),
                        itemCount: controller.dsVbdeTheoDoi.length,
                        itemBuilder: ((context, index) => ItemListVbde(
                              trichYeu: dsldChoDuyet![index].trichYeu ?? "",
                              soKyHieu: dsldChoDuyet![index].soKyHieu ?? "",
                              isXem: dsldChoDuyet![index].ngayXem.toString(),
                              coQuanBanHanh:
                                  dsldChoDuyet![index].tenCoQuanBanHanh ?? "",
                              ngayVanBanDen: dsldChoDuyet![index].ngayDen ?? "",
                              onClickItem: () {
                                controller.onDetailVbde(dsldChoDuyet![index]);
                              },
                            ))),
                    onLoading: SpinKitCircle(
                      color: AppColor.blueAccentColor,
                    ),
                    onEmpty: Container(
                      child: Center(
                          child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Lottie.asset("lottie/emptyBox.json",
                              height: 200, width: 200),
                          const Text("Không tìm thấy dữ liệu!")
                        ],
                      )),
                    ))),
          ),
        ],
      ),
    );
  }
}
