import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/settings/settings_controller.dart';

class SettingsScreen extends GetView<SettingsController> {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Obx(() => Text(
          'Cấu hình',
          style: GoogleFonts.inter(
            fontSize: controller.titleFontSize,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        )),
        backgroundColor: AppColor.blueAccentColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildFontSizeSection(),
            const SizedBox(height: 24),
            _buildPreviewSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSizeSection() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.text_fields,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Obx(() => Text(
                'Cỡ chữ',
                style: GoogleFonts.inter(
                  fontSize: controller.titleFontSize,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              )),
            ],
          ),
          const SizedBox(height: 16),
          
          Obx(() => Text(
            'Chọn cỡ chữ phù hợp với bạn:',
            style: GoogleFonts.inter(
              fontSize: controller.currentFontSize.value,
              color: Colors.grey[600],
            ),
          )),
          const SizedBox(height: 16),

          // Font size options
          Obx(() => Column(
            children: controller.fontSizes.entries.map((entry) {
              String fontName = entry.key;
              double fontSize = entry.value;
              bool isSelected = controller.currentFontSizeName.value == fontName;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: InkWell(
                  onTap: () => controller.changeFontSize(fontName),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isSelected 
                            ? AppColor.blueAccentColor 
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      color: isSelected 
                          ? AppColor.blueAccentColor.withOpacity(0.1)
                          : Colors.white,
                    ),
                    child: Row(
                      children: [
                        Radio<String>(
                          value: fontName,
                          groupValue: controller.currentFontSizeName.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.changeFontSize(value);
                            }
                          },
                          activeColor: AppColor.blueAccentColor,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                fontName,
                                style: GoogleFonts.inter(
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected 
                                      ? AppColor.blueAccentColor 
                                      : Colors.grey[800],
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Văn bản mẫu với cỡ chữ $fontName',
                                style: GoogleFonts.inter(
                                  fontSize: fontSize - 2,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: AppColor.blueAccentColor,
                            size: 20,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          )),
          
          const SizedBox(height: 16),
          
          // Reset button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: Colors.grey[700],
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => controller.resetFontSize(),
              icon: const Icon(Icons.refresh, size: 20),
              label: Obx(() => Text(
                'Đặt lại mặc định',
                style: GoogleFonts.inter(
                  fontSize: controller.currentFontSize.value,
                  fontWeight: FontWeight.w500,
                ),
              )),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewSection() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.preview,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Obx(() => Text(
                'Xem trước',
                style: GoogleFonts.inter(
                  fontSize: controller.titleFontSize,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              )),
            ],
          ),
          const SizedBox(height: 16),
          
          // Preview content
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[200]!),
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[50],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(() => Text(
                  'Tiêu đề văn bản',
                  style: GoogleFonts.inter(
                    fontSize: controller.titleFontSize,
                    fontWeight: FontWeight.w700,
                    color: AppColor.blueAccentColor,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => Text(
                  'Nội dung văn bản sẽ hiển thị với cỡ chữ bạn đã chọn. Đây là đoạn văn mẫu để bạn có thể xem trước cỡ chữ trước khi áp dụng.',
                  style: GoogleFonts.inter(
                    fontSize: controller.currentFontSize.value,
                    color: Colors.grey[800],
                    height: 1.5,
                  ),
                )),
                const SizedBox(height: 8),
                Obx(() => Text(
                  'Thông tin phụ với cỡ chữ nhỏ hơn',
                  style: GoogleFonts.inter(
                    fontSize: controller.subtitleFontSize,
                    color: Colors.grey[600],
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
