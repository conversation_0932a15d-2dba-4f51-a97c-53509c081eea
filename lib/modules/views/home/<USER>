import 'dart:ui';
import 'package:badges/badges.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/utils/font_size_helper.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';
import 'package:badges/badges.dart' as badges;

class Home extends GetView<HomeController> {
  final store = GetStorage();
  final SetupController setup = Get.find();

  Home({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) {
          int crossAxisCount =
              (constraints.maxWidth ~/ 180).clamp(2, 6); // Tự động tính số cột
          return Obx(
            () => GridView.builder(
              padding: const EdgeInsets.all(5),
              itemCount: controller.arrDsNhacViec.length,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                mainAxisSpacing: 10,
                crossAxisSpacing: 10,
                childAspectRatio: 1,
              ),
              itemBuilder: (context, index) {
                final element = controller.arrDsNhacViec[index];
                return Obx(
                  () => ItemMenu(
                    title: element.tenNhacViec,
                    icon: Icons.description,
                    color: Colors.white,
                    numberNV: controller.badgeNhacViec[index],
                    urlSvg: element.iconSvg,
                    clickOn: () => setup.switchScreen(element.key),
                    backgroundItemMenu: AppColor.whiteColor,
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class ItemMenu extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color? color;
  final Color? backgroundItemMenu;
  final String urlSvg;
  final int? numberNV;
  final VoidCallback clickOn;
  const ItemMenu(
      {super.key,
      required this.title,
      required this.icon,
      required this.color,
      required this.numberNV,
      required this.backgroundItemMenu,
      required this.urlSvg,
      required this.clickOn});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        border: Border.all(width: 1, color: AppColor.blueAccentColor),
        color: backgroundItemMenu,
      ),
      child: InkWell(
        onTap: clickOn,
        child: Stack(alignment: AlignmentDirectional.center, children: [
          Positioned(
            right: 20,
            top: 30,
            child: Container(
              child: (numberNV == 0)
                  ? null
                  : badges.Badge(
                      badgeContent: Obx(() => Text(
                            numberNV.toString(),
                            style: FontSizeHelper.getCaptionStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          )),
                      badgeStyle: BadgeStyle(
                          shape: badges.BadgeShape.square,
                          badgeColor: Colors.red,
                          padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                          borderRadius: BorderRadius.circular(8)),
                    ),
            ),
          ),
          SvgPicture.asset(
            urlSvg,
            height: context.isTablet ? 100 : 90,
            width: context.isTablet ? 100 : 90,
          ),
          Positioned(
            bottom: 15,
            child: Obx(() => Text(
                  title,
                  style: FontSizeHelper.getTextStyle(
                    fontSize: context.isTablet
                        ? FontSizeHelper.titleFontSize
                        : FontSizeHelper.currentFontSize,
                    color: AppColor.blackColor,
                    fontWeight: FontWeight.w500,
                  ),
                )),
          )
        ]),
      ),
    );
  }
}
