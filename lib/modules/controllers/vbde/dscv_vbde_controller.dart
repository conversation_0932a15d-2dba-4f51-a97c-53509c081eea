import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xulycv_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DsCvVbdeController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<DetailDsXlCv>> {
  var vbdeProvider = VbdeProvider();
  late TabController tabController;
  var _store = GetStorage();

  int page = 1;
  int size = 20;
  int totalpage = 1;
  String? keySearch = "";

  Rx<int> indexTabCvGobal = 0.obs;
  Rx<bool> istatcads = true.obs;
  Rx<bool> isxlc = false.obs;
  Rx<bool> isph = false.obs;
  Rx<bool> isxdb = false.obs;

  ScrollController scrollerControllerChuaXuLy = ScrollController();
  ScrollController scrollerControllerDangXuLy = ScrollController();
  ScrollController scrollerControllerDaXuLy = ScrollController();
  RxList<DetailDsXlCv> dsChuaXuLy = <DetailDsXlCv>[].obs;
  RxList<DetailDsXlCv> dsDangXuLy = <DetailDsXlCv>[].obs;
  RxList<DetailDsXlCv> dsDaXuly = <DetailDsXlCv>[].obs;

  void isChangeXDB(bool value) {
    isxdb.value = true;
    istatcads.value = !isxdb.value;
    isph.value = !isxdb.value;
    isxlc.value = !isxdb.value;
    page = 1;
    getDsCvByIndexTab(indexTabCvGobal.value, 1);
  }

  void isChangePH(bool value) {
    isph.value = true;
    isxdb.value = !isph.value;
    istatcads.value = !isph.value;
    isxlc.value = !isph.value;
    page = 1;
    getDsCvByIndexTab(indexTabCvGobal.value, 1);
  }

  void isChangeAllDS(bool value) {
    istatcads.value = true;
    isph.value = !istatcads.value;
    isxdb.value = !istatcads.value;
    isxlc.value = !istatcads.value;
    page = 1;
    getDsCvByIndexTab(indexTabCvGobal.value);
  }

  void isChangeXLC(bool value) {
    isxlc.value = true;
    isph.value = !isxlc.value;
    isxdb.value = !isxlc.value;
    istatcads.value = !isxlc.value;
    page = 1;
    getDsCvByIndexTab(indexTabCvGobal.value, 2);
  }

  void setSearchKeyWord(String? keyWord) {
    keySearch = keyWord;
    getDsCvByIndexTab(indexTabCvGobal.value);
  }

  void LoadData() {
    getDsCvByIndexTab(indexTabCvGobal.value);
    patinagetion(
        indexTabCvGobal.value,
        ((indexTabCvGobal.value == 0)
            ? 1
            : (indexTabCvGobal.value == 1)
                ? 2
                : 3));
  }

  // lấy danh sách chưa xử lý
  void getDsCvTheoTrangThai(int trangThaiXuly, [int? maYeuCau]) async {
    page = 1;
    try {
      await vbdeProvider
          .getDsCvTheoTrangThai(page, size, trangThaiXuly, keySearch, maYeuCau)
          .then((element) {
        if (element.message! == "Lấy dữ liệu thành công") {
          if (element.data!.isNotEmpty) {
            switch (indexTabCvGobal.value) {
              case 0:
                dsChuaXuLy.clear();
                dsChuaXuLy.addAll(element.data!);
                totalpage = element.totalPage!;
                change(dsChuaXuLy, status: RxStatus.success());
                break;
              case 1:
                dsDangXuLy.clear();
                dsDangXuLy.addAll(element.data!);
                totalpage = element.totalPage!;
                change(dsDangXuLy, status: RxStatus.success());
                break;
              case 2:
                dsDaXuly.clear();
                dsDaXuly.addAll(element.data!);
                totalpage = element.totalPage!;
                change(dsDaXuly, status: RxStatus.success());
                break;
              default:
            }
          }
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.someThingWentWrong);
    }
  }

  void getDsCvByIndexTab(int indexTab, [int? maYeuCau]) {
    indexTabCvGobal.value = indexTab;
    page = 1;
    change(null, status: RxStatus.loading());
    if (isph.value) {
      maYeuCau = 3;
    } else if (isxlc.value) {
      maYeuCau = 2;
    } else if (isxdb.value) {
      maYeuCau = 1;
    } else {
      maYeuCau = 0;
    }
    switch (indexTab) {
      case 0:
        getDsCvTheoTrangThai(1, maYeuCau);
        patinagetion(indexTab, 1, maYeuCau);
        break;
      case 1:
        getDsCvTheoTrangThai(2, maYeuCau);
        patinagetion(indexTab, 2, maYeuCau);
        break;
      case 2:
        getDsCvTheoTrangThai(3, maYeuCau);
        patinagetion(indexTab, 3, maYeuCau);
        break;
      default:
    }
  }

  void patinagetion(int indexTab, int trangThaiXuLy, [int? maYeuCau]) {
    try {
      switch (indexTab) {
        case 0:
          scrollerControllerChuaXuLy.addListener(() {
            if (scrollerControllerChuaXuLy.position.maxScrollExtent ==
                scrollerControllerChuaXuLy.position.pixels) {
              if (page <= totalpage) {
                page++;
                getMoreDsCvXuLy(indexTab, trangThaiXuLy, maYeuCau);
              }
            }
          });
          break;
        case 1:
          scrollerControllerDangXuLy.addListener(() async {
            if (scrollerControllerDangXuLy.position.maxScrollExtent ==
                scrollerControllerDangXuLy.position.pixels) {
              if (page <= totalpage) {
                page++;
                getMoreDsCvXuLy(indexTab, trangThaiXuLy, maYeuCau);
              }
            }
          });
          break;
        case 2:
          scrollerControllerDaXuLy.addListener(() async {
            if (scrollerControllerDaXuLy.position.maxScrollExtent ==
                scrollerControllerDaXuLy.position.pixels) {
              if (page <= totalpage) {
                page++;
                Get.dialog(Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitThreeBounce(
                      size: 50,
                      color: AppColor.blueAccentColor,
                    )
                  ],
                ));
                await vbdeProvider
                    .getDsCvTheoTrangThai(
                        page, size, trangThaiXuLy, keySearch, maYeuCau)
                    .then((item) {
                  Get.back();
                  if (item.message == 'Lấy dữ liệu thành công') {
                    if (item.data!.isNotEmpty) {
                      switch (indexTab) {
                        case 0:
                          dsChuaXuLy.addAll(item.data!);
                          change(dsChuaXuLy, status: RxStatus.success());
                          break;
                        case 1:
                          dsDangXuLy.addAll(item.data!);
                          change(dsDangXuLy, status: RxStatus.success());
                          break;
                        case 2:
                          dsDaXuly.addAll(item.data!);
                          change(dsDaXuly, status: RxStatus.success());
                          break;
                        default:
                      }
                    }
                  }
                });
              }
            }
          });
          break;
        default:
          break;
      }
    } catch (exception) {}
  }

  void getMoreDsCvXuLy(int indexTab, int trangThaiXuLy, [int? maYeuCau]) async {
    try {
      Get.dialog(Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            size: 50,
            color: AppColor.blueAccentColor,
          )
        ],
      ));
      await vbdeProvider
          .getDsCvTheoTrangThai(page, size, trangThaiXuLy, keySearch, maYeuCau)
          .then((item) {
        Get.back();
        if (item.message == 'Lấy dữ liệu thành công') {
          if (item.data!.isNotEmpty) {
            switch (indexTab) {
              case 0:
                dsChuaXuLy.addAll(item.data!);
                change(dsChuaXuLy, status: RxStatus.success());
                break;
              case 1:
                dsDangXuLy.addAll(item.data!);
                change(dsDangXuLy, status: RxStatus.success());
                break;
              case 2:
                dsDaXuly.addAll(item.data!);
                change(dsDaXuly, status: RxStatus.success());
                break;
              default:
            }
          }
        }
      });
    } catch (exception) {}
  }

  void onDetailVbde(item) {
    var maVanBanDen = item.maVanBanDenKc;
    var maXuLyDen = item.maXuLyDen;
    var maYeuCau = item.maYeuCau;
    _store.write(GetStorageKey.maVanBanDenKc, maVanBanDen);
    _store.write(GetStorageKey.maXulyDenKc, maXuLyDen);
    _store.write(GetStorageKey.maYeuCau, maYeuCau);
    Get.delete<ChiTietVbdeController>();
    Get.toNamed(Routers.DETAILVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'isQuyen': 'isChuyenVien',
      'indexTab': indexTabCvGobal.value,
      'maYeuCau': maYeuCau
    });
  }

  @override
  void onInit() {
    super.onInit();
    var initIndex = Get.arguments != null ? Get.arguments['indexTab'] : 0;
    tabController =
        TabController(length: 3, initialIndex: initIndex, vsync: this);
    indexTabCvGobal.value = initIndex ?? 0;
    LoadData();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    scrollerControllerChuaXuLy.dispose();
    scrollerControllerDangXuLy.dispose();
    scrollerControllerDaXuLy.dispose();
  }
}
