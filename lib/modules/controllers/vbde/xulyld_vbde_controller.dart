import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chuyenldk_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class XuLyLdVbdeController extends GetxController
    with SingleGetTickerProviderMixin {
  final _getStorage = GetStorage();
  XuLyLdVbdeController();
  var vbdeProvider = VbdeProvider();
  HomeController homeController = Get.find();
  //khai báo
  late int maVanBanDen;
  late int maXuLyDen;
  late String trichYeu;
  // input
  late TextEditingController yKienInputController;
  late TextEditingController yKienLdkInputController;
  late TextEditingController yKienLDDInputController;
  Rx<DateTime?> selectDateTime = Rx<DateTime?>(null);
  late TabController tabTreeController;
  var treeNodecb = TreeNodes(title: "", children: []).obs;
  var treeNhomDVN = TreeNodes(title: "", children: []).obs;
  var treeCDTT = TreeNodes(title: "", children: []).obs;
  List<Tab> tabsTree = [
    const Tab(text: "ĐV/CB"),
    const Tab(text: "CĐ trực tiếp"),
    const Tab(text: "Nhóm"),
  ];

  Rx<bool> isSms = false.obs;
  Rx<bool> isCanPhucDap = false.obs;
  Rx<bool> isSmsLdk = false.obs;

  final searchKeyLDK = "".obs;

  var listLanhDaoKhac = <LanhDaoKhac>[].obs;
  var checkBoxLanhDaoKhac = <int, bool>{}.obs;
  var arrayTreeDuyet = <TreeDetail>[];
  var arrayNhomDVN = <TreeDetail>[];
  var arrayCDTT = <TreeDetail>[];

  List<String> selectedPhArrr = <String>[].obs;
  List<String> selectedXDBArr = <String>[].obs;
  List<String> selectedXlc = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    maVanBanDen = Get.arguments["maVanBanDen"].toInt();
    maXuLyDen = Get.arguments["maXuLyDen"].toInt();
    trichYeu = Get.arguments["trichYeu"].toString();
    yKienInputController = TextEditingController();
    yKienLdkInputController = TextEditingController();
    yKienLDDInputController = TextEditingController();
    tabTreeController = TabController(length: tabsTree.length, vsync: this);
  }
  // Method

  void checkChoseXlc(String id, bool value) {
    if (selectedXlc.isNotEmpty) {
      selectedXlc.clear();
      if (value) {
        selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
        selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
        selectedXlc.add(id);
      } else {
        selectedXlc.remove(id);
      }
    } else {
      selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
      selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
      selectedXlc.add(id);
    }
  }

  void checkAllPhxl(String id, bool value) {
    if (selectedPhArrr.contains(id)) {
      selectedPhArrr.remove(id);
    } else {
      if (value) {
        selectedXlc.contains(id) ? selectedXlc.remove(id) : null;
        selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
        selectedPhArrr.add(id);
      } else {
        selectedPhArrr.remove(id);
      }
    }
    var arrayChildren = arrayTreeDuyet.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedPhArrr.isNotEmpty) {
        check = selectedPhArrr.contains(item.id);
      }
      var children = arrayTreeDuyet.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedPhArrr.contains(item.id)) {
          selectedPhArrr.remove(item.id);
        } else {
          if (value) {
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.contains(item.id)
                ? selectedXDBArr.remove(item.id)
                : null;
            selectedPhArrr.add(item.id!);
          } else {
            selectedPhArrr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllPhxl(item2.id!, value);
        }
      } else {
        if (check) {
          selectedPhArrr.remove(item.id);
        } else {
          if (value) {
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.contains(item.id)
                ? selectedXDBArr.remove(item.id)
                : null;
            selectedPhArrr.add(item.id!);
          } else {
            selectedPhArrr.remove(item.id);
          }
        }
      }
    }
  }

  void checkAllxdb(String id, bool value) {
    if (selectedXDBArr.contains(id)) {
      selectedXDBArr.remove(id);
    } else {
      if (value) {
        selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
        selectedXlc.contains(id) ? selectedXlc.remove(id) : null;
        selectedXDBArr.add(id);
      } else {
        selectedXDBArr.remove(id);
      }
    }
    var arrayChildren = arrayTreeDuyet.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedXDBArr.isNotEmpty) {
        check = selectedXDBArr.contains(item.id);
      }
      var children = arrayTreeDuyet.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedXDBArr.contains(item.id)) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedPhArrr.contains(item.id)
                ? selectedPhArrr.remove(item.id)
                : null;
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllxdb(item2.id!, value);
        }
      } else {
        if (check) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedPhArrr.contains(item.id)
                ? selectedPhArrr.remove(item.id)
                : null;
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
      }
    }
  }

  void confirmLDchuyenXuLy(List<DetailCBTree> listCB, int maVanBanDen,
      int maXuLyDen, String trichYeu, int indexTab) async {
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    List<Map<String, dynamic>> chuoiThongTInGuiSms = [];
    listCB.forEach((element) {
      var itemTemp = {
        'ma_ctcb_nhan': element.ma!.replaceAll("CB", ""),
        'ma_yeu_cau': int.parse(element.maYeuCau!),
        'han_xu_ly': hanxuly == 'dd/mm/yyyy' ? "" : hanxuly,
        'ma_don_vi_nhan': 0,
        'sms': isSms.value ? 1 : 0,
        'email': 0,
        'chi_dao_tt': 0,
      };
      chuoiThongTInGuiSms.add(itemTemp);
    });

    int ldChuyenTiep = 0;
    if (indexTab == 0) {
      await vbdeProvider
          .ldDuyetVbde(maXuLyDen, maVanBanDen, ldChuyenTiep, maCtcbKc, maCtcbKc,
              yKienLDDInputController.text, hanxuly, chuoiThongTInGuiSms)
          .then((value) {
        if (value.chuoiMaCtcbNhan!.isNotEmpty) {
          if (isCanPhucDap.value) {
            vbdeProvider.ldDoiPhucDap(maVanBanDen);
          }
          if (isSms.value) {
            List<String> arrSDT = [];
            listCB.forEach((element) {
              element.diDongCanBo!.isNotEmpty
                  ? arrSDT.add(element.diDongCanBo!)
                  : null;
            });
            if (arrSDT.isNotEmpty) {
              var noiDung = trichYeu;
              var chuoiDiDong = arrSDT.join(',');
              vbdeProvider.SendSMS(noiDung, chuoiDiDong,
                  _getStorage.read(GetStorageKey.maDonViQuanTri));
            }
          }
          clearInput();
          clearArraySelected();
          // DuyetVbdeController duyetVbdeController = Get.find();
          // duyetVbdeController.loadData();
          homeController.loadDSnv();
          Get.delete<ChiTietVbdeController>();
          Get.delete<DuyetVbdeController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.VBDEN);
        } else {
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện không thành công!");
        }
      });
    }
  }

  List<DetailCBTree> getCBchoosed() {
    List<DetailCBTree> arrayResult = <DetailCBTree>[];
    List<TreeDetail> arrayDSCB = MethodUntils()
        .removeDuplicates([...arrayTreeDuyet, ...arrayNhomDVN, ...arrayCDTT])
        .where((element) => element.id!.indexOf('CB') != -1)
        .toList();

    selectedXlc.forEach((item) {
      var detalCb = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCb.id != null) {
        arrayResult.add(DetailCBTree(
            detalCb.id,
            detalCb.liAttr!.dataTenCanBo,
            detalCb.liAttr!.dataChucVu,
            detalCb.liAttr!.dataPhone,
            '2',
            "Xử lý chính"));
      }
    });
    selectedPhArrr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => (element.id == item && item!.indexOf('CB') != -1),
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '3',
            "Phối hợp"));
      }
    });
    selectedXDBArr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '1',
            "Xem để biết"));
      }
    });
    return arrayResult;
  }

  void toggleCheckboxValue(int value) {
    checkBoxLanhDaoKhac[value] = !(checkBoxLanhDaoKhac[value] ?? false);
  }

  void onChuyenldK() {
    if (checkBoxLanhDaoKhac[checkBoxLanhDaoKhac.keys.first] == false ||
        checkBoxLanhDaoKhac[checkBoxLanhDaoKhac.keys.first] == null) {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Vui lòng chọn lãnh dạo!");
    } else {
      int maVanBanDen = Get.arguments["maVanBanDen"].toInt();
      int maXuLyDenCha = Get.arguments["maXuLyDen"].toInt();
      String trichYeu = Get.arguments["trichYeu"].toString();
      var maCtcbGui = _getStorage.read(GetStorageKey.maCtcbKc);
      var nguoiNhan = listLanhDaoKhac.firstWhere(
          (element) => element.ma == checkBoxLanhDaoKhac.keys.first);
      int maCtcbNhan = nguoiNhan.ma!.toInt();

      int sms = isSmsLdk.value ? 1 : 0;
      Get.defaultDialog(
          title: "Xác nhận",
          middleText: "Bạn muốn chuyển lãnh đạo ${nguoiNhan.ten} duyệt ?",
          textCancel: "Đóng",
          textConfirm: "Xác nhận",
          confirmTextColor: Colors.white,
          onCancel: () {},
          onConfirm: () {
            ConfirmChuyenLdk(
                maXuLyDenCha,
                maVanBanDen,
                maCtcbGui,
                maCtcbNhan,
                yKienLdkInputController.text,
                sms,
                0,
                trichYeu,
                nguoiNhan.diDongCanBo!);
          });
    }
  }

  void ConfirmChuyenLdk(
      int maXuLyDenCha,
      int maVanBanDen,
      int maCtcbGui,
      int maCtcbNhan,
      String noiDungChuyen,
      int sms,
      int email,
      String noiDungSms,
      String diDongCanBo) {
    try {
      vbdeProvider
          .chuyenLdkVbde(maXuLyDenCha, maVanBanDen, maCtcbGui, maCtcbNhan,
              noiDungChuyen, sms, email)
          .then((value) => {
                if (value.id! > 0)
                  // send SMS
                  {
                    if (sms == 1)
                      {
                        vbdeProvider.SendSMS(noiDungChuyen, diDongCanBo,
                            _getStorage.read(GetStorageKey.maDonViQuanTri))
                      },
                    homeController.loadDSnv(),
                    Get.delete<ChiTietVbdeController>(),
                    Get.delete<DuyetVbdeController>(),
                    CustomSnackBar.showSuccessSnackBar(
                        context: Get.context,
                        title: "Thông báo",
                        message: "Thực hiện thành công!"),
                    Get.offNamed(Routers.VBDEN),
                  }
                else
                  {
                    CustomSnackBar.showWarningSnackBar(
                        context: Get.context,
                        title: "Thông báo",
                        message: "Thực hiện không thành công!")
                  }
              });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void dslanhDaoKhac() {
    int maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    int maDonVi = _getStorage.read(GetStorageKey.maDonVi);
    vbdeProvider.getDanhSachLanhDaoKhac(maDonVi, maCtcbKc).then((item) => {
          if (item.message == 'Lấy dữ liệu thành công')
            {
              if (item.data!.isNotEmpty)
                {
                  listLanhDaoKhac.clear(),
                  listLanhDaoKhac.addAll(
                      item.data!.where((element) => element.ma != maCtcbKc)),
                }
              else
                {
                  CustomSnackBar.showWarningSnackBar(
                      context: Get.context,
                      title: AppString.thongBao,
                      message: AppString.noMoreItem)
                }
            }
          else
            {
              CustomSnackBar.showErrorSnackBar(
                  context: Get.context,
                  title: AppString.thongBao,
                  message: AppString.errloadDataChuyenLdk)
            }
        });
  }

  List<LanhDaoKhac> get fillterDsLanhDaoKhac {
    if (searchKeyLDK.value.isEmpty) {
      return listLanhDaoKhac;
    } else {
      return listLanhDaoKhac
          .where((item) => item.ten!
              .toLowerCase()
              .contains(searchKeyLDK.value.toLowerCase()))
          .toList();
    }
  }

  void setSearchText(String text) {
    searchKeyLDK.value = text;
  }

  void ldHoanTatVbden() {
    int maVanBanDen = Get.arguments["maVanBanDen"].toInt();
    int maXuLyDen = Get.arguments["maXuLyDen"].toInt();
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);

    vbdeProvider
        .ldHoanTatVbde(
            maCtcbKc, maVanBanDen, maXuLyDen, yKienInputController.text)
        .then((value) => {
              if (value.id! == 0)
                {
                  homeController.loadDSnv(),
                  CustomSnackBar.showSuccessSnackBar(
                      context: Get.context,
                      title: "Thông báo",
                      message: "Thực hiện thành công!"),
                  yKienInputController.clear(),
                  Get.delete<ChiTietVbdeController>(),
                  Get.delete<DuyetVbdeController>(),
                  Get.offNamed(Routers.VBDEN),
                }
              else
                {
                  Get.delete<ChiTietVbdeController>(),
                  Get.delete<DuyetVbdeController>(),
                  CustomSnackBar.showWarningSnackBar(
                      context: Get.context,
                      title: 'Thông báo',
                      message: "Thực hiện không thành công!"),
                  Get.offNamed(Routers.VBDEN),
                }
            });
  }

  // load cây đơn vị/ cán bộ
  void loadTreeCbTheoDv() async {
    int maDonVi = _getStorage.read(GetStorageKey.maDonViQuanTri);
    List<Map<String, String>> arrayTree = [];
    arrayTree.clear();
    await vbdeProvider.getTreeCbTheoDv(maDonVi).then((data) => {
          if (data.data!.isNotEmpty)
            {
              data.data!.forEach((element) {
                arrayTree.add({
                  'id': element.id.toString(),
                  'parent': element.parent.toString(),
                  'value': element.text.toString(),
                  'hoVaTen': element.liAttr!.dataTenCanBo.toString(),
                  'diDong': element.liAttr!.dataPhone.toString(),
                  'tenChucVu': element.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayTreeDuyet.addAll(data.data!)
            }
        });

    arrayTree.isNotEmpty
        ? treeNodecb.value = MethodUntils.listToTrees(arrayTree)
        : null;
  }

  void loadTreeCBCDTT() async {
    int maDonViQuanTri = _getStorage.read(GetStorageKey.maDonViQuanTri);
    List<Map<String, String>> arrayDsCDTT = [];
    arrayDsCDTT.clear();
    await vbdeProvider.getDonViCDTT(maDonViQuanTri).then((element) => {
          if (element.data!.isNotEmpty)
            {
              element.data!.forEach((item) {
                arrayDsCDTT.add({
                  'id': item.id.toString(),
                  'parent': item.parent.toString(),
                  'value': item.text.toString(),
                  'hoVaTen': item.liAttr!.dataTenCanBo.toString(),
                  'diDong': item.liAttr!.dataPhone.toString(),
                  'tenChucVu': item.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayCDTT.addAll(element.data!)
            }
        });
    arrayDsCDTT.isNotEmpty
        ? treeCDTT.value = MethodUntils.listToTrees(arrayDsCDTT)
        : null;
  }

  void loadTreeNhomCanBo() async {
    int maCanBo = _getStorage.read(GetStorageKey.maCanBo);
    List<Map<String, String>> arrayNhomCBN = [];
    arrayNhomCBN.clear();
    await vbdeProvider.getNhomCanBoNhan(maCanBo).then((item) => {
          if (item.data!.isNotEmpty)
            {
              item.data!.forEach((element) {
                arrayNhomCBN.add({
                  'id': element.id.toString(),
                  'parent': element.parent.toString(),
                  'value': element.text.toString(),
                  'hoVaTen': element.liAttr!.dataTenCanBo.toString(),
                  'diDong': element.liAttr!.dataPhone.toString(),
                  'tenChucVu': element.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayNhomDVN.addAll(item.data!)
            }
        });
    arrayNhomCBN.isNotEmpty
        ? treeNhomDVN.value = MethodUntils.listToTrees(arrayNhomCBN)
        : null;
  }

  void clearDateTime() {
    if (selectDateTime.value != null) {
      selectDateTime.value = null;
    }
  }

  String get hanxuly => selectDateTime.value == null
      ? "dd/mm/yyyy"
      : DateFormat('dd/MM/yyyy').format(selectDateTime.value!);

  void changeIsSms(bool value) {
    isSms.value = value;
  }

  void changeIsCanPhucDap(bool value) {
    isCanPhucDap.value = value;
  }

  void changeSmsLdk(bool value) {
    isSmsLdk.value = value;
  }

  void clearInput() {
    yKienInputController.clear();
    yKienLdkInputController.clear();
    yKienLDDInputController.clear();
    isSms.value = false;
    isSmsLdk.value = false;
    isCanPhucDap.value = false;
    clearDateTime();
  }

  void clearArraySelected() {
    selectedPhArrr.clear();
    selectedXDBArr.clear();
    selectedXlc.clear();
  }

  // cyclelife

  void loadListTree() {
    loadTreeCbTheoDv();
    loadTreeNhomCanBo();
    loadTreeCBCDTT();
  }

  @override
  void onReady() {
    isSmsLdk.value = false;
    yKienInputController.clear();
    yKienLdkInputController.clear();
    yKienLDDInputController.clear();
    loadListTree();
    clearDateTime();
    dslanhDaoKhac();
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    yKienInputController.dispose();
    yKienLdkInputController.dispose();
    yKienLDDInputController.dispose();
    tabTreeController.dispose();
  }
}
