import 'package:get/get.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dscv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsvt_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/theidoi_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulycv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyvt_vbde_controller.dart';

// danh sach binding
// chuyên viên
class DsCVVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DsCvVbdeController());
  }
}

// lãnh đạo
class DuyetVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DuyetVbdeController());
  }
}

// văn thư
class VanThuVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => VanThuController());
  }
}

// chi tiết

class ChiTietVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ChiTietVbdeController());
  }
}

// xử lý binding
class XulyVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => XuLyLdVbdeController());
  }
}

class CvXuLyVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => XuLyCvVbdeController());
  }
}

class VtXuLyVbdeBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => XuLyVtVbdeController());
  }
}

class VbdeTheoDoiBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => VbdeTheoDoiController());
  }
}
