import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_choduyet_model.dart'
    as _cd;
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xulycv_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DuyetVbdeController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<_cd.ChoDuyet>> {
  var vbdeProvider = VbdeProvider();
  var store = GetStorage();
  late TabController tabController;
  late TextEditingController searchController;
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 3, initialIndex: 0, vsync: this);
    searchController = TextEditingController();
  }

  @override
  void onReady() {
    super.onReady();
    loadData();
  }

  @override
  void onClose() {
    tabController.dispose();
    searchController.dispose();
    super.onClose();
  }

  List<_cd.ChoDuyet> dsChoDuyet = [];
  List<_cd.ChoDuyet> dsDaDuyet = [];
  List<_cd.ChoDuyet> dsUyDuyet = [];
  int page = 1;
  int totalPage = 1;
  int size = 20;

  int totalPageLdDaChuyen = 1;
  ScrollController scrollerControllerChoDuyet = ScrollController();
  ScrollController scrollerControllerDaDuyet = ScrollController();
  ScrollController scrollerControllerUyDuyet = ScrollController();
  _cd.ChoDuyet? choDuyetModel;
  late int maVanBanDenKc;
  late int maXuLyDenKc;
  late String? keyWord = "";
  Rx<int> indexTabGobal = 0.obs;

  void loadData() {
    getDsLdByIndex(indexTabGobal.value);
  }

  void onChangeTab(int indexTab) {
    // set indexTab
    indexTabGobal.value = indexTab;
    getDsLdByIndex(indexTab);
    paginateChoDuyet(indexTab);
  }

  void setSearchKeyWord(String? text) {
    keyWord = text;
    switch (indexTabGobal.value) {
      case 0:
        getDsChoDuyet();
        break;
      case 1:
        getDsLdUyQuyenDuyet();
        break;
      case 2:
        getDsDaChuyen();
        break;
      default:
    }
  }

  Future<void> getDsChoDuyet() async {
    try {
      page = 1;
      await vbdeProvider.getDsChoDuyet(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          if (value.data.isNotEmpty) {
            totalPage = value.totalPage;
            dsChoDuyet.clear();
            dsChoDuyet.addAll(value.data);
            change(dsChoDuyet, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
          }
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      // CustomSnackBar.showErrorSnackBar(
      //     context: Get.context,
      //     title: AppString.error,
      //     message: AppString.noMoreItem);
    }
  }

  Future<void> getDsDaChuyen() async {
    try {
      page = 1;
      await vbdeProvider
          .getDsDaChuyen(page, size, keyWord)
          .then((dsLdDaChuyen) {
        if (dsLdDaChuyen.message == "Lấy dữ liệu thành công") {
          if (dsLdDaChuyen.data!.isNotEmpty) {
            totalPage = dsLdDaChuyen.totalPage!;
            dsDaDuyet.clear();
            dsDaDuyet.addAll(dsLdDaChuyen.data!);
            change(dsDaDuyet, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
            // CustomSnackBar.showErrorSnackBar(
            //     context: Get.context,
            //     title: AppString.error,
            //     message: AppString.noMoreItem);
          }
        } else {
          change(null, status: RxStatus.empty());
          // Fluttertoast.showToast(
          //     msg: dsLdDaChuyen.message,
          //     toastLength: Toast.LENGTH_SHORT,
          //     gravity: ToastGravity.BOTTOM,
          //     backgroundColor: Colors.orange,
          //     textColor: AppColor.whiteColor,
          //     fontSize: 16.0);
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  Future<void> getDsLdUyQuyenDuyet() async {
    try {
      page = 1;
      await vbdeProvider
          .getDsLdUyQuyenDuyet(page, size, keyWord)
          .then((dsLdUyQuyen) {
        if (dsLdUyQuyen.message == "Lấy dữ liệu thành công") {
          if (dsLdUyQuyen.data!.isNotEmpty) {
            totalPage = dsLdUyQuyen.totalPage!;
            dsUyDuyet.clear();
            dsUyDuyet.addAll(dsLdUyQuyen.data);
            change(dsUyDuyet, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
            // Fluttertoast.showToast(
            //     msg: AppString.noMoreItem,
            //     toastLength: Toast.LENGTH_SHORT,
            //     gravity: ToastGravity.BOTTOM,
            //     backgroundColor: Colors.orange,
            //     textColor: AppColor.whiteColor,
            //     fontSize: 16.0);
          }
        } else {
          change(null, status: RxStatus.empty());
          // Fluttertoast.showToast(
          //     msg: dsLdUyQuyen.message,
          //     toastLength: Toast.LENGTH_SHORT,
          //     gravity: ToastGravity.BOTTOM,
          //     backgroundColor: Colors.orange,
          //     textColor: AppColor.whiteColor,
          //     fontSize: 16.0);
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.someThingWentWrong);
    }
  }

  void getDsLdByIndex(int index) async {
    try {
      change(null, status: RxStatus.loading());
      switch (index) {
        case 0:
          getDsChoDuyet();
          break;
        case 1:
          getDsLdUyQuyenDuyet();
          break;
        case 2:
          getDsDaChuyen();
          break;
        default:
          break;
      }
    } catch (exception) {
      change(null, status: RxStatus.error(AppString.someThingWentWrong));
    }
  }

  void paginateChoDuyet(int indexTab) {
    try {
      switch (indexTab) {
        case 0:
          scrollerControllerChoDuyet.addListener(() {
            if (scrollerControllerChoDuyet.position.maxScrollExtent ==
                scrollerControllerChoDuyet.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDsLdDuyet(indexTab);
              }
            }
          });
          break;
        case 1:
          scrollerControllerUyDuyet.addListener(() {
            if (scrollerControllerUyDuyet.position.maxScrollExtent ==
                scrollerControllerUyDuyet.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDsLdDuyet(indexTab);
              }
            }
          });
          break;
        case 2:
          scrollerControllerDaDuyet.addListener(() {
            if (scrollerControllerDaDuyet.position.maxScrollExtent ==
                scrollerControllerDaDuyet.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDsLdDuyet(indexTab);
              }
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void getMoreDsLdDuyet(int indexTab) async {
    try {
      Get.dialog(Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            size: 20,
            color: AppColor.blueAccentColor,
          )
        ],
      ));
      switch (indexTab) {
        case 0:
          await vbdeProvider.getDsChoDuyet(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              if (value.data.isNotEmpty) {
                dsChoDuyet.addAll(value.data);
                change(dsChoDuyet, status: RxStatus.success());
              } else {
                change(null, status: RxStatus.empty());
              }
            } else {
              CustomSnackBar.showErrorSnackBar(
                  context: Get.context,
                  title: AppString.error,
                  message: value.message);
            }
          }, onError: (error) {
            // CustomSnackBar.showErrorSnackBar(
            //     context: Get.context,
            //     title: AppString.error,
            //     message: AppString.someThingWentWrong);
          });
          break;
        case 1:
          await vbdeProvider.getDsLdUyQuyenDuyet(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              if (value.data.isNotEmpty) {
                dsUyDuyet.addAll(value.data);
                change(dsUyDuyet, status: RxStatus.success());
              } else {
                change(null, status: RxStatus.empty());
              }
            } else {
              CustomSnackBar.showErrorSnackBar(
                  context: Get.context,
                  title: AppString.error,
                  message: value.message);
            }
          }, onError: (error) {
            // CustomSnackBar.showErrorSnackBar(
            //     context: Get.context,
            //     title: AppString.error,
            //     message: AppString.someThingWentWrong);
          });
          break;
        case 2:
          await vbdeProvider.getDsDaChuyen(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              if (value.data.isNotEmpty) {
                dsDaDuyet.addAll(value.data);
                change(dsDaDuyet, status: RxStatus.success());
              } else {
                change(null, status: RxStatus.empty());
              }
            }
          }, onError: (error) {
            // CustomSnackBar.showErrorSnackBar(
            //     context: Get.context,
            //     title: AppString.error,
            //     message: AppString.someThingWentWrong);
          });
          break;
        default:
      }
    } catch (exception) {
      Get.back();
      // CustomSnackBar.showErrorSnackBar(
      //     context: Get.context,
      //     title: AppString.error,
      //     message: AppString.someThingWentWrong);
    }
  }

  void onDetailVbde(item) {
    var maVanBanDen = item.maVanBanDenKc;
    var maXuLyDen = item.maXuLyDen;
    store.write(GetStorageKey.maVanBanDenKc, maVanBanDen);
    store.write(GetStorageKey.maXulyDenKc, maXuLyDen);
    Get.delete<ChiTietVbdeController>();
    Get.toNamed(Routers.DETAILVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'isQuyen': 'isLanhDao',
      'indexTab': indexTabGobal.value
    });
  }
}
