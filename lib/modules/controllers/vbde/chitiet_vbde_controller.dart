import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_butpheld_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dscv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/theidoi_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ChiTietVbdeController extends GetxController {
  var args = Get.arguments;
  var vbdeProvider = VbdeProvider();
  // khỏi tạo vbdiProvider để laays thông tin danh sách liên quan
  var vbdiProvider = VbdiProvider();
  final _getStorage = GetStorage();
  Rx<VbdeDetail> vbdeDetail = VbdeDetail().obs;
  var dsButphe = <DsButPheLanhDao>[].obs;
  HomeController homeController = Get.find();
  late String isQuyen;
  late int maYeuCau;
  late int indexTab;

  Rx<String?> dsVanBanLienQuan = "".obs;
  Rx<String?> chuoiMaVanBanLienQuan = "".obs;

  @override
  void onInit() {
    super.onInit();
    isQuyen = Get.parameters["isQuyen"].toString();
    maYeuCau = int.parse(Get.parameters["maYeuCau"] ?? '2');
    indexTab = int.parse(Get.parameters["indexTab"] ?? '0'); // 4: theo dỗi
    getDetailVbde();
    getButPheLanhDao();
    UpdateViewDaXem();
    update();
  }

  // Chuyển chức nằng của lãnh đạo
  void onSwitchPageDuyet() {
    var maVanBanDen = vbdeDetail.value.maVanBanDenKc!.toInt();
    var maXuLyDen = vbdeDetail.value.maXuLyDen!.toInt();
    Get.delete<XuLyLdVbdeController>();
    Get.toNamed(Routers.DUYETXULYVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'trichYeu': vbdeDetail.value.trichYeu.toString()
    });
  }

  void onSwitchPageXuLy() {
    var maVanBanDen = vbdeDetail.value.maVanBanDenKc!.toInt();
    var maXuLyDen = vbdeDetail.value.maXuLyDen!.toInt();
    Get.delete<XuLyLdVbdeController>();
    Get.toNamed(Routers.HOATTATVBDE,
        arguments: {'maVanBanDen': maVanBanDen, 'maXuLyDen': maXuLyDen});
  }

  void onSwitchPageChuyenLDKhac() {
    var maVanBanDen = vbdeDetail.value.maVanBanDenKc!.toInt();
    var maXuLyDen = vbdeDetail.value.maXuLyDen!.toInt();
    Get.delete<XuLyLdVbdeController>();
    Get.toNamed(Routers.CHUYENLDKHAC, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'trichYeu': vbdeDetail.value.trichYeu.toString()
    });
  }

  // Chuyển chức năng của chuyên viên

  void onSwitchPageChuyenXLCV() {
    var maVanBanDen = vbdeDetail.value.maVanBanDenKc!.toInt();
    var maXuLyDen = vbdeDetail.value.maXuLyDen!.toInt();
    var maYeuCau = vbdeDetail.value.maYeuCau.toInt();
    Get.delete<XuLyLdVbdeController>();
    Get.toNamed(Routers.CVCHUYENXL, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'trichYeu': vbdeDetail.value.trichYeu.toString(),
      'maYeuCau': maYeuCau ?? 2
    });
  }

  void onSwitchPageCvNhapYkien() {
    var maVanBanDen = vbdeDetail.value.maVanBanDenKc!.toInt();
    var maXuLyDen = vbdeDetail.value.maXuLyDen!.toInt();
    var maYeuCau = vbdeDetail.value.maYeuCau.toInt();
    Get.delete<XuLyLdVbdeController>();
    Get.toNamed(Routers.CVNHAPYKIEN, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'maYeuCau': maYeuCau ?? 1
    });
  }

  void getDetailVbde() {
    var maVanBanDen = _getStorage.read(GetStorageKey.maVanBanDenKc).toInt();

    // args["maVanBanDen"].toInt();
    var maXuLyDen = _getStorage.read(GetStorageKey.maXulyDenKc).toInt();
    //args["maXuLyDen"].toInt();

    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc).toInt();
    vbdeProvider
        .getChiTietVanBanDen(maXuLyDen, maVanBanDen, maCtcbKc)
        .then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        vbdeDetail.value = value.data!;
        _getStorage.write(
            GetStorageKey.maVanBanDenKc, value.data!.maVanBanDenKc);
        _getStorage.write(GetStorageKey.maXulyDenKc, value.data!.maXuLyDen);
        _getStorage.write(GetStorageKey.trichYeu, value.data!.trichYeu);
        try {
          loadDanhSachVbLienQuan(vbdeDetail.value.maVanBanKc!.toInt());
        } catch (exception) {}
      }
    });
  }

  void getButPheLanhDao() {
    var maVanBanDen = _getStorage.read(GetStorageKey.maVanBanDenKc).toInt();
    var maXuLyDen = _getStorage.read(GetStorageKey.maXulyDenKc).toInt();
    vbdeProvider.getButPheLanhDao(maVanBanDen, maXuLyDen).then((dsButPhe) {
      dsButphe.clear();
      dsButphe.addAll(dsButPhe);
    });
  }

  // Hoàn thành văn bản quyền chuyên viên

  void loadDanhSachVbLienQuan(int maVanBanKc) async {
    try {
      await vbdiProvider.auVbdiVbLienQuan(maVanBanKc).then((value) {
        for (var element in value.data!) {
          if (dsVanBanLienQuan.value!.isNotEmpty) {
            dsVanBanLienQuan.value =
                dsVanBanLienQuan.value! + ':' + element.fileVanBan!;

            chuoiMaVanBanLienQuan.value = chuoiMaVanBanLienQuan.value! +
                ';' +
                element.maVanBanKc!.toInt().toString();
          } else {
            dsVanBanLienQuan.value = element.fileVanBan!;
            chuoiMaVanBanLienQuan.value =
                element.maVanBanKc!.toInt().toString();
          }
        }
      });
    } catch (exception) {}
  }

  // hoàn thành văn bản

  void cvHoanThanhVbd() {
    String maXuLyDen =
        _getStorage.read(GetStorageKey.maXulyDenKc).toInt().toString();
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Hoàn thành văn bản này ",
        textCancel: "Đóng",
        textConfirm: "Đồng ý",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () {
          vbdeProvider.cvDaXemVBD(maXuLyDen).then((value) {
            if (value.message == "Thực thi thành công") {
              vbdeProvider.cvHoanThanhVanBan(maXuLyDen).then((element) {
                if (element.message == "Thực thi thành công") {
                  Get.lazyPut(() => DsCvVbdeController());
                  DsCvVbdeController xuLyCvVbdeController = Get.find();
                  xuLyCvVbdeController.LoadData();
                  homeController.loadDSnv();
                  CustomSnackBar.showSuccessSnackBar(
                      context: Get.context,
                      title: "Thông báo",
                      message: "Thực hiện thành công!");
                  Get.offNamed(Routers.XULYCVVBDE);
                }
              });
            }
          });
        });
  }

  void cvDangXulyVbd() {
    int maXuLyDen = _getStorage.read(GetStorageKey.maXulyDenKc).toInt();
    int trangThaiXuLy = 2;
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Chuyển đang xử lý?",
        textCancel: "Đóng",
        textConfirm: "Đồng ý",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () {
          vbdeProvider
              .cvDangXuLyVbd(maXuLyDen, "", trangThaiXuLy)
              .then((value) {
            if (value.message == "Thực thi thành công") {
              Get.lazyPut(() => DsCvVbdeController());
              DsCvVbdeController xuLyCvVbdeController = Get.find();
              xuLyCvVbdeController.LoadData();
              homeController.loadDSnv();
              CustomSnackBar.showSuccessSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Thực hiện thành công!");
              Get.offNamed(Routers.XULYCVVBDE);
            } else {
              CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: AppString.error);
            }
          });
        });
  }

  void UpdateViewDaXem() {
    int maXuLyDen = _getStorage.read(GetStorageKey.maXulyDenKc).toInt();
    vbdeProvider.cvDaXemVBD(maXuLyDen.toString());
  }

  // huỷ theoi dõi văn bản đến

  void huyTheoDoiVbde() {
    int maXuLyDen = args["maXuLyDen"].toInt();
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Huỷ theo dõi văn bản?",
        textCancel: "Đóng",
        textConfirm: "Đồng ý",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () {
          vbdeProvider.auVbdeHuyTheoDoi(maXuLyDen).then((value) {
            Map<String, dynamic> result = value;
            if (result['message'] == "Thực thi thành công") {
              Get.lazyPut(() => VbdeTheoDoiController());
              VbdeTheoDoiController vbdeTheoDoiController = Get.find();
              vbdeTheoDoiController.getDsChoDuyet();
              homeController.loadDSnv();
              CustomSnackBar.showSuccessSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Thực hiện thành công!");
              Get.offNamed(Routers.THEODOIVBDE);
            } else {
              CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: AppString.error);
            }
          });
        });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
