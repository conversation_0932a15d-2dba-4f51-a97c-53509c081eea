import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dscv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class XuLyCvVbdeController extends GetxController
    with SingleGetTickerProviderMixin {
  final _getStorage = GetStorage();
  XuLyCvVbdeController();
  var vbdeProvider = VbdeProvider();
  HomeController homeController = Get.find();
  Rx<DateTime?> selectDateTime = Rx<DateTime?>(null);
  //khai báo
  late int maVanBanDen;
  late int maXuLyDen;
  late String trichYeu;
  late int maYeuCau;

  late String vbdeUserHienThiSoNgayHXL;
  late String vbdeXulySoNgayHXL;
  var treeDsCbChuyenVbde = TreeNodes(title: "", children: []).obs;

  List<String> selectedPhArrr = <String>[].obs;
  List<String> selectedXDBArr = <String>[].obs;
  List<String> selectedXlc = <String>[].obs;

  var arrayTreecvChuyen = <TreeDetail>[];
  var arrayNhomDVN = <TreeDetail>[];
  var treeNhomDVN = TreeNodes(title: "", children: []).obs;

  late TextEditingController yKienInputController;
  late TextEditingController yKienXDBinputController;
  RxBool isSms = false.obs;
  RxBool isHoanTatXDB = true.obs;
  late TabController tabTreeController;
  List<Tab> tabsTree = [
    const Tab(text: "Cán bộ"),
    const Tab(text: "Nhóm cán bộ"),
  ];

  var trangThaiXuLy = "Đã hoàn tất".obs;

  @override
  void onInit() {
    maVanBanDen = Get.arguments["maVanBanDen"].toInt();
    maXuLyDen = Get.arguments["maXuLyDen"].toInt();
    trichYeu = Get.arguments["trichYeu"].toString();
    maYeuCau = Get.arguments["maYeuCau"].toInt();
    yKienInputController = TextEditingController();
    yKienXDBinputController = TextEditingController();

    tabTreeController = TabController(length: tabsTree.length, vsync: this);
    macDinhNgayXuLyTheoUser();
    super.onInit();
  }

  void macDinhNgayXuLyTheoUser() {
    vbdeUserHienThiSoNgayHXL =
        _getStorage.read(GetStorageKey.vbdeUserHienThiSoNgayHxl);
    vbdeXulySoNgayHXL = _getStorage.read(GetStorageKey.vbdeXuLySoNgayHxl);
    if (vbdeUserHienThiSoNgayHXL == 'all') {
      selectDateTime.value =
          DateTime.now().add(Duration(days: int.parse(vbdeXulySoNgayHXL)));
    } else if (vbdeUserHienThiSoNgayHXL != 'null') {
      var arrayUser = vbdeUserHienThiSoNgayHXL.split('|');
      var useNamer = _getStorage.read(GetStorageKey.userName);
      var result = arrayUser.firstWhere(
          (element) =>
              element.toLowerCase() ==
              _getStorage.read(GetStorageKey.userName).toLowerCase(),
          orElse: () => '');
      if (result.isNotEmpty) {
        selectDateTime.value =
            DateTime.now().add(Duration(days: int.parse(vbdeXulySoNgayHXL)));
      }
    }
  }

  void clearDateTime() {
    if (selectDateTime.value != null) {
      selectDateTime.value = null;
    }
  }

  void changeIsSms(bool value) {
    isSms.value = value;
  }

  void isChangeHoanTatXDB(bool value) {
    isHoanTatXDB.value = value;
  }

  String get hanxuly => selectDateTime.value == null
      ? "dd/mm/yyyy"
      : DateFormat('dd/MM/yyyy').format(selectDateTime.value!);

  void onChangeTrangThai(int index) {
    if (index == 0) {
      trangThaiXuLy.value = "Đang xử lý";
    } else {
      trangThaiXuLy.value = "Đã hoàn tất";
    }
    Get.back();
  }
  // method chẹck chọn cây
  // Method

  void checkChoseXlc(String id, bool value) {
    if (selectedXlc.isNotEmpty) {
      selectedXlc.clear();
      if (value) {
        selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
        selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
        selectedXlc.add(id);
      } else {
        selectedXlc.remove(id);
      }
    } else {
      selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
      selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
      selectedXlc.add(id);
    }
  }

  void checkAllPhxl(String id, bool value) {
    if (selectedPhArrr.contains(id)) {
      selectedPhArrr.remove(id);
    } else {
      if (value) {
        selectedXlc.contains(id) ? selectedXlc.remove(id) : null;
        selectedXDBArr.contains(id) ? selectedXDBArr.remove(id) : null;
        selectedPhArrr.add(id);
      } else {
        selectedPhArrr.remove(id);
      }
    }
    var arrayChildren =
        arrayTreecvChuyen.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedPhArrr.isNotEmpty) {
        check = selectedPhArrr.contains(item.id);
      }
      var children = arrayTreecvChuyen.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedPhArrr.contains(item.id)) {
          selectedPhArrr.remove(item.id);
        } else {
          if (value) {
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.contains(item.id)
                ? selectedXDBArr.remove(item.id)
                : null;
            selectedPhArrr.add(item.id!);
          } else {
            selectedPhArrr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllPhxl(item2.id!, value);
        }
      } else {
        if (check) {
          selectedPhArrr.remove(item.id);
        } else {
          if (value) {
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.contains(item.id)
                ? selectedXDBArr.remove(item.id)
                : null;
            selectedPhArrr.add(item.id!);
          } else {
            selectedPhArrr.remove(item.id);
          }
        }
      }
    }
  }

  void checkAllxdb(String id, bool value) {
    if (selectedXDBArr.contains(id)) {
      selectedXDBArr.remove(id);
    } else {
      if (value) {
        selectedPhArrr.contains(id) ? selectedPhArrr.remove(id) : null;
        selectedXlc.contains(id) ? selectedXlc.remove(id) : null;
        selectedXDBArr.add(id);
      } else {
        selectedXDBArr.remove(id);
      }
    }
    var arrayChildren =
        arrayTreecvChuyen.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedXDBArr.isNotEmpty) {
        check = selectedXDBArr.contains(item.id);
      }
      var children = arrayTreecvChuyen.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedXDBArr.contains(item.id)) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedPhArrr.contains(item.id)
                ? selectedPhArrr.remove(item.id)
                : null;
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllxdb(item2.id!, value);
        }
      } else {
        if (check) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedPhArrr.contains(item.id)
                ? selectedPhArrr.remove(item.id)
                : null;
            selectedXlc.contains(item.id) ? selectedXlc.remove(item.id) : null;
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
      }
    }
  }

  void loadTreeDsCbChuyenVbde() async {
    List<Map<String, String>> arrayDsCbChuyenVbde = [];
    arrayDsCbChuyenVbde.clear();
    await vbdeProvider.getDsCbChuyenVbde().then((element) => {
          if (element.data!.isNotEmpty)
            {
              element.data!.forEach((item) {
                arrayDsCbChuyenVbde.add({
                  'id': item.id.toString(),
                  'parent': item.parent.toString(),
                  'value': item.text.toString(),
                  'hoVaTen': item.liAttr!.dataTenCanBo.toString(),
                  'diDong': item.liAttr!.dataPhone.toString(),
                  'tenChucVu': item.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayTreecvChuyen.addAll(element.data!)
            }
        });
    arrayDsCbChuyenVbde.isNotEmpty
        ? treeDsCbChuyenVbde.value =
            MethodUntils.listToTrees(arrayDsCbChuyenVbde)
        : null;
  }

  void loadTreeNhomCanBo() async {
    int maCanBo = _getStorage.read(GetStorageKey.maCanBo);
    List<Map<String, String>> arrayNhomCBN = [];
    arrayNhomCBN.clear();
    await vbdeProvider.getNhomCanBoNhan(maCanBo).then((item) => {
          if (item.data!.isNotEmpty)
            {
              item.data!.forEach((element) {
                arrayNhomCBN.add({
                  'id': element.id.toString(),
                  'parent': element.parent.toString(),
                  'value': element.text.toString(),
                  'hoVaTen': element.liAttr!.dataTenCanBo.toString(),
                  'diDong': element.liAttr!.dataPhone.toString(),
                  'tenChucVu': element.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayNhomDVN.addAll(item.data!)
            }
        });
    arrayNhomCBN.isNotEmpty
        ? treeNhomDVN.value = MethodUntils.listToTrees(arrayNhomCBN)
        : null;
  }

// danh sachs can bộ đã chọn
  List<DetailCBTree> getCBchoosedCv() {
    List<DetailCBTree> arrayResult = <DetailCBTree>[];
    List<TreeDetail> arrayDSCB = MethodUntils()
        .removeDuplicates([...arrayTreecvChuyen, ...arrayNhomDVN])
        .where((element) => element.id!.indexOf('CB') != -1)
        .toList();

    selectedXlc.forEach((item) {
      var detalCb = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCb.id != null) {
        arrayResult.add(DetailCBTree(
            detalCb.id,
            detalCb.liAttr!.dataTenCanBo,
            detalCb.liAttr!.dataChucVu,
            detalCb.liAttr!.dataPhone,
            '2',
            "Xử lý chính"));
      }
    });
    selectedPhArrr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => (element.id == item && item!.indexOf('CB') != -1),
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '3',
            "Phối hợp"));
      }
    });
    selectedXDBArr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '1',
            "Xem để biết"));
      }
    });
    return arrayResult;
  }

// comfirm cv chuyen xl
  void confirmCvchuyenXuLy(List<DetailCBTree> listCB, int maVanBanDen,
      int maXuLyDen, String trichYeu, int indexTab) async {
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    int isTrangThai = 3;
    List<Map<String, dynamic>> chuoiMaCanBoNhan = [];
    listCB.forEach((element) {
      var itemTemp = {
        'ma_ctcb_nhan': element.ma!.replaceAll("CB", ""),
        'ma_yeu_cau': int.parse(element.maYeuCau!),
        'han_xu_ly': hanxuly == 'dd/mm/yyyy' ? "" : hanxuly,
        'ma_don_vi_nhan': 0,
        'sms': isSms.value ? 1 : 0,
        'email': 0,
        'chi_dao_tt': 0,
      };
      chuoiMaCanBoNhan.add(itemTemp);
    });
    if (trangThaiXuLy == 'Đang xử lý') {
      isTrangThai = 2;
    }
    await vbdeProvider
        .cvDangXuLyVbd(maXuLyDen, yKienInputController.text, isTrangThai)
        .then((value) {
      if (value.message == "Thực thi thành công") {
        vbdeProvider
            .auVbdeCvCTH(
                maVanBanDen,
                maXuLyDen,
                maCtcbKc,
                chuoiMaCanBoNhan,
                hanxuly,
                maYeuCau,
                (isSms.value ? 1 : 0),
                yKienInputController.text)
            .then((result) {
          if (isSms.value) {
            List<String> arrSDT = [];
            listCB.forEach((element) {
              element.diDongCanBo!.isNotEmpty
                  ? arrSDT.add(element.diDongCanBo!)
                  : null;
            });
            if (arrSDT.isNotEmpty) {
              var noiDung = trichYeu;
              var chuoiDiDong = arrSDT.join(',');
              vbdeProvider.SendSMS(noiDung, chuoiDiDong,
                  _getStorage.read(GetStorageKey.maDonViQuanTri));
            }
          }
          yKienInputController.clear();
          HomeController homeController = Get.find();
          homeController.loadDSnv();

          clearArraySelected();
          Get.delete<DsCvVbdeController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.XULYCVVBDE);
        });
      }
    });
  }

  void cvNhapYKien() {
    int maXuLyDen = Get.arguments["maXuLyDen"].toInt();
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Hoàn tất thêm ý kiến xử lý? ",
        textCancel: "Đóng",
        textConfirm: "Đồng ý",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () {
          vbdeProvider.cvDaXemVBD(maXuLyDen.toString()).then((value) {
            if (value.message == "Thực thi thành công") {
              vbdeProvider
                  .auVbdeCvCnYKienXL(maXuLyDen, yKienXDBinputController.text)
                  .then((result) {
                if (result.message == 'Thực thi thành công') {
                  if (isHoanTatXDB.value) {
                    vbdeProvider
                        .cvHoanThanhVanBan(maXuLyDen.toString())
                        .then((result) {
                      if (result.id == 0) {
                        yKienXDBinputController.clear();
                        HomeController homeController = Get.find();
                        homeController.loadDSnv();
                        Get.lazyPut(() => DsCvVbdeController());
                        DsCvVbdeController dscvController = Get.find();
                        dscvController.LoadData();
                        CustomSnackBar.showSuccessSnackBar(
                            context: Get.context,
                            title: "Thông báo",
                            message: "Thực thi thành công!");
                        Get.offNamed(Routers.XULYCVVBDE);
                      }
                    });
                  }
                }
              });
            }
          });
        });
  }

  void clearArraySelected() {
    selectedPhArrr.clear();
    selectedXDBArr.clear();
    selectedXlc.clear();
  }

  @override
  void onReady() {
    yKienInputController.clear();
    loadTreeDsCbChuyenVbde();
    loadTreeNhomCanBo();
    super.onReady();
  }

  @override
  void onClose() {
    yKienInputController.dispose();
    tabTreeController.dispose();
    super.onClose();
  }
}
