import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_choduyet_model.dart'
    as _cd;
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class VbdeTheoDoiController extends GetxController
    with StateMixin<List<_cd.ChoDuyet>> {
  var vbdeProvider = VbdeProvider();
  var store = GetStorage();

  late TextEditingController searchController;
  ScrollController scrollerControllerTheoDoiVbde = ScrollController();
  _cd.ChoDuyet? choDuyetModel;
  late int maVanBanDenKc;
  late int maXuLyDenKc;
  late String? keyWord = "";
  List<_cd.ChoDuyet> dsVbdeTheoDoi = [];

  int page = 1;
  int totalPage = 1;
  int size = 20;
  void setSearchKeyWord(String? text) {
    keyWord = text;
    getDsChoDuyet();
  }

  void onDetailVbde(item) {
    var maVanBanDen = item.maVanBanDenKc;
    var maXuLyDen = item.maXuLyDen;
    store.write(GetStorageKey.maVanBanDenKc, maVanBanDen);
    store.write(GetStorageKey.maXulyDenKc, maXuLyDen);
    Get.delete<ChiTietVbdeController>();
    Get.toNamed(Routers.DETAILVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'isQuyen': 'isLanhDao',
      'indexTab': 4
    });
  }

  void getDsChoDuyet() async {
    try {
      change(null, status: RxStatus.empty());
      page = 1;
      await vbdeProvider.getDsVbdeTheoDoi(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          if (value.data.isNotEmpty) {
            totalPage = value.totalPage;
            dsVbdeTheoDoi.clear();
            dsVbdeTheoDoi.addAll(value.data);
            change(dsVbdeTheoDoi, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
          }
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.error(exception.toString()));
      // CustomSnackBar.showErrorSnackBar(
      //     context: Get.context,
      //     title: AppString.error,
      //     message: AppString.noMoreItem);
    }
  }

  @override
  void onInit() {
    super.onInit();
    getDsChoDuyet();
    searchController = TextEditingController();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
