import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_dschualuuvt_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_themvbde_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsvt_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class XuLyVtVbdeController extends GetxController {
  final _getStorage = GetStorage();
  XuLyVtVbdeController();
  var vbdeProvider = VbdeProvider();
  // khai báo vbdiProvider để lấy thông tin văn bản liên quan
  var vbdiProvider = VbdiProvider();
  HomeController homeController = Get.find();
  VanThuController vanThuController = Get.find();
  //khai báo
  late int maVanBanDen;
  late DsVanDenDienTu item;
  late int indexTab;
  late TextEditingController inputSoKyHieu;
  late TextEditingController inputTrichYeu;
  late TextEditingController inputSoPhatHanh;
  late TextEditingController inputLoaiVanBan;
  late TextEditingController inputSoVanBan;
  late TextEditingController inputNam;
  late TextEditingController inputSoDen;
  late TextEditingController inputCQBH;
  late TextEditingController inputNguoiKy;
  late TextEditingController inputCapDoKhan;
  late TextEditingController inputCapDoMat;
  late TextEditingController inputLanhDaoDuyet;
  late TextEditingController inputNoiLuuBanChinh;
  late TextEditingController inputGhiChu;
  late TextEditingController inputLinhVuc;
  RxBool isSms = false.obs;
  RxBool isKemVanBanGiay = false.obs;

  List<DMSoVbde> DmSoVbde = [];
  List<LoaiVanBan> DmLoaiVanBan = [];
  List<dsCapDoKhan> DmCapDoKhan = [];
  List<DmCapDoMat> dmCapDoMat = [];
  List<DsLinhVuc> dmLinhVuc = [];
  List<LanhDaoDuyetVbde> dsLanhDaoDuyetVbde = [];
  List<int> dsNam = MethodUntils().generateYearList(10);
  Rx<bool> isLoadData = false.obs;
  // văn bản liên quan
  Rx<String?> dsVanBanLienQuan = "".obs;
  Rx<String?> chuoiMaVanBanLienQuan = "".obs;

  var detailsItem = Rx<DsVanDenDienTu>(DsVanDenDienTu());

  @override
  void onInit() {
    super.onInit();
    maVanBanDen = Get.arguments["maVanBanDen"].toInt();
    item = Get.arguments["item"];
    indexTab = Get.arguments["indexTab"];
    inputSoKyHieu = TextEditingController();
    inputTrichYeu = TextEditingController();
    inputLoaiVanBan = TextEditingController();
    inputSoPhatHanh = TextEditingController();
    inputSoVanBan = TextEditingController();
    inputNam = TextEditingController();
    inputSoDen = TextEditingController();
    inputNguoiKy = TextEditingController();
    inputCapDoKhan = TextEditingController();
    inputCapDoMat = TextEditingController();
    inputLanhDaoDuyet = TextEditingController();
    inputNoiLuuBanChinh = TextEditingController();
    inputGhiChu = TextEditingController();
    inputCQBH = TextEditingController();
    inputLinhVuc = TextEditingController();
    loadDsDmSoVanBan();
    loadLoaiVanBan();
    loadDsDmCapDoKhan();
    loadDsDmCapDoMat();
    loadDsLanhDaoDuyetVbde();
    loadDsLinhVucVb();
  }

  @override
  void onReady() {
    super.onReady();
    inputSoKyHieu.clear();
    inputTrichYeu.clear();
    inputSoPhatHanh.clear();
    inputLoaiVanBan.clear();
    inputSoVanBan.clear();
    inputNam.clear();
    inputSoDen.clear();
    inputNguoiKy.clear();
    inputCapDoKhan.clear();
    inputCapDoMat.clear();
    inputLanhDaoDuyet.clear();
    inputNoiLuuBanChinh.clear();
    inputGhiChu.clear();
    inputCQBH.clear();
    inputLinhVuc.clear();
    loadData();
  }

  @override
  void dispose() {
    super.dispose();
    inputSoKyHieu.dispose();
    inputTrichYeu.dispose();
    inputSoPhatHanh.dispose();
    inputLoaiVanBan.dispose();
    inputSoVanBan.dispose();
    inputNam.dispose();
    inputSoDen.dispose();
    inputNguoiKy.dispose();
    inputCapDoKhan.dispose();
    inputCapDoMat.dispose();
    inputLanhDaoDuyet.dispose();
    inputNoiLuuBanChinh.dispose();
    inputGhiChu.dispose();
    inputCQBH.dispose();
    inputLinhVuc.dispose();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }

  void loadData() {
    if (Get.arguments["indexTab"] == 1) {
      getDetailsItem();
      loadDanhSachVbLienQuan();
    } else {
      loadDetailItem();
      loadDanhSachVbLienQuan();
    }
  }

  void loadDanhSachVbLienQuan() async {
    try {
      await vbdiProvider
          .auVbdiVbLienQuan(item.maVanBanKc!.toInt())
          .then((value) {
        for (var element in value.data!) {
          if (dsVanBanLienQuan.value!.isNotEmpty) {
            dsVanBanLienQuan.value =
                dsVanBanLienQuan.value! + ':' + element.fileVanBan!;

            chuoiMaVanBanLienQuan.value = chuoiMaVanBanLienQuan.value! +
                ';' +
                element.maVanBanKc!.toInt().toString();
          } else {
            dsVanBanLienQuan.value = element.fileVanBan!;
            chuoiMaVanBanLienQuan.value =
                element.maVanBanKc!.toInt().toString();
          }
        }
      });
    } catch (exception) {}
  }

  void loadDetailItem() {
    DateTime now = DateTime.now();
    inputSoKyHieu.text = item.soKyHieu.toString();
    detailsItem.value.soKyHieu = item.soKyHieu;
    inputTrichYeu.text = item.trichYeu.toString();
    detailsItem.value.trichYeu = item.trichYeu.toString();
    inputSoPhatHanh.text = detailsItem.value.soBanPhatHanh == null
        ? "1"
        : item.soBanPhatHanh!.toString();
    detailsItem.value.soBanPhatHanh = item.soBanPhatHanh ?? "1";
    inputNam.text = now.year.toString();
    inputSoDen.text = item.soDen == null ? "" : item.soDen!.toInt().toString();
    detailsItem.value.soDen = item.soDen == null ? 0 : item.soDen!.toInt();
    inputNguoiKy.text = item.nguoiKy == null ? "" : item.nguoiKy.toString();
    detailsItem.value.nguoiKy = item.nguoiKy;
    detailsItem.value.fileVanBan = item.fileVanBan ?? "";
    detailsItem.value.fileVanBanBs = item.fileVanBanBs ?? "";
    inputNoiLuuBanChinh.text = item.noiLuuBanChinh ?? "";
    detailsItem.value.noiLuuBanChinh = item.noiLuuBanChinh ?? "";
    inputCQBH.text = item.tenCoQuanBanHanh ?? "";
    detailsItem.value.tenCoQuanBanHanh = item.tenCoQuanBanHanh ?? "";
    inputGhiChu.text = item.ghiChu == null ? "" : item.ghiChu!;
    detailsItem.value.ghiChu = item.ghiChu ?? "";
    detailsItem.value.daLuuFile = item.daLuuFile ?? 0;
    detailsItem.value.chuoiVbDienTu = item.chuoiVbDienTu ?? "";
    detailsItem.value.soTrangVb = item.soTrangVb ?? 0;
    detailsItem.value.maVanBanDenKc = item.maVanBanKc ?? 0;
    detailsItem.value.maVanBanDenKc = item.maVanBanDenKc;
    detailsItem.value.maXuLyDen = item.maXuLyDen ?? 0;
    detailsItem.value.ngayXem = item.ngayXem ?? "";
    detailsItem.value.hienThiVblq = item.hienThiVblq ?? 0;
    final DateFormat dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    if (item.ngayDen == null || item.ngayDen == "") {
      item.ngayDen = "";
    } else {
      if (indexTab == 1) {
        detailsItem.value.ngayDen = item.ngayDen;
      } else {
        final DateTime dateTime = dateFormat.parse(item.ngayDen!);
        final DateFormat newDateFormat = DateFormat('dd/MM/yyyy');
        final String newNgayDen = newDateFormat.format(dateTime);
        detailsItem.value.ngayDen = newNgayDen;
      }
    }
    if (item.ngayBanHanh == null || item.ngayBanHanh == "") {
      item.ngayBanHanh = "";
    } else {
      if (indexTab == 1) {
        detailsItem.value.ngayBanHanh = item.ngayDen;
      } else {
        final DateFormat newDateFormat = DateFormat('dd/MM/yyyy');
        final DateTime ngayBanHanh = dateFormat.parse(item.ngayBanHanh!);
        final String newNgayBanHanh = newDateFormat.format(ngayBanHanh);

        detailsItem.value.ngayBanHanh = newNgayBanHanh;
      }
    }

    if (item.loaiVbDienTu != null &&
        item.loaiVbDienTu!.toInt() == 1 &&
        int.parse(_getStorage.read(GetStorageKey.vbdeGopChungVbDienTu)) == 1) {
      getInfoEdxml(item.tenGoiTinXml!);
    } else {
      isLoadData.value = true;
    }
  }

  void getDetailsItem() async {
    await vbdeProvider
        .auVbdeVBDChiTiet(item.maXuLyDen!.toInt(), item.maVanBanDenKc!.toInt())
        .then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        item.bussinessDoctype = value.data!.bussinessdoctype;
        item.fileVanBan = value.data!.fileVanBan;
        item.fileVanBanBs = value.data!.fileVanBanBs;
        item.ghiChu = value.data!.ghiChu;
        item.hanXuLy = value.data!.hanXuLy;
        item.hienThiVblq = value.data!.hienThiVblq;
        item.maCapDoKhan = value.data!.maCapDoKhan;
        item.maCapDoMat = value.data!.maCapDoMat;
        item.maCtcbDuyet = value.data!.maCtcbDuyet;
        item.maLinhVucVanBan = value.data!.maLinhVucVanBan;
        item.maLoaiVanBan = value.data!.maLoaiVanBan;
        item.maSoVbDen = value.data!.maSoVbDen;
        item.maVanBanDenKc = value.data!.maVanBanDenKc;
        item.maVanBanKc = value.data!.maVanBanKc;
        item.maXuLyDen = value.data!.maXuLyDen;
        item.ngayBanHanh = value.data!.ngayBanHanh;
        item.ngayDen = value.data!.ngayDen;
        item.ngayDuyet = value.data!.ngayDuyet;
        item.ngayNhan = value.data!.ngayNhan;
        item.ngayXem = value.data!.ngayXem.toString();
        item.nguoiKy = value.data!.nguoiKy;
        item.noiLuuBanChinh = value.data!.noiLuuBanChinh;
        item.soBanPhatHanh = value.data!.soBanPhatHanh;
        item.soDen = value.data!.soDen;
        item.soKyHieu = value.data!.soKyHieu;
        item.tenCoQuanBanHanh = value.data!.tenCoQuanBanHanh;
        item.tenDonViGui = value.data!.tenDonViGui;
        item.trangThaiVanBanDen = value.data!.trangThaiVanBanDen;
        item.trichYeu = value.data!.trichYeu;
        item.vblqNgoaiHeThong = value.data!.vanBanLienQuan;
        loadDetailItem();
      }
    });
  }

  void loadDsDmSoVanBan() async {
    try {
      await vbdeProvider.auVbdeDsDmSoVBDE().then((value) {
        if (value.data!.isNotEmpty) {
          DmSoVbde.clear();
          DmSoVbde.addAll(value.data!);
          inputSoVanBan.text = DmSoVbde[0].tenSoVbDen.toString();
          detailsItem.value.maSoVbDen = DmSoVbde[0].maSoVbDenKc!;
          laySoDenBySoVanBan(DmSoVbde[0].maSoVbDenKc!.toInt());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void laySoDenBySoVanBan(int maSoVanBan, [String? tenSoVanBanDen]) async {
    try {
      await vbdeProvider.auVbdeLaySoDe(maSoVanBan).then((value) {
        if (value.data!.soDenHienTai != 0) {
          inputSoDen.text = value.data!.soDenHienTai!.toInt().toString();
          detailsItem.value.soDen = value.data!.soDenHienTai;
          if (tenSoVanBanDen!.isNotEmpty) {
            inputSoVanBan.text = tenSoVanBanDen;
            Get.back();
          }
        }
      });
    } catch (exception) {}
  }

  void loadLoaiVanBan() async {
    await vbdeProvider.auVbdeLoaiVanBan(1).then((value) {
      if (value.data!.isNotEmpty) {
        DmLoaiVanBan.clear();
        DmLoaiVanBan.addAll(value.data!);
        if (item.maLoaiVanBan != null) {
          var loaiVb = DmLoaiVanBan.firstWhere(
              (element) => element.maLoaiVanBanKc == item.maLoaiVanBan,
              orElse: () => DmLoaiVanBan[0]);

          inputLoaiVanBan.text = loaiVb.tenLoaiVanBan.toString();
          detailsItem.value.maLoaiVanBan = loaiVb.maLoaiVanBanKc;
        } else {
          inputLoaiVanBan.text = DmLoaiVanBan[0].tenLoaiVanBan.toString();
          detailsItem.value.maLoaiVanBan = DmLoaiVanBan[0].maLoaiVanBanKc;
        }
      }
    });
  }

  void loadDsDmCapDoKhan() async {
    try {
      await vbdeProvider.auVbdeCapDoKhan().then((value) {
        if (value.data!.isNotEmpty) {
          DmCapDoKhan.clear();
          DmCapDoKhan.addAll(value.data!);
          if (item.maCapDoKhan != null) {
            var capDoKhan = DmCapDoKhan.firstWhere(
                (element) => element.maCapDoKhanKc == item.maCapDoKhan,
                orElse: () => DmCapDoKhan[0]);

            inputCapDoKhan.text = capDoKhan.tenCapDoKhan.toString();
            detailsItem.value.maCapDoKhan = capDoKhan.maCapDoKhanKc;
          } else {
            inputCapDoKhan.text = DmCapDoKhan[0].tenCapDoKhan.toString();
            detailsItem.value.maCapDoKhan = DmCapDoKhan[0].maCapDoKhanKc;
          }
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void loadDsDmCapDoMat() async {
    try {
      await vbdeProvider.auVbdeCapDoMat().then((value) {
        if (value.data!.isNotEmpty) {
          dmCapDoMat.clear();
          dmCapDoMat.addAll(value.data!);
          if (item.maCapDoMat != null) {
            var capDoMat = dmCapDoMat.firstWhere(
                (element) => element.maCapDoMatKc == item.maCapDoMat,
                orElse: () => dmCapDoMat[0]);

            inputCapDoMat.text = capDoMat.tenCapDoMat.toString();
            detailsItem.value.maCapDoMat = capDoMat.maCapDoMatKc;
          } else {
            inputCapDoMat.text = dmCapDoMat[0].tenCapDoMat.toString();
            detailsItem.value.maCapDoMat = dmCapDoMat[0].maCapDoMatKc;
          }
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void loadDsLanhDaoDuyetVbde() async {
    try {
      await vbdeProvider.auVbdeLanhDaoDuyetVbde().then((value) {
        if (value.data!.isNotEmpty) {
          dsLanhDaoDuyetVbde.clear();
          dsLanhDaoDuyetVbde.addAll(value.data!);
          inputLanhDaoDuyet.text = dsLanhDaoDuyetVbde[0].ten.toString();
          detailsItem.value.maCtcbDuyet = dsLanhDaoDuyetVbde[0].ma;
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void getInfoEdxml(String tenFileXml) async {
    try {
      await vbdeProvider.auVbdeGetInfoEdxml(tenFileXml).then((element) {
        if (element.id == 1) {
          vbdeProvider.copyDocumentEdoc(element.data!.filevb).then((res) {
            item.fileVanBan = res.data;
            detailsItem.value.fileVanBan = res.data;
            detailsItem.value.maVanBanDenKc = 0;
            detailsItem.value.nguoiKy = element.data!.nguoiky;
            inputNguoiKy.text = element.data!.nguoiky ?? "";
            detailsItem.value.tenCoQuanBanHanh = element.data!.cqbh;
            inputCQBH.text = element.data!.cqbh.toString();
            detailsItem.value.maLinhVucVanBan =
                dmLinhVuc[0].maLinhVucVanBanKc!.toDouble();
            inputLinhVuc.text = dmLinhVuc[0].tenLinhVucVanBan!.toString();
            detailsItem.value.maCapDoMat = dmCapDoMat[0].maCapDoMatKc!;
            inputCapDoMat.text = dmCapDoMat[0].tenCapDoMat.toString();
            detailsItem.value.maCapDoKhan = DmCapDoKhan[0].maCapDoKhanKc;
            inputCapDoKhan.text = DmCapDoKhan[0].tenCapDoKhan.toString();
            detailsItem.value.maLoaiVanBan = DmLoaiVanBan[0].maLoaiVanBanKc;
            inputLoaiVanBan.text = DmLoaiVanBan[0].tenLoaiVanBan.toString();
            detailsItem.value.noiLuuBanChinh = element.data!.noiluu;
            isLoadData.value = true;
          });
        }
      });
    } catch (exception) {
      isLoadData.value = true;
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.thongBao,
          message: exception.toString());
    }
  }

  void loadDsLinhVucVb() async {
    await vbdeProvider.auVbdeDsLvVb().then((value) {
      dmLinhVuc.clear();
      dmLinhVuc.addAll(value.data!);
      inputLinhVuc.text = value.data![0].tenLinhVucVanBan.toString();
      detailsItem.value.maLinhVucVanBan = value.data![0].maLinhVucVanBanKc;
    });
  }

  void changeLoaiVanBan(LoaiVanBan itemLoai) {
    inputLoaiVanBan.text = itemLoai.tenLoaiVanBan.toString();
    detailsItem.value.maLoaiVanBan = itemLoai.maLoaiVanBanKc;
    Get.back();
  }

  void changeLinhVuc(DsLinhVuc item) {
    inputLinhVuc.text = item.tenLinhVucVanBan.toString();
    detailsItem.value.maLinhVucVanBan = item.maLinhVucVanBanKc;
    Get.back();
  }

  void changeCapDoKhan(dsCapDoKhan item) {
    inputCapDoKhan.text = item.tenCapDoKhan.toString();
    detailsItem.value.maCapDoKhan = item.maCapDoKhanKc;
    Get.back();
  }

  void changeLanhDaoDuyetVbde(LanhDaoDuyetVbde item) {
    inputLanhDaoDuyet.text = item.ten.toString();
    detailsItem.value.maCtcbDuyet = item.ma;
    Get.back();
  }

  void changeCapDoMat(DmCapDoMat item) {
    inputCapDoMat.text = item.tenCapDoMat.toString();
    detailsItem.value.maCapDoMat = item.maCapDoMatKc;
    Get.back();
  }

  void changeNam(int year) {
    inputNam.text = year.toString();
    detailsItem.value.nam = year;
    Get.back();
  }

  void onChangeIsSms(bool? value) {
    isSms.value = value!;
  }

  void onChangeKemVanBanGiay(bool? value) {
    isKemVanBanGiay.value = value ?? false;
  }

  void onGuiLanhDao() async {
    detailsItem.value.sms = isSms.value ? 1 : 0;
    detailsItem.value.guiKemVbGiay = isKemVanBanGiay.value ? 1 : 0;
    detailsItem.value.trangThaiVanBanDen =
        2; // trang thái = 2 lưu chuyển lãnh đạo
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Chuyển lãnh đạo duyệt ?",
        textCancel: "Đóng",
        textConfirm: "Xác nhận",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () async {
          await vbdeProvider.auVbdeThemMoiVbde(detailsItem.value).then((value) {
            if (value.maVanBanDen! > 0) {
              vbdeProvider.auVbdeCnKTmVbd(value.maVanBanDen!).then((DataRow) {
                print(DataRow.message);
                if (DataRow.message == "Thục thi thành công") {
                  print(DataRow.message);
                }
                if (item.loaiVbDienTu != null &&
                    item.loaiVbDienTu!.toInt() == 1 &&
                    int.parse(_getStorage
                            .read(GetStorageKey.vbdeGopChungVbDienTu)) ==
                        1) {
                  vbdeProvider.auVbdeUpadteStatusEdocTemp(
                      detailsItem.value.maVanBanDenKc!.toInt(), 1);
                }
                homeController.loadDSnv();
                vanThuController.getDsVanBanByIndexTab(0);
                Get.delete<XuLyVtVbdeController>();
                CustomSnackBar.showSuccessSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Chuyển lãnh đạo thành công!");
                Get.offNamed(Routers.SOVBDENVT);
              });
            } else {
              CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
              Get.offNamed(Routers.SOVBDENVT);
            }
          });
        });
  }

  void onLuuTam() async {
    detailsItem.value.sms = isSms.value ? 1 : 0;
    detailsItem.value.guiKemVbGiay = isKemVanBanGiay.value ? 1 : 0;
    detailsItem.value.trangThaiVanBanDen =
        1; // trang thái = 2 lưu chuyển lãnh đạo
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Lưu tạm văn bản?",
        textCancel: "Đóng",
        textConfirm: "Xác nhận",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () async {
          await vbdeProvider.auVbdeThemMoiVbde(detailsItem.value).then((value) {
            if (value.maVanBanDen! > 0) {
              vbdeProvider.auVbdeCnKTmVbd(value.maVanBanDen!).then((DataRow) {
                if (item.loaiVbDienTu != null &&
                    item.loaiVbDienTu!.toInt() == 1 &&
                    int.parse(_getStorage
                            .read(GetStorageKey.vbdeGopChungVbDienTu)) ==
                        1) {
                  vbdeProvider.auVbdeUpadteStatusEdocTemp(
                      detailsItem.value.maVanBanDenKc!.toInt(), 1);
                }
                CustomSnackBar.showSuccessSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Lưu tạm thành công!");
                Get.delete<XuLyVtVbdeController>();
                Get.offNamed(Routers.SOVBDENVT);
                homeController.loadDSnv();
                vanThuController.getDsVanBanByIndexTab(0);
              });
            } else {
              Get.delete<XuLyVtVbdeController>();
              CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
              Get.offNamed(Routers.SOVBDENVT);
            }
          });
        });
  }

  void onHuy() async {
    if (item.loaiVbDienTu != null &&
        item.loaiVbDienTu!.toInt() == 1 &&
        int.parse(_getStorage.read(GetStorageKey.vbdeGopChungVbDienTu)) == 1) {
      await vbdeProvider.auVbdeThemDeHuyVbEdoc(detailsItem.value).then((value) {
        if (value.maVanBanDen! > 0) {
          Get.defaultDialog(
              title: "Xác nhận",
              middleText: "Huỷ văn bản đến?",
              textCancel: "Đóng",
              textConfirm: "Xác nhận",
              confirmTextColor: Colors.white,
              onCancel: () {},
              onConfirm: () {
                vbdeProvider.auVbdeUpadteStatusEdocTemp(
                    detailsItem.value.maVanBanDenKc!.toInt(), -2);
                vbdeProvider
                    .auVbdeUpdateStatusEdxml(detailsItem.value.tenGoiTinXml!);
                vbdeProvider.auVbdeUpdateStatusTrucEdoc(
                    detailsItem.value.tenGoiTinXml!);
                if (value.maVanBanDen! > 0) {
                  Get.delete<XuLyVtVbdeController>();
                  homeController.loadDSnv();
                  vanThuController.getDsVanBanByIndexTab(0);
                  CustomSnackBar.showSuccessSnackBar(
                      context: Get.context,
                      title: "Thông báo",
                      message: "Huỷ văn bản thành công!");
                  Get.offNamed(Routers.SOVBDENVT);
                } else {
                  Get.delete<XuLyVtVbdeController>();
                  CustomSnackBar.showWarningSnackBar(
                      context: Get.context,
                      title: "Thông báo",
                      message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
                  Get.offNamed(Routers.SOVBDENVT);
                }
              });
        }
      });
    } else {
      Get.defaultDialog(
          title: "Xác nhận",
          middleText: "Huỷ văn bản đến?",
          textCancel: "Đóng",
          textConfirm: "Xác nhận",
          confirmTextColor: Colors.white,
          onCancel: () {},
          onConfirm: () async {
            await vbdeProvider
                .auVbdeVanThuHVBDDT(detailsItem.value.maXuLyDen!.toInt())
                .then((value) {
              if (value.huy_duoc == 1) {
                homeController.loadDSnv();

                vanThuController.getDsVanBanByIndexTab(0);
                CustomSnackBar.showSuccessSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Huỷ văn bản thành công!");
                Get.delete<XuLyVtVbdeController>();
                Get.offNamed(Routers.SOVBDENVT);
              } else {
                CustomSnackBar.showWarningSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
                Get.delete<XuLyVtVbdeController>();
                Get.offNamed(Routers.SOVBDENVT);
              }
            });
          });
    }
  }
}
