import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_dschualuuvt_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyvt_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class VanThuController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<DsVanDenDienTu>> {
  var vbdeProvider = VbdeProvider();
  final _getStorage = GetStorage();
  late TabController tabController;
  // khai báo
  int page = 1;
  int size = 20;
  int totalPage = 0;
  Rx<int> indexTabGobal = 0.obs;
  // List danh sách
  List<DsVanDenDienTu> dsDienTu = [];
  List<DsVanDenDienTu> dsVbDaChuyenVt = [];
  List<DsVanDenDienTu> dsVbDaHuyVt = [];
  List<DsVanDenDienTu> dsVbLuuTamVt = [];
  List<DsVanDenDienTu> dsVbLdTraLai = [];
  List<DsVanDenDienTu> dsVbLdChoDuyetVt = [];
  List<DsVanDenDienTu> dsVbChoChuyenVt = [];

  ScrollController scrollControllerVanDienTu = ScrollController();
  ScrollController scrollControllerLuuTam = ScrollController();
  ScrollController scrollControllerDaChuyen = ScrollController();
  ScrollController scrollControllerDaHuy = ScrollController();
  ScrollController scrollControllerLdTraLai = ScrollController();
  ScrollController scrollControllerLdChoDuyet = ScrollController();
  ScrollController scrollControllerChoChuyen = ScrollController();
  late TextEditingController searchController;
  String? keyWord = "";
  @override
  void onInit() {
    super.onInit();
    var initIndex = Get.arguments != null ? Get.arguments['indexTab'] : 0;
    tabController =
        TabController(length: 7, initialIndex: initIndex, vsync: this);
    searchController = TextEditingController();
    indexTabGobal.value = initIndex;
    getDsVanBanByIndexTab(indexTabGobal.value);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void dispose() {
    tabController.dispose();
    scrollControllerVanDienTu.dispose();
    scrollControllerDaChuyen.dispose();
    scrollControllerDaHuy.dispose();
    scrollControllerLuuTam.dispose();
    scrollControllerLdTraLai.dispose();
    scrollControllerLdChoDuyet.dispose();
    scrollControllerChoChuyen.dispose();
    super.dispose();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }

  void getDsVanBanByIndexTab(int newIndex) {
    indexTabGobal.value = newIndex;
    clearAllList();
    switch (newIndex) {
      case 0:
        getDsVanBanDienTu();
        loadMoreDs();
        break;
      case 1:
        getDsVanBanLuuTamVt();
        loadMoreDs();
        break;
      case 2:
        getDsVanBanLdTraLai();
        loadMoreDs();
        break;
      case 3:
        getDsVanBanLdChoDuyetVt();
        loadMoreDs();
        break;
      case 4:
        getDsVanBanChoChuyenVt();
        loadMoreDs();
        break;
      case 5:
        getDsVanBanDaChuyenVt();
        loadMoreDs();
        break;
      case 6:
        getDsVanBanDaHuyVt();
        loadMoreDs();
        break;
      default:
    }
  }

  void clearAllList() {
    dsDienTu.clear();
    dsVbDaChuyenVt.clear();
    dsVbDaHuyVt.clear();
    dsVbLuuTamVt.clear();
    dsVbLdTraLai.clear();
    dsVbLdChoDuyetVt.clear();
    dsVbChoChuyenVt.clear();
  }

  void setKeyWordSearch(String? searchWord) {
    keyWord = searchWord;
    switch (indexTabGobal.value) {
      case 0:
        getDsVanBanDienTu();
        break;
      case 1:
        getDsVanBanLuuTamVt();
        break;
      case 2:
        getDsVanBanLdTraLai();
        break;
      case 3:
        getDsVanBanLdChoDuyetVt();
        break;
      case 4:
        getDsVanBanChoChuyenVt();
        break;
      case 5:
        getDsVanBanDaChuyenVt();
        break;
      case 6:
        getDsVanBanDaHuyVt();
        break;
      default:
    }
  }

  void getDsVanBanDienTu() async {
    try {
      page = 1;
      await vbdeProvider.auVbdeDienTuChuaLuu(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          if (value.data!.isNotEmpty) {
            vbdeProvider.auVbdeDienTuChuaLuu(0, size, keyWord).then((data) {
              double totalrow = (data.data![0].totalRow!.toInt() ?? 1) / page;
              totalPage = totalrow.floor();
              dsDienTu.clear();
              dsDienTu.addAll(value.data!);
              change(dsDienTu, status: RxStatus.success());
            });
          } else {
            change(null, status: RxStatus.empty());
          }
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanLdTraLai() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdeProvider.auVbdeDsLdTraLai(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeDsLdTraLai(0, size, keyWord).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbLdTraLai.clear();
            dsVbLdTraLai.addAll(value.data!);
            change(dsVbLdTraLai, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanLdChoDuyetVt() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdeProvider.auVbdeLdChoDuyetVt(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeLdChoDuyetVt(0, size).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbLdChoDuyetVt.clear();
            dsVbLdChoDuyetVt.addAll(value.data!);
            change(dsVbLdChoDuyetVt, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanChoChuyenVt() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdeProvider.auVbdeDsChoChuyeVt(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeDsChoChuyeVt(0, size, keyWord).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbChoChuyenVt.clear();
            dsVbChoChuyenVt.addAll(value.data!);
            change(dsVbChoChuyenVt, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanDaChuyenVt() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdeProvider.auVbdeDaChuyen(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeDaChuyen(0, size, keyWord).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbDaChuyenVt.clear();
            dsVbDaChuyenVt.addAll(value.data!);
            change(dsVbDaChuyenVt, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanDaHuyVt() async {
    try {
      page = 1;
      change(null, status: RxStatus.empty());
      await vbdeProvider.auVbdeDaHuyCuaVt(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeDaHuyCuaVt(0, size, keyWord).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbDaHuyVt.clear();
            dsVbDaHuyVt.addAll(value.data!);
            change(dsVbDaHuyVt, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void getDsVanBanLuuTamVt() async {
    try {
      page = 1;
      change(null, status: RxStatus.empty());
      await vbdeProvider.auVbdeDsLuuTamVt(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          vbdeProvider.auVbdeDsLuuTamVt(0, size, keyWord).then((item) {
            double totalrow = (item.data![0].totalRow!.toInt() ?? 1) / page;
            totalPage = totalrow.floor();
            dsVbLuuTamVt.clear();
            dsVbLuuTamVt.addAll(value.data!);
            change(dsVbLuuTamVt, status: RxStatus.success());
          });
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: AppString.noMoreItem);
    }
  }

  void loadMoreDs() {
    try {
      switch (indexTabGobal.value) {
        case 0:
          scrollControllerVanDienTu.addListener(() {
            if (scrollControllerVanDienTu.position.maxScrollExtent ==
                scrollControllerVanDienTu.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDs();
              }
            }
          });
          break;
        case 5:
          scrollControllerDaChuyen.addListener(() {
            if (scrollControllerDaChuyen.position.maxScrollExtent ==
                scrollControllerDaChuyen.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDs();
              }
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void getMoreDs() async {
    Get.dialog(Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SpinKitThreeBounce(
          size: 50,
          color: AppColor.blueAccentColor,
        )
      ],
    ));
    switch (indexTabGobal.value) {
      case 0:
        await vbdeProvider.auVbdeDienTuChuaLuu(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            if (value.data!.isNotEmpty) {
              dsDienTu.addAll(value.data!);
              change(dsDienTu, status: RxStatus.success());
            }
          }
        });
        break;
      case 1:
        await vbdeProvider.auVbdeDsLuuTamVt(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbLuuTamVt.addAll(value.data!);
            change(dsVbLuuTamVt, status: RxStatus.success());
          }
        });
        break;
      case 2:
        await vbdeProvider.auVbdeDsLdTraLai(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbLdTraLai.addAll(value.data!);
            change(dsVbLdTraLai, status: RxStatus.success());
          }
        });
        break;
      case 3:
        await vbdeProvider.auVbdeLdChoDuyetVt(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbLdChoDuyetVt.addAll(value.data!);
            change(dsVbLdChoDuyetVt, status: RxStatus.success());
          }
        });
        break;
      case 4:
        await vbdeProvider.auVbdeDsChoChuyeVt(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbChoChuyenVt.addAll(value.data!);
            change(dsVbChoChuyenVt, status: RxStatus.success());
          }
        });
        break;
      case 5:
        await vbdeProvider.auVbdeDaChuyen(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbDaChuyenVt.addAll(value.data!);
            change(dsVbDaChuyenVt, status: RxStatus.success());
          }
        });
        break;
      case 6:
        await vbdeProvider.auVbdeDaHuyCuaVt(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            dsVbDaHuyVt.addAll(value.data!);
            change(dsVbDaHuyVt, status: RxStatus.success());
          }
        });
        break;
      default:
    }
  }

  void updateViewCv(String chuoiMaVanBanDen) async {
    await vbdeProvider.cvDaXemVBD(chuoiMaVanBanDen);
  }

  void onPressItem(item) {
    var maVanBanDen = item.maVanBanDenKc;
    var maXuLyDen = item.maXuLyDen;
    updateViewCv(maVanBanDen!.toInt().toString());
    Get.delete<XuLyVtVbdeController>();
    Get.toNamed(Routers.THEMMOIVBDEVT, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'item': item,
      'indexTab': indexTabGobal.value
    });
  }

  void onDetaitsItem(item) {
    var maVanBanDen = item.maVanBanDenKc;
    var maXuLyDen = item.maXuLyDen;
    _getStorage.write(GetStorageKey.maVanBanDenKc, maVanBanDen);
    _getStorage.write(GetStorageKey.maXulyDenKc, maXuLyDen);
    updateViewCv(maVanBanDen!.toInt().toString());
    Get.delete<ChiTietVbdeController>();
    Get.toNamed(Routers.DETAILVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXuLyDen,
      'isQuyen': "isVanThu",
      'indexTab': indexTabGobal.value
    });
  }
}
