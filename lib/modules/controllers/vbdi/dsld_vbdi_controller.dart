import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_dsldduyet_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DuyetLDVbdiController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<DuyetVbdi>> {
  var vbdiProvider = VbdiProvider();
  var _store = GetStorage();
  late TabController tabController;
  Rx<int> indexTabGobal = 0.obs;
  List<DuyetVbdi> ListChoDuyetVbdi = [];
  List<DuyetVbdi> UyQuyen = [];
  List<DuyetVbdi> daPhatHanh = [];
  List<DuyetVbdi> daChuyenVanThu = [];
  late String? keyWord = "";
  int page = 1;
  int size = 20;
  int totalPage = 1;
  ScrollController scrollerControllerChoDuyet = ScrollController();
  ScrollController scrollerControllerUyQuyen = ScrollController();
  ScrollController scrollerControllerDaChuyenVt = ScrollController();
  ScrollController scrollerControllerDaPhatHanh = ScrollController();
  var isLoadData = false.obs;

  Future<void> loadChoDuyetVbdi() async {
    page = 1;
    change(null, status: RxStatus.loading());
    isLoadData(true);
    try {
      await vbdiProvider.auVbdiLdChoDuyet(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          totalPage = value.totalPage!;
          ListChoDuyetVbdi.clear();
          ListChoDuyetVbdi.addAll(value.data!);
          isLoadData(false);
          change(ListChoDuyetVbdi, status: RxStatus.success());
        } else {
          isLoadData(false);
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      isLoadData(false);
      change(null, status: RxStatus.empty());
    }
  }

  Future<void> loadUyQuyenVbdi() async {
    page = 1;
    change(null, status: RxStatus.loading());
    try {
      await vbdiProvider.auVbdiUyQuyenLd(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          totalPage = value.totalPage!;
          UyQuyen.clear();
          UyQuyen.addAll(value.data!);
          change(UyQuyen, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  Future<void> loadDaChuyenVt() async {
    page = 1;
    change(null, status: RxStatus.loading());
    try {
      await vbdiProvider
          .auVbdiDaChuyenVanThu(page, size, keyWord)
          .then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          totalPage = value.totalPage!;
          daChuyenVanThu.clear();
          daChuyenVanThu.addAll(value.data!);
          change(daChuyenVanThu, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  void loadDaPhatHanh() async {
    page = 1;
    change(null, status: RxStatus.loading());
    try {
      await vbdiProvider.auVbdiDaPhatHanhVt(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          totalPage = value.totalPage!;
          daPhatHanh.clear();
          daPhatHanh.addAll(value.data!);
          change(daPhatHanh, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  void getDsVbdiByIndex(int index) {
    indexTabGobal.value = index;
    switch (index) {
      case 0:
        loadChoDuyetVbdi();
        loadMoreds();
        break;
      case 1:
        loadUyQuyenVbdi();
        loadMoreds();
        break;
      case 2:
        loadDaChuyenVt();
        loadMoreds();
        break;
      case 3:
        loadDaPhatHanh();
        loadMoreds();
        break;
      default:
    }
  }

  void setSearchKey(String? text) {
    keyWord = text;
    switch (indexTabGobal.value) {
      case 0:
        loadChoDuyetVbdi();

        break;
      case 1:
        loadUyQuyenVbdi();
        break;
      case 2:
        loadDaChuyenVt();
        break;
      case 3:
        loadDaPhatHanh();
        break;
      default:
    }
  }

  void loadMoreds() {
    try {
      switch (indexTabGobal.value) {
        case 0:
          scrollerControllerChoDuyet.addListener(() {
            if (scrollerControllerChoDuyet.position.maxScrollExtent ==
                scrollerControllerChoDuyet.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreds();
              }
            }
          });
          break;
        case 1:
          scrollerControllerUyQuyen.addListener(() {
            if (scrollerControllerUyQuyen.position.maxScrollExtent ==
                scrollerControllerUyQuyen.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreds();
              }
            }
          });
          break;
        case 2:
          scrollerControllerDaChuyenVt.addListener(() {
            if (scrollerControllerDaChuyenVt.position.maxScrollExtent ==
                scrollerControllerDaChuyenVt.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreds();
              }
            }
          });
          break;
        case 3:
          scrollerControllerDaPhatHanh.addListener(() {
            if (scrollerControllerDaPhatHanh.position.maxScrollExtent ==
                scrollerControllerDaPhatHanh.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreds();
              }
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void getMoreds() async {
    Get.dialog(Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SpinKitThreeBounce(
          size: 50,
          color: AppColor.blueAccentColor,
        )
      ],
    ));
    switch (indexTabGobal.value) {
      case 0:
        await vbdiProvider.auVbdiLdChoDuyet(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liêụ thành công") {
            ListChoDuyetVbdi.addAll(value.data!);
            change(ListChoDuyetVbdi, status: RxStatus.success());
          }
        });
        break;
      case 1:
        await vbdiProvider.auVbdiUyQuyenLd(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            UyQuyen.addAll(value.data!);
            change(UyQuyen, status: RxStatus.success());
          }
        });
        break;
      case 2:
        await vbdiProvider.auVbdiDaChuyenVanThu(page, size).then(((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            daChuyenVanThu.addAll(value.data!);
            change(daChuyenVanThu, status: RxStatus.success());
          }
        }));
        break;
      case 3:
        await vbdiProvider.auVbdiDaPhatHanhVt(page, size).then((value) {
          Get.back();
          if (value.message == "Lấy dữ liệu thành công") {
            daPhatHanh.addAll(value.data!);
            change(daPhatHanh, status: RxStatus.success());
          }
        });
        break;
      default:
    }
  }

  @override
  void onInit() {
    tabController = TabController(length: 4, initialIndex: 0, vsync: this);
    getDsVbdiByIndex(0);
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void dispose() {
    scrollerControllerChoDuyet.dispose();
    scrollerControllerUyQuyen.dispose();
    scrollerControllerDaChuyenVt.dispose();
    scrollerControllerDaPhatHanh.dispose();
  }

  void onChangeTab(int indexTab) {
    // set indexTab
    getDsVbdiByIndex(indexTab);
    indexTabGobal.value = indexTab;
  }

  void onDetailVbdi(int maVanBanDi, int maXuLyVanBanDi, int indexTab) {
    Get.delete<ChiTietVbdiController>();
    Get.toNamed(Routers.DETAILSVBDI, arguments: {
      "maVanBanDi": maVanBanDi,
      "indexTab": indexTab,
      "isLanhDao": 1,
      "maXuLyVanBanDi": maXuLyVanBanDi
    });
  }
}
