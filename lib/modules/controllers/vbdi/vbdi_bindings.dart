import 'package:get/get.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dsld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/xulyld_vbdi_controller.dart';

class VbdiChoDuyetBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DuyetLDVbdiController());
  }
}

class ChiTietVbdiBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ChiTietVbdiController());
  }
}

class XuLyVbdiBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => XulyLdVbdiController());
  }
}

class DsCvVbdiBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DsCvVbdiController());
  }
}

class KySoVbdiBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => KySoController());
  }
}
