import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/kyso/kyso_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dsld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class XulyLdVbdiController extends GetxController {
  var vbdiProvider = VbdiProvider();
  var vbdeProvider = VbdeProvider();
  var kysiProvider = KysoProvider();
  var _store = GetStorage();
  //lãnh đạo
  var ListVanThu = <DsVanThu>[].obs;
  var listLanhDaoKhac = <DsLanhDaoKhacVbdi>[].obs;
  var listChuyenVien = <DsChuyenVienVbdi>[].obs;
  var checkBoxLanhDaoKhac = <int, bool>{}.obs;
  var checkBoxVanThu = <int, bool>{}.obs;
  var checkBoxChuyenVien = <int, bool>{}.obs;

  var searchKeyLDK = "".obs;
  var searchKeyCv = "".obs;
  var searchKeyVt = "".obs;
  var isSmsLdk = false.obs;
  var isSmsVt = false.obs;
  var isSmsCv = false.obs;

  // chuyên viên
  var CheckBoxLanhDaoCv = <int, bool>{}.obs;
  var checkBoxVanThucCv = <int, bool>{}.obs;
  var checkBoxChuyenVienCV = <int, bool>{}.obs;

  var listLanhDaoCv = <DsLanhDaoKhacVbdi>[].obs;

  var searchKeyLdCv = "".obs;
  var searChKeyCvkCv = "".obs;
  var searchKeyVtCv = "".obs;

  var isSmsLdCv = false.obs;
  var isSmsVtCv = false.obs;
  var isSmsCvCv = false.obs;

  late VbdiChiTiet detailsVbdi;

  late String chuoiFileVblLienQuan;
  late String chuoiMaVbLienQuan;

  late TextEditingController inputYKienVt;
  late TextEditingController inputYKienLdk;
  late TextEditingController inputYKienCv;

  late TextEditingController inputYKienVtCv;
  late TextEditingController inputYKienLdCv;
  late TextEditingController inputYKienCvkCv;

  @override
  void onInit() {
    super.onInit();
    detailsVbdi = Get.arguments["DetalsVbdi"];
    chuoiFileVblLienQuan = Get.arguments["vbLienQuan"];
    chuoiMaVbLienQuan = Get.arguments["maVbLienQuan"];
    inputYKienVt = TextEditingController();
    inputYKienLdk = TextEditingController();
    inputYKienCv = TextEditingController();
    inputYKienVtCv = TextEditingController();
    inputYKienLdCv = TextEditingController();
    inputYKienCvkCv = TextEditingController();
  }

  void loadDsVanThu() {
    vbdiProvider.auVbdiDsVttDvQt().then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        ListVanThu.clear();
        ListVanThu.addAll(value.data!);
        if (ListVanThu.isNotEmpty) {
          ListVanThu.firstWhere(
              (element) => checkBoxVanThu[element.maCtcbKc!.toInt()] = true);
          ListVanThu.firstWhere(
              (element) => checkBoxVanThucCv[element.maCtcbKc!.toInt()] = true);
        }
      }
    });
  }

  void loadDsLanhDaoKhac() {
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    vbdiProvider.auVbdiDsLdCDKhac().then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        listLanhDaoKhac.clear();
        listLanhDaoKhac
            .addAll(value.data!.where((element) => element.ma != maCtcbKc));
        listLanhDaoCv.addAll(value.data!);
        listLanhDaoCv.firstWhere(
            (element) => CheckBoxLanhDaoCv[element.ma!.toInt()] = true);
      }
    });
  }

  void loadDsChuyenVien() {
    vbdiProvider.auVbdiDsCbCv().then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        listChuyenVien.clear();
        listChuyenVien.addAll(value.data!);
      }
    });
  }

  void toggleCheckboxVanThuValue(int value) {
    checkBoxVanThu[value] = !(checkBoxVanThu[value] ?? false);
  }

  void toggleCheckboxLDKvalue(int value) {
    checkBoxLanhDaoKhac[value] = !(checkBoxLanhDaoKhac[value] ?? false);
  }

  void toggleCheckboxCvValue(int value) {
    if (checkBoxChuyenVien.isNotEmpty) {
      checkBoxChuyenVien.clear();
    }
    checkBoxChuyenVien[value] = !(checkBoxChuyenVien[value] ?? false);
  }

  // chuyên viên
  void toggleCheckboxCvCvValue(int value) {
    if (checkBoxChuyenVienCV.isNotEmpty) {
      checkBoxChuyenVienCV.clear();
    }
    checkBoxChuyenVienCV[value] = !(checkBoxChuyenVienCV[value] ?? false);
  }

  void toggleCheckboxLdCvValue(int value) {
    if (CheckBoxLanhDaoCv.isNotEmpty) {
      CheckBoxLanhDaoCv.clear();
    }
    CheckBoxLanhDaoCv[value] = !(CheckBoxLanhDaoCv[value] ?? false);
  }

  void toggleCheckboxVtCvValue(int value) {
    checkBoxVanThucCv[value] = !(checkBoxVanThucCv[value] ?? false);
  }

  List<DsLanhDaoKhacVbdi> get fillterDsLanhDaoKhac {
    if (searchKeyLDK.value.isEmpty) {
      return listLanhDaoKhac;
    } else {
      return listLanhDaoKhac
          .where((item) => item.ten!
              .toLowerCase()
              .contains(searchKeyLDK.value.toLowerCase()))
          .toList();
    }
  }

  List<DsChuyenVienVbdi> get fillterDsChuyenVienVbdi {
    if (searchKeyCv.value.isEmpty) {
      return listChuyenVien;
    } else {
      return listChuyenVien
          .where((item) => item.hoVaTenCanBo!
              .toLowerCase()
              .contains(searchKeyCv.value.toLowerCase()))
          .toList();
    }
  }

  List<DsVanThu> get fillterDsVanThuVbdi {
    if (searchKeyVt.value.isEmpty) {
      return ListVanThu;
    } else {
      return ListVanThu.where((item) => item.hoVaTenCanBo!
          .toLowerCase()
          .contains(searchKeyVt.value.toLowerCase())).toList();
    }
  }

  void setSearchTextLdk(String text) {
    searchKeyLDK.value = text;
  }

  void setSearchTextCv(String text) {
    searchKeyCv.value = text;
  }

  void setSearchTextVt(String text) {
    searchKeyVt.value = text;
  }

  void setSearchTextLdCv(String text) {
    searchKeyLdCv.value = text;
  }

  void setSearchTextCvCv(String text) {
    searChKeyCvkCv.value = text;
  }

  void setSearchTextVtCv(String text) {
    searchKeyVtCv.value = text;
  }

  void onChangeSmsLdk(bool isSms) {
    isSmsLdk.value = isSms;
  }

  void onChangeSmsLdCv(bool isSms) {
    isSmsLdCv.value = isSms;
  }

  // chuyên viên

  List<DsChuyenVienVbdi> get fillterDsChuyenVienCvVbdi {
    if (searChKeyCvkCv.value.isEmpty) {
      return listChuyenVien;
    } else {
      return listChuyenVien
          .where((item) => item.hoVaTenCanBo!
              .toLowerCase()
              .contains(searChKeyCvkCv.value.toLowerCase()))
          .toList();
    }
  }

  List<DsLanhDaoKhacVbdi> get fillterDsLanhDaoCv {
    if (searchKeyLdCv.value.isEmpty) {
      return listLanhDaoCv;
    } else {
      return listLanhDaoCv
          .where((item) => item.ten!
              .toLowerCase()
              .contains(searchKeyLdCv.value.toLowerCase()))
          .toList();
    }
  }

  List<DsVanThu> get fillterDsVanThuCvVbdi {
    if (searchKeyVtCv.value.isEmpty) {
      return ListVanThu;
    } else {
      return ListVanThu.where((item) => item.hoVaTenCanBo!
          .toLowerCase()
          .contains(searchKeyVtCv.value.toLowerCase())).toList();
    }
  }

  void onSendVanThu() {
    checkBoxVanThu.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn lãnh đaọ!")
        : Get.defaultDialog(
            title: "Xác nhận",
            middleText: "Bạn muốn duyệt chuyển văn thư?",
            textCancel: "Đóng",
            textConfirm: "Xác nhận",
            confirmTextColor: Colors.white,
            onCancel: () {},
            onConfirm: () {
              confirmSendVanThu();
            });
  }

  void confirmSendVanThu() async {
    var sms = isSmsVt.value ? 1 : 0;
    var loaiXuLy = 7;
    var fileVanBan = detailsVbdi.data!.fileVanBan;
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    var arrayListVt = [];
    var maCtcbVtArr = [];
    checkBoxVanThu.forEach((key, value) {
      if (value) {
        arrayListVt.add(
            ListVanThu.firstWhere((element) => element.maCtcbKc == key)
                .toJson());
        maCtcbVtArr.add(key);
      }
    });
    String strArrayVt = arrayListVt.join(';');
    String strArrayCtcbVt = maCtcbVtArr.join(';');
    kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      vbdiProvider
          .auVbdiLDCVT(
              _store.read(GetStorageKey.maVanBanDiKc).toInt(),
              maCtcbKc,
              strArrayVt,
              inputYKienVt.text,
              fileVanBan!,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "",
              chuoiFileVblLienQuan,
              chuoiMaVbLienQuan,
              strArrayCtcbVt,
              loaiXuLy,
              _store.read(GetStorageKey.maXuLyDiKc).toInt(),
              sms)
          .then((item) {
        if (item.message == "Thực thi thành công") {
          vbdiProvider
              .auVbdiLDDVBD(
                  maCtcbKc, _store.read(GetStorageKey.maVanBanDiKc).toInt())
              .then((resp) {
            if (isSmsVt.value) {
              var arrSDT = [];
              arrayListVt.forEach((element) {
                arrSDT.add(element.diDongCanBo);
              });
              if (arrSDT.isNotEmpty) {
                String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi);
                String chuoiDiDong = arrSDT.join(",");
                vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                    _store.read(GetStorageKey.maDonViQuanTri));
              }
            }
            DuyetLDVbdiController duyetldVbdiController = Get.find();
            duyetldVbdiController.loadChoDuyetVbdi();
            HomeController homeController = Get.find();
            homeController.loadDSnv();
            clearInput();
            Get.delete<ChiTietVbdiController>();
            CustomSnackBar.showSuccessSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Thực hiện thành công!");
            Get.offNamed(Routers.DUYETVBDI);
          });
        }
      });
    });
  }

  void onSendLdkVbdi() {
    checkBoxLanhDaoKhac.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn lãnh đaọ!")
        : Get.defaultDialog(
            title: "Xác nhận",
            middleText:
                "Bạn muốn duyệt chuyển ${listLanhDaoKhac.firstWhere((element) => element.ma == checkBoxLanhDaoKhac.keys.first).ten} ?",
            textCancel: "Đóng",
            textConfirm: "Xác nhận",
            confirmTextColor: Colors.white,
            onCancel: () {},
            onConfirm: () {
              confirmSendLdK();
            });
  }

  void confirmSendLdK() async {
    int smsLdk = isSmsLdk.value ? 1 : 0;
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCtcbNhan = checkBoxLanhDaoKhac.keys.first;
    List<DsLanhDaoKhacVbdi> listLdk = [];
    checkBoxLanhDaoKhac.forEach((key, value) {
      if (value) {
        listLdk.add(listLanhDaoKhac.firstWhere((element) => element.ma == key));
      }
    });
    await kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      vbdiProvider
          .auVbdiLdcVbdCld(
              _store.read(GetStorageKey.maVanBanDiKc).toInt(),
              maCtcbKc,
              maCtcbNhan,
              inputYKienLdk.text,
              _store.read(GetStorageKey.fileVbdi),
              _store.read(GetStorageKey.maXuLyDiKc).toInt(),
              smsLdk,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "")
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          if (smsLdk == 1) {
            var arrSdt = [];
            listLdk.forEach((element) {
              arrSdt.add(element.diDongCanBo);
            });
            if (arrSdt.isNotEmpty) {
              String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi);
              String chuoiDiDong = arrSdt.join(",");
              vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                  _store.read(GetStorageKey.maDonViQuanTri));
            }
          }

          DuyetLDVbdiController duyetldVbdiController = Get.find();
          duyetldVbdiController.loadChoDuyetVbdi();
          HomeController homeController = Get.find();
          homeController.loadDSnv();
          clearInput();
          checkBoxLanhDaoKhac.clear();
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.DUYETVBDI);
        } else {
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đã có lỗi xảy ra!");
          Get.offNamed(Routers.DUYETVBDI);
        }
      });
    });
  }

  void onSendCvVbdi() {
    checkBoxChuyenVien.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn chuyên viên!")
        : Get.defaultDialog(
            title: "Xác nhận",
            middleText:
                "Bạn muốn chuyển cho ${listChuyenVien.firstWhere((element) => element.maCtcbKc == checkBoxChuyenVien.keys.first).hoVaTenCanBo} ?",
            textCancel: "Đóng",
            textConfirm: "Xác nhận",
            confirmTextColor: Colors.white,
            onCancel: () {},
            onConfirm: () {
              confirmSendCv();
            });
  }

  void confirmSendCv() async {
    int smsCv = isSmsCv.value ? 1 : 0;
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCtcbNhan = checkBoxChuyenVien.keys.first;
    List<DsChuyenVienVbdi> listCv = [];
    checkBoxLanhDaoKhac.forEach((key, value) {
      if (value) {
        listCv.add(
            listChuyenVien.firstWhere((element) => element.maCtcbKc == key));
      }
    });
    await kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      vbdiProvider
          .auVbdiLdCVbdCcv(
              _store.read(GetStorageKey.maVanBanDiKc).toInt(),
              maCtcbKc,
              maCtcbNhan,
              inputYKienCv.text,
              _store.read(GetStorageKey.fileVbdi),
              _store.read(GetStorageKey.maXuLyDiKc).toInt(),
              smsCv,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "")
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          if (smsCv == 1) {
            var arrSdt = [];
            listCv.forEach((element) {
              arrSdt.add(element.diDongCanBo);
            });
            if (arrSdt.isNotEmpty) {
              String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi)!;
              String chuoiDiDong = arrSdt.join(",");
              vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                  _store.read(GetStorageKey.maDonViQuanTri));
            }
          }
          DsCvVbdiController dsCvVbdiController = Get.find();
          dsCvVbdiController.loadDsChuaXuLy();
          DuyetLDVbdiController duyetldVbdiController = Get.find();
          duyetldVbdiController.loadChoDuyetVbdi();
          HomeController homeController = Get.find();
          homeController.loadDSnv();
          clearInput();
          Get.delete<ChiTietVbdiController>();
          checkBoxLanhDaoKhac.clear();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.DUYETVBDI);
        } else {
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đã có lỗi xảy ra!");
          Get.offNamed(Routers.DUYETVBDI);
        }
      });
    });
  }

  // Chuyên viên chuyển xử lý

  void onCvChuyenLanhDao() async {
    CheckBoxLanhDaoCv.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn lãnh đạo!")
        : Get.defaultDialog(
            title: "Xác nhận",
            middleText:
                "Bạn muốn duyệt chuyển lãnh đạo ${listLanhDaoKhac.firstWhere((element) => element.ma == CheckBoxLanhDaoCv.keys.first).ten} ?",
            textCancel: "Đóng",
            textConfirm: "Xác nhận",
            confirmTextColor: Colors.white,
            onCancel: () {},
            onConfirm: () {
              confirmCvChuyenLanhDao();
            });
  }

  void confirmCvChuyenLanhDao() async {
    int smsCvChuyenLd = isSmsLdCv.value ? 1 : 0;
    String? noiDungChuyen = inputYKienLdCv.text;
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCtcbNhan = CheckBoxLanhDaoCv.keys.first;
    List<DsLanhDaoKhacVbdi> listCvLanhDao = [];
    CheckBoxLanhDaoCv.forEach((key, value) {
      if (value) {
        listCvLanhDao
            .add(listLanhDaoKhac.firstWhere((element) => element.ma == key));
      }
    });
    await kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      vbdiProvider
          .auVbdiLdcVbdCld(
              _store.read(GetStorageKey.maVanBanDiKc).toInt(),
              maCtcbKc,
              maCtcbNhan,
              noiDungChuyen,
              _store.read(GetStorageKey.fileVbdi),
              _store.read(GetStorageKey.maXuLyDiKc).toInt(),
              smsCvChuyenLd,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "")
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          if (smsCvChuyenLd == 1) {
            var arrSdt = [];
            listCvLanhDao.forEach((element) {
              arrSdt.add(element.diDongCanBo);
            });
            if (arrSdt.isNotEmpty) {
              String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi);
              String chuoiDiDong = arrSdt.join(",");
              vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                  _store.read(GetStorageKey.maDonViQuanTri));
            }
          }

          HomeController homeController = Get.find();
          homeController.loadDSnv();
          CheckBoxLanhDaoCv.clear();
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.DSCVVBDI);
        } else {
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đã có lỗi xảy ra!");
          Get.offNamed(Routers.DSCVVBDI);
        }
      });
    });
  }

  // chuyên viên chuyển chuyen viên
  void onCvChuyenCv() {
    checkBoxChuyenVienCV.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn chuyên viên!")
        : Get.defaultDialog(
            title: "Xác nhận",
            middleText:
                "Bạn muốn chuyển cho ${listChuyenVien.firstWhere((element) => element.maCtcbKc == checkBoxChuyenVienCV.keys.first).hoVaTenCanBo} ?",
            textCancel: "Đóng",
            textConfirm: "Xác nhận",
            confirmTextColor: Colors.white,
            onCancel: () {},
            onConfirm: () {
              confirmCvSendCv();
            });
  }

  void confirmCvSendCv() async {
    int smsCv = isSmsCv.value ? 1 : 0;
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maCtcbNhan = checkBoxChuyenVienCV.keys.first;
    List<DsChuyenVienVbdi> listCv = [];
    checkBoxChuyenVienCV.forEach((key, value) {
      if (value) {
        listCv.add(
            listChuyenVien.firstWhere((element) => element.maCtcbKc == key));
      }
    });
    await kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      vbdiProvider
          .auVbdiLdCVbdCcv(
              _store.read(GetStorageKey.maVanBanDiKc).toInt(),
              maCtcbKc,
              maCtcbNhan,
              inputYKienCv.text,
              _store.read(GetStorageKey.fileVbdi),
              _store.read(GetStorageKey.maXuLyDiKc).toInt(),
              smsCv,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "")
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          if (smsCv == 1) {
            var arrSdt = [];
            listCv.forEach((element) {
              arrSdt.add(element.diDongCanBo);
            });
            if (arrSdt.isNotEmpty) {
              String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi);
              String chuoiDiDong = arrSdt.join(",");
              vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                  _store.read(GetStorageKey.maDonViQuanTri));
            }
          }
          DsCvVbdiController dsCvVbdiController = Get.find();
          dsCvVbdiController.loadDsChuaXuLy();
          HomeController homeController = Get.find();
          homeController.loadDSnv();
          inputYKienCvkCv.clear();
          isSmsCvCv.value = false;
          checkBoxChuyenVienCV.clear();
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.DSCVVBDI);
        } else {
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đã có lỗi xảy ra!");
          Get.offNamed(Routers.DSCVVBDI);
        }
      });
    });
  }

  void onCvChuyenVt() {
    checkBoxVanThucCv.isEmpty
        ? CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng chọn chuyên viên!")
        : (checkBoxVanThucCv.isEmpty)
            ? CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Vui lòng chọn văn thư!")
            : Get.defaultDialog(
                title: "Xác nhận",
                middleText: "Bạn muốn chuyển văn thư?",
                textCancel: "Đóng",
                textConfirm: "Xác nhận",
                confirmTextColor: Colors.white,
                onCancel: () {},
                onConfirm: () {
                  confirmCvChuyenVt();
                });
  }

  void confirmCvChuyenVt() async {
    var sms = isSmsVtCv.value ? 1 : 0;
    int maVanBanDi = _store.read(GetStorageKey.maVanBanDiKc).toInt();
    var fileVanBan = _store.read(GetStorageKey.fileVbdi);
    var maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    int maXuLyDiCha = _store.read(GetStorageKey.maXuLyDiKc).toInt();
    var arrayListVt = [];
    var maCtcbVtArr = [];
    var maCtcbDuyet = CheckBoxLanhDaoCv.keys.first!;
    checkBoxVanThu.forEach((key, value) {
      if (value) {
        arrayListVt.add(
            ListVanThu.firstWhere((element) => element.maCtcbKc == key)
                .toJson());
        maCtcbVtArr.add(key);
      }
    });
    String strArrayVt = arrayListVt.join(';');
    String strArrayCtcbVt = maCtcbVtArr.join(';');
    kysiProvider
        .auKysoGFDK(_store.read(GetStorageKey.maVanBanDiKc).toInt(), maCtcbKc)
        .then((value) {
      //
      vbdiProvider
          .auVbdiCvChuyenVt(
              maVanBanDi,
              maCtcbKc,
              strArrayCtcbVt,
              inputYKienVtCv.text,
              value.data!.isNotEmpty ? value.data![0].fileKySinhRa! : "",
              fileVanBan!,
              maXuLyDiCha,
              sms,
              maCtcbDuyet)
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          if (isSmsVtCv.value) {
            var arrSDT = [];
            arrayListVt.forEach((element) {
              arrSDT.add(element.diDongCanBo);
            });
            if (arrSDT.isNotEmpty) {
              String noiDungChuyen = _store.read(GetStorageKey.trichYeuDi)!;
              String chuoiDiDong = arrSDT.join(",");
              vbdeProvider.SendSMS(noiDungChuyen, chuoiDiDong,
                  _store.read(GetStorageKey.maDonViQuanTri));
            }
          }
          DsCvVbdiController dsCvVbdiController = Get.find();
          dsCvVbdiController.loadDsChuaXuLy();
          HomeController homeController = Get.find();
          homeController.loadDSnv();
          inputYKienVtCv.clear();
          isSmsVtCv.value = false;
          checkBoxVanThucCv.clear();
          CheckBoxLanhDaoCv.clear();
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showSuccessSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Thực hiện thành công!");
          Get.offNamed(Routers.DSCVVBDI);
        } else {
          Get.delete<ChiTietVbdiController>();
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đã có lỗi xảy ra!");
          Get.offNamed(Routers.DSCVVBDI);
        }
      });
    });
  }

  void clearInput() {
    inputYKienVt.clear();
    inputYKienLdk.clear();
    inputYKienCv.clear();
  }

  @override
  void onReady() {
    super.onReady();
    loadDsVanThu();
    loadDsLanhDaoKhac();
    loadDsChuyenVien();
  }

  @override
  void dispose() {
    super.dispose();
    inputYKienVt.dispose();
    inputYKienLdk.dispose();
    inputYKienCv.dispose();
    inputYKienVtCv.dispose();
    inputYKienLdCv.dispose();
    inputYKienCvkCv.dispose();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
