import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/file/upload_file_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_vb_lienquan_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/kyso/kyso_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dsld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/xulyld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';

class ChiTietVbdiController extends GetxController {
  final _getStorage = GetStorage();
  var vbdiProvide = VbdiProvider();
  var fileProvide = FileProvider();
  var kysiProvider = KysoProvider();

  final item = VbdiChiTiet().obs;
  var dsQtxlVbdi = <QtxlVbdi>[].obs;
  // file uploa
  Rx<bool> isLoadData = true.obs;
  Rx<File?> selectedFile = File("").obs;
  Rx<File?> selectedFilePhieuTrinh = File("").obs;

  Rx<String?> selectedFileName = "".obs;
  Rx<String?> selectedFileNamePhieuTrinh = "".obs;
  Rx<String?> base64FileDinhKem = "".obs;
  Rx<String?> base64FilePhieuTrinh = "".obs;
  //Van bản liên quanlate
  Rx<String?> dsVanBanLienQuan = "".obs;
  Rx<String?> chuoiMaVanBanLienQuan = "".obs;

  late int maVanBanDi;
  late int maXuLyVanBanDi;
  late int indexTab;
  late int isChuyenVien;
  late int isLanhDao;
  @override
  void onInit() {
    super.onInit();
    maVanBanDi = Get.arguments["maVanBanDi"];
    maXuLyVanBanDi = Get.arguments["maXuLyVanBanDi"];
    indexTab = Get.arguments["indexTab"] ?? 0;
    isChuyenVien = Get.arguments["isChuyenVien"] ?? 0;
    isLanhDao = Get.arguments["isLanhDao"] ?? 0;
    loadChiTietVbdi();
    loadQuaTrinhXuLy();
  }

  Future<void> loadChiTietVbdi() async {
    isLoadData(true);
    await vbdiProvide.auVbdiChiTiet(maVanBanDi).then((data) {
      if (data.message == "Lấy dữ liệu thành công") {
        item.value = data;

        _getStorage.write(
            GetStorageKey.maVanBanDiKc, item.value.data!.maVanBanDiKc);
        _getStorage.write(GetStorageKey.maXuLyDiKc, item.value.data!.maXuLyDi);
        _getStorage.write(GetStorageKey.fileVbdi, item.value.data!.fileVanBan);
        _getStorage.write(GetStorageKey.trichYeuDi, item.value.data!.trichYeu);
        loadFileVanBanLienQuan();
        isLoadData.value = false;
      }
    });
  }

  void loadQuaTrinhXuLy() async {
    await vbdiProvide.auVbdiQtxl(maVanBanDi).then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        dsQtxlVbdi.clear();
        dsQtxlVbdi.addAll(value.data!);
      }
    });
  }

  void onSendVanThuNhanh() {
    vbdiProvide.auVbdiDsVttDvQt().then(((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        if (value.data!.isNotEmpty) {
          Get.defaultDialog(
              title: "Xác nhận",
              middleText: "Duyệt nhanh văn bản?",
              textCancel: "Đóng",
              textConfirm: "Xác nhận",
              confirmTextColor: Colors.white,
              onCancel: () {},
              onConfirm: () {
                confirmSendVanThuNhanh(value.data![0]);
              });
        }
      } else {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
      }
    }));
  }

  confirmSendVanThuNhanh(DsVanThu vanthu) async {
    var sms = 0;
    var loaiXuLy = 7;
    var fileVanBan = item.value.data!.fileVanBan;
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    var arrayListVt = [];
    var maCtcbVtArr = [];
    arrayListVt.add(vanthu.toJson());
    String strArrayVt = arrayListVt.join(';');
    String strArrayCtcbVt = maCtcbVtArr.join(';');
    kysiProvider
        .auKysoGFDK(item.value.data!.maVanBanDiKc!.toInt(), maCtcbKc)
        .then((fileKs) {
      vbdiProvide
          .auVbdiLDCVT(
              item.value.data!.maVanBanDiKc!.toInt(),
              maCtcbKc,
              strArrayVt,
              "",
              fileVanBan!,
              fileKs.data!.isNotEmpty ? fileKs.data![0].fileKySinhRa! : "",
              dsVanBanLienQuan.value,
              chuoiMaVanBanLienQuan.value,
              strArrayCtcbVt,
              loaiXuLy,
              item.value.data!.maXuLyDi!.toInt(),
              sms)
          .then((resp) {
        if (resp.message == "Thực thi thành công") {
          vbdiProvide
              .auVbdiLDDVBD(maCtcbKc, item.value.data!.maVanBanDiKc!.toInt())
              .then((resp) {
            DuyetLDVbdiController duyetldVbdiController = Get.find();
            duyetldVbdiController.loadChoDuyetVbdi();
            HomeController homeController = Get.find();
            homeController.loadDSnv();
            CustomSnackBar.showSuccessSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Thực hiện thành công!");

            Get.offNamed(Routers.DUYETVBDI);
          });
        }
      });
    });
  }

  // Hoàn tất văn bản đi xử lý đi chuyên viên

  void onSuccessVbdi() {
    Get.defaultDialog(
        title: "Xác nhận",
        middleText: "Hoàn thành văn bản này?",
        textCancel: "Đóng",
        textConfirm: "Xác nhận",
        confirmTextColor: Colors.white,
        onCancel: () {},
        onConfirm: () {
          confirmHoanTatVbdi();
        });
  }

  void confirmHoanTatVbdi() async {
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    await vbdiProvide
        .auVbdiCnDx(maCtcbKc, item.value.data!.maXuLyDi!.toInt())
        .then((value) {
      if (value.message == "Thực thi thành công") {
        vbdiProvide
            .auVbdiHoanTatVBDI(item.value.data!.maXuLyDi!.toInt())
            .then((item) {
          if (item.message == "Thực thi thành công") {
            DsCvVbdiController dsCvVbdiController = Get.find();
            dsCvVbdiController.loadDataByIndexTab(0);
            HomeController homeController = Get.find();
            homeController.loadDSnv();
            CustomSnackBar.showSuccessSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Thực hiện thành công!");

            Get.offNamed(Routers.DSCVVBDI);
          } else {
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Xảy ra lỗi. Vui lòng thực hiện lại sau!");
          }
        });
      }
    });
  }

  // single file
  void pickFile() async {
    final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'png',
          'jpg',
          'odt',
          'xlsx',
          'xls'
        ]);
    if (result == null) return null;
    selectedFile.value = File(result.files.first.path!);
    selectedFileName.value = result.files.first.name;
    var bytesFile = await selectedFile.value!.readAsBytes();
    base64FileDinhKem.value = base64Encode(bytesFile);
  }

  void setStateFile() {
    selectedFile = File("").obs;
    selectedFileName.value = "";
    base64FileDinhKem.value = "";
  }

  void setStateFileDinhKem() {
    selectedFilePhieuTrinh = File("").obs;
    selectedFileNamePhieuTrinh.value = "";
  }
  // pick file phiếu trình

  void pickFilePhieuTrinh() async {
    final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'png',
          'jpg',
          'odt',
          'xlsx',
          'xls'
        ]);
    if (result == null) return null;
    selectedFilePhieuTrinh.value = File(result.files.first.path!);
    selectedFileNamePhieuTrinh.value = result.files.first.name;
  }

  void uploadFilePhieuTrinh() async {
    if (selectedFilePhieuTrinh.value!.path == "") {
      Get.defaultDialog(
        title: "Thông báo",
        middleText: "Bạn chưa chọn tập tin!",
        textCancel: "Ok",
        confirmTextColor: Colors.white,
        onCancel: () {
          Get.back();
        },
      );
    } else {
      var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
      String chucNang = "vanbandi";
      await fileProvide
          .uploadFile(selectedFilePhieuTrinh.value!, chucNang,
              selectedFileNamePhieuTrinh.value!)
          .then((res) {
        List<DetaiUploadFile> respones = res.data!;
        if (respones.isNotEmpty) {
          String pathFilePhieuTring = respones[0].path!;
          late String path;
          if (item.value.data!.fileDinhKem != null) {
            path = item.value.data!.fileDinhKem =
                item.value.data!.fileDinhKem! + ':' + pathFilePhieuTring;
          } else {
            path = pathFilePhieuTring;
          }
          vbdiProvide.auLuuFilePhieuTrinh(maVanBanDi, path).then((res) {
            loadChiTietVbdi();
            setStateFileDinhKem();
            Get.back();
          });
        } else {
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Tải lên thất bại!");
        }
      });
    }
  }

  void upLoadFileDinhKem() async {
    if (selectedFile.value!.path == "") {
      Get.defaultDialog(
        title: "Thông báo",
        middleText: "Bạn chưa chọn tập tin!",
        textCancel: "Ok",
        confirmTextColor: Colors.white,
        onCancel: () {
          Get.back();
        },
      );
    } else {
      var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
      String chucNang = "bo_sung_file_vbdi";
      // gọi hàm upload file
      await fileProvide
          .auUploadFileCntttlB64(maCtcbKc, chucNang, selectedFileName.value!,
              base64FileDinhKem.value!, maVanBanDi)
          .then((res) {
        Get.back();
        switch (res) {
          case "0":
            setStateFile();
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Tải lên thất bại!");
            break;
          case null:
            setStateFile();
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Tải lên thất bại!");
            break;
          case "2":
            setStateFile();
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Tệp tin quá lớn, chỉ tối đa 20MB!");
            break;
          case "3":
            setStateFile();
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Định dạng tệp tin không hợp lệ!");
            break;
          default:
            CustomSnackBar.showSuccessSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Tải lên tệp tin thành công!");
        }
        loadChiTietVbdi();
      });
    }
  }

  // thực hiện xoá file
  void confirmDeleteFile(String pathFile, String fileName) {
    Get.defaultDialog(
        title: "Xác nhận",
        middleText:
            "Bạn chắc muốn xoá tập tin: $fileName ? \r\nChú ý: Không thể phục hồi tập tin đã xoá!",
        textCancel: "Không",
        textConfirm: "Có!",
        confirmTextColor: Colors.white,
        onCancel: () {
          if (Get.isDialogOpen ?? false) {
            Get.toNamed(Get.currentRoute);
          }
        },
        onConfirm: () async {
          var arrayFile = item.value.data!.fileVanBan!.split(":");
          var arrayFileThen = arrayFile.where((element) => element != pathFile);
          String chuoiXoa = pathFile;
          String chuoiConLai = arrayFileThen.join(":");
          String trichYeu = item.value.data!.trichYeu!;
          int maVanBanDi = item.value.data!.maVanBanDiKc!.toInt();
          var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
          String hoVaTenCanBoXoa = _getStorage.read(GetStorageKey.hoVaTen);
          await fileProvide
              .auFileCNLF(chuoiXoa, chuoiConLai, maVanBanDi, maCtcbKc, trichYeu,
                  hoVaTenCanBoXoa)
              .then((res) {
            loadChiTietVbdi();
            Get.back();
            if (res.message == "Thực thi thành công") {
              CustomSnackBar.showSuccessSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Xoá file thành công!");
            } else {
              CustomSnackBar.showErrorSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Lỗi. Vui lòng thử lại sau!");
            }
          });
        });
  }

  // lãnh đạo chuyển xử lý
  void onChuyentVt(VbdiChiTiet item) {
    // xoá để khơir tạo lại
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CHUYENVTVBDI, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  void onChuyenLdk(VbdiChiTiet item) {
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CHUYENLDKVBDI, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  void onChuyenCv(VbdiChiTiet item) {
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CHUYENCVVBDI, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  // chuyên viên chuyển xử lý
  void onCvChuyenCv(VbdiChiTiet item) {
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CVCHUYENCV, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  void onCvChuyenLd(VbdiChiTiet item) {
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CVCHUYENLD, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  void onCvChuyenVt(VbdiChiTiet item) {
    Get.delete<XulyLdVbdiController>();
    Get.toNamed(Routers.CVCHUYENVT, arguments: {
      "DetalsVbdi": item,
      "vbLienQuan": dsVanBanLienQuan.value,
      "maVbLienQuan": chuoiMaVanBanLienQuan.value
    });
  }

  // chuyển page sang ký số

  void convertToPdf(String pathFile, int isLoaiKySo) async {
    if (pathFile.toString().toLowerCase().split(".").last == "doc" ||
        pathFile.toString().toLowerCase().split(".").last == "docx") {
      var oldFile = pathFile;
      await fileProvide.auFileCdTTWP(pathFile).then((resp) {
        if (resp.length > 0) {
          if (resp.toString().split('|')[0] == "1") {
            String newFile =
                oldFile.toString().replaceAll(RegExp(r'.docx'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.doc'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.DOC'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.DOCX'), '.pdf');
            _getStorage.write(GetStorageKey.pathFileKySo, newFile);
            _getStorage.write(GetStorageKey.maCongVan, maVanBanDi);
            _getStorage.write(GetStorageKey.isLoaiKySo, isLoaiKySo);
            _getStorage.write(GetStorageKey.maXuLyCongVan, maXuLyVanBanDi);
            _getStorage.write(
                GetStorageKey.srcFileKySo, item.value.data!.fileVanBan);
            _getStorage.write(GetStorageKey.isVbnbKySo, 0);
            Get.delete<KySoController>();
            Get.toNamed(Routers.KYSOKPI);
          }
        } else {
          _getStorage.write(GetStorageKey.pathFileKySo, oldFile);
          _getStorage.write(GetStorageKey.maCongVan, maVanBanDi);
          _getStorage.write(GetStorageKey.isLoaiKySo, isLoaiKySo);
          _getStorage.write(GetStorageKey.maXuLyCongVan, maXuLyVanBanDi);
          _getStorage.write(
              GetStorageKey.srcFileKySo, item.value.data!.fileVanBan);
          _getStorage.write(GetStorageKey.isVbnbKySo, 0);
          Get.delete<KySoController>();
          Get.toNamed(Routers.KYSOKPI);
        }
      });
    } else {
      _getStorage.write(GetStorageKey.pathFileKySo, pathFile);
      _getStorage.write(GetStorageKey.maCongVan, maVanBanDi);
      _getStorage.write(GetStorageKey.isLoaiKySo, isLoaiKySo);
      _getStorage.write(GetStorageKey.maXuLyCongVan, maXuLyVanBanDi);
      _getStorage.write(GetStorageKey.srcFileKySo, item.value.data!.fileVanBan);
      _getStorage.write(GetStorageKey.isVbnbKySo, 0);
      Get.delete<KySoController>();
      Get.toNamed(Routers.KYSOKPI);
    }
  }

  void onChuyenPageKySo(String? pathFile, int isLoaiKySo) {
    // convert file
    convertToPdf(pathFile!, isLoaiKySo);
  }

  void onChuyenPageKsPhieuTrinh(String? pathFile, int isLoaikySo) {
    _getStorage.write(GetStorageKey.isKsPhieuTrinh, 1);
    _getStorage.write(
        GetStorageKey.filesPhieuTrinh, item.value.data!.fileDinhKem);
    convertToPdf(pathFile!, isLoaikySo);
  }

  void loadFileVanBanLienQuan() async {
    dsVanBanLienQuan.value = "";
    try {
      await vbdiProvide
          .auVbdiVbLienQuan(item.value.data!.maVanBanKc!.toInt())
          .then((value) {
        for (var element in value.data!) {
          if (dsVanBanLienQuan.value!.isNotEmpty) {
            dsVanBanLienQuan.value =
                dsVanBanLienQuan.value! + ':' + element.fileVanBan!;

            chuoiMaVanBanLienQuan.value = chuoiMaVanBanLienQuan.value! +
                ';' +
                element.maVanBanKc!.toInt().toString();
          } else {
            dsVanBanLienQuan.value = element.fileVanBan!;
            chuoiMaVanBanLienQuan.value =
                element.maVanBanKc!.toInt().toString();
          }
        }
      });
    } catch (exception) {}
  }

  void viewFilePdfAfterSign(String pathFile) {
    ModalViewFileOnline.ViewFileOnline(
        tenFile: MethodUntils.getFileChiTietTTDH([pathFile])[0].fileName!,
        item: pathFile,
        path: MethodUntils.getFileChiTietTTDH([pathFile])[0].urlViewFile!
    );
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
