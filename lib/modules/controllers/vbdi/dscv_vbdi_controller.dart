import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_dscv_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DsCvVbdiController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<CvXuLyVbdi>> {
  late TabController tabController;
  var vbdiProvide = VbdiProvider();
  var indexTabGobal = 0.obs;
  final _getStorage = GetStorage();

  ScrollController scrollerControllerChuaXuLy = ScrollController();
  ScrollController scrollerControllerDaXuLy = ScrollController();
  ScrollController scrollerControllerChoPhatHanh = ScrollController();
  ScrollController scrollerControllerDaPhatHanh = ScrollController();

  int page = 1;
  int size = 20;
  int totalPage = 0;

  List<CvXuLyVbdi> listChuaXulyCv = [];
  List<CvXuLyVbdi> listDaXuLyCv = [];
  List<CvXuLyVbdi> listChoPhatHanh = [];
  List<CvXuLyVbdi> listDaPhatHanh = [];
  String? keyWord = "";

  void onChangeTab(int indexTab) {
    // set indexTab
    indexTabGobal.value = indexTab;
    loadDataByIndexTab(indexTab);
  }

  void loadDataByIndexTab(int index) {
    switch (index) {
      case 0:
        loadDsChuaXuLy();
        paginateChoDuyet(index);
        break;
      case 1:
        loadDsDaXuLy();
        paginateChoDuyet(index);
        break;
      case 2:
        loadDsChoPhatHanh();
        paginateChoDuyet(index);
        break;
      case 3:
        loadDsDaPhatHanh();
        paginateChoDuyet(index);
        break;
      default:
    }
  }

  Future<void> loadDsChuaXuLy() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdiProvide
          .auVbdiDsChoXuLyCvVbdi(page, size, keyWord)
          .then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          listChuaXulyCv.clear();
          listChuaXulyCv.addAll(value.data!);
          totalPage = value.totalPage!.toInt();
          change(listChuaXulyCv, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: exception.toString());
    }
  }

  Future<void> loadDsDaXuLy() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdiProvide.auVbdiDsDaXuLyCvVbdi(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          listDaXuLyCv.clear();
          listDaXuLyCv.addAll(value.data!);
          totalPage = value.totalPage!.toInt();
          change(listDaXuLyCv, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: exception.toString());
    }
  }

  Future<void> loadDsChoPhatHanh() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdiProvide
          .auVbdiDsChoPhatHanhCv(page, size, keyWord)
          .then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          listChoPhatHanh.clear();
          listChoPhatHanh.addAll(value.data!);
          totalPage = value.totalPage!.toInt();
          change(listChoPhatHanh, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: exception.toString());
    }
  }

  void setKeySearch(String text) {
    keyWord = text;
    switch (indexTabGobal.value) {
      case 0:
        loadDsChuaXuLy();
        break;
      case 1:
        loadDsDaXuLy();
        break;
      case 2:
        loadDsChoPhatHanh();
        break;
      case 3:
        loadDsDaPhatHanh();
        break;
      default:
    }
  }

  Future<void> loadDsDaPhatHanh() async {
    try {
      page = 1;
      change(null, status: RxStatus.loading());
      await vbdiProvide.auVbdiDsDaPhatHanh(page, size, keyWord).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          listDaPhatHanh.clear();
          listDaPhatHanh.addAll(value.data!);
          totalPage = value.totalPage!.toInt();
          change(listDaPhatHanh, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: exception.toString());
    }
  }

  void paginateChoDuyet(int indexTab) {
    try {
      switch (indexTab) {
        case 0:
          scrollerControllerChuaXuLy.addListener(() {
            if (scrollerControllerChuaXuLy.position.maxScrollExtent ==
                scrollerControllerChuaXuLy.position.pixels) {
              if (page <= totalPage) {
                page++;
                loadMoreDs(indexTab);
              }
            }
          });
          break;
        case 1:
          scrollerControllerDaXuLy.addListener(() {
            if (scrollerControllerDaXuLy.position.maxScrollExtent ==
                scrollerControllerDaXuLy.position.pixels) {
              if (page <= totalPage) {
                page++;
                loadMoreDs(indexTab);
              }
            }
          });
          break;
        case 2:
          scrollerControllerChoPhatHanh.addListener(() {
            if (scrollerControllerChoPhatHanh.position.maxScrollExtent ==
                scrollerControllerChoPhatHanh.position.pixels) {
              if (page <= totalPage) {
                page++;
                loadMoreDs(indexTab);
              }
            }
          });
          break;
        case 3:
          scrollerControllerDaPhatHanh.addListener(() {
            if (scrollerControllerDaPhatHanh.position.maxScrollExtent ==
                scrollerControllerDaPhatHanh.position.pixels) {
              if (page <= totalPage) {
                page++;
                loadMoreDs(indexTab);
              }
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void loadMoreDs(int index) async {
    try {
      Get.dialog(Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            size: 20,
            color: AppColor.blueAccentColor,
          )
        ],
      ));
      switch (index) {
        case 0:
          await vbdiProvide.auVbdiDsChoXuLyCvVbdi(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              listChuaXulyCv.addAll(value.data!);
              change(listChuaXulyCv, status: RxStatus.success());
            }
          }, onError: (error) {
            Get.back();
            CustomSnackBar.showErrorSnackBar(
                context: Get.context,
                title: AppString.error,
                message: error.toString());
          });
          break;
        case 1:
          await vbdiProvide.auVbdiDsDaXuLyCvVbdi(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              listDaXuLyCv.addAll(value.data!);
              change(listDaXuLyCv, status: RxStatus.success());
            }
          }, onError: (error) {
            Get.back();
            CustomSnackBar.showErrorSnackBar(
                context: Get.context,
                title: AppString.error,
                message: error.toString());
          });
          break;
        case 2:
          await vbdiProvide.auVbdiDsChoPhatHanhCv(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              listChoPhatHanh.addAll(value.data!);
              change(listChoPhatHanh, status: RxStatus.success());
            }
          }, onError: (error) {
            Get.back();
            CustomSnackBar.showErrorSnackBar(
                context: Get.context,
                title: AppString.error,
                message: error.toString());
          });
          break;
        case 3:
          await vbdiProvide.auVbdiDsDaPhatHanh(page, size).then((value) {
            Get.back();
            if (value.message == "Lấy dữ liệu thành công") {
              listDaPhatHanh.addAll(value.data!);
              change(listDaPhatHanh, status: RxStatus.success());
            }
          }, onError: (error) {
            Get.back();
            CustomSnackBar.showErrorSnackBar(
                context: Get.context,
                title: AppString.error,
                message: error.toString());
          });
          break;
        default:
      }
    } catch (exception) {
      Get.back();
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: exception.toString());
    }
  }

  void onPressDetaisl(int maVanBanDi, int? maXuLyVanBanDi, int indexTab) {
    Get.delete<ChiTietVbdiController>();
    Get.toNamed(Routers.DETAILSVBDI, arguments: {
      "maVanBanDi": maVanBanDi,
      "indexTab": indexTab,
      "isChuyenVien": 1,
      "maXuLyVanBanDi": maXuLyVanBanDi
    });
  }

  @override
  void onInit() {
    super.onInit();
    var initIndex = Get.arguments != null ? Get.arguments['index'] : 0;
    indexTabGobal.value = initIndex ?? 0;
    tabController = TabController(
        length: 4, initialIndex: indexTabGobal.value, vsync: this);
    onChangeTab(indexTabGobal.value);
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    super.onClose();
  }
}
