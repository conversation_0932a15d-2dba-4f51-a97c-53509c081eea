import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'package:get_storage/get_storage.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:vnpt_ioffice_camau/app/model/kyso/DsMauCks_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/kyso/kyso_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/utils/modal_maucks.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_config.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/chitiet_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';
import 'package:webview_flutter/webview_flutter.dart';

class KySoController extends GetxController {
  var kysoProvider = KysoProvider();
  var fileProvider = FileProvider();
  var vbdiProvider = VbdiProvider();

  var _store = GetStorage();
  late WebViewController webViewController = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted);
  Rx<String?> activeMauChuKy = "".obs;
  Rx<String?> activetenKySo = "".obs;
  Rx<String?> accessTokenSmartCa = "".obs;
  Rx<String?> injectJavaData = "".obs;
  Rx<String?> fileNameKySo = "".obs;
  RxList<DsMauCks> listMauChuKy = <DsMauCks>[].obs;
  Rx<MKySoSim> infoCa = MKySoSim().obs;

  var isHash = 0.obs;
  var urlViewKyso = "".obs;
  var pathFile = "".obs;
  // Get.arguments["pathFile"];
  var maCongVan = 0.obs;
  // Get.arguments["maVanBanDi"];
  var maXuLyCongVan = 0.obs;
  //Get.arguments["maXuLy"];
  var srcFile = "".obs;
  //Get.arguments["srcFile"];
  var isLoaiKySo = "".obs;
  // Get.arguments["isLoaiKySo"];
  var isVBNB = 0.obs;
  //Get.arguments["isVBNB"].toInt();
  // ký số file phiếu trình
  var isKsPhieuTrinh = 0.obs;
  Rx<String?> filesPhieuTrinh = "".obs;
  @override
  void onInit() {
    super.onInit();
    var domainApi = _store.read(GetStorageKey.domainApi);
    var domainFile = _store.read(GetStorageKey.domainFile);
    pathFile.value = _store.read(GetStorageKey.pathFileKySo);

    fileNameKySo.value = MethodUntils.getFileName(pathFile.value);
    urlViewKyso.value = MethodUntils.getViewFilePdfDomain(
        domainApi, domainFile, pathFile.value);
    // var x = _store.read(GetStorageKey.maCongVan);
    maCongVan.value = _store.read(GetStorageKey.maCongVan) ?? 0;
    maXuLyCongVan.value = _store.read(GetStorageKey.maXuLyCongVan) ?? 0;
    srcFile.value = _store.read(GetStorageKey.srcFileKySo).toString();
    isLoaiKySo.value = _store.read(GetStorageKey.isLoaiKySo).toString();
    isVBNB.value = _store.read(GetStorageKey.isVbnbKySo) ?? 0;
    // ký số phiếu trình
    isKsPhieuTrinh.value = _store.read(GetStorageKey.isKsPhieuTrinh) ?? 0;
    filesPhieuTrinh.value = _store.read(GetStorageKey.filesPhieuTrinh) ?? "";
    getListMauChuKy();
  }

  void cleanStore() {
    _store.write(GetStorageKey.maXuLyCongVan, 0);
    _store.write(GetStorageKey.srcFileKySo, "");
    _store.write(GetStorageKey.isLoaiKySo, "");
    _store.write(GetStorageKey.isVbnbKySo, 0);
  }

  void cleanBienPhieuTring() {
    _store.write(GetStorageKey.isKsPhieuTrinh, 0);
    _store.write(GetStorageKey.filesPhieuTrinh, "");
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void dispose() {
    checkTokenSmartCa();
    super.dispose();
  }

  void updateFile(String filePath, [int viewAfterUpdate = 0]) async {
    try {
      var arrFile = srcFile.toString().split(":");
      if (int.parse(
              _store.read(GetStorageKey.cksXoaFileSauKhiKySo).toString()) ==
          1) {
        arrFile =
            arrFile.where((element) => element != pathFile.value).toList();
      }
      if (int.parse(
              _store.read(GetStorageKey.cksXoaFileSauKhiKySo).toString()) ==
          2) {
        var ext = pathFile.toString().toLowerCase().split(".").last;
        if (ext == "pdf") {
          arrFile =
              arrFile.where((element) => element != pathFile.value).toList();
        }
      }
      arrFile.add(filePath);
      var chuoiFileMoi = arrFile.join(":").replaceAll(RegExp(r'null:'), '');
      if (isVBNB == 1) {
        Get.lazyPut(() => ChiTietVbnbController());
        ChiTietVbnbController controllerVbnb = Get.find();
        await kysoProvider
            .auKySoLFKSVbnb(maCongVan.value, maXuLyCongVan.value, chuoiFileMoi)
            .then((value) => {
                  FullScreenDialogLoader.cancleDialog(),
                  webViewController.removeJavaScriptChannel("flutterToWebView"),
                  cleanStore(),
                  Get.delete<KySoController>(),
                  controllerVbnb.loadChiTietVbnbDaNhan(),
                  Get.offNamed(Routers.VBNBDETAILS)
                });
      } else if (isKsPhieuTrinh.value == 1) {
        Get.lazyPut(() => ChiTietVbdiController());
        ChiTietVbdiController controllerDetailsVbdi = Get.find();
        var arrayFilePT = filesPhieuTrinh.value!.split(':');
        late String pathFileTrinh;
        if (arrayFilePT.length > 1) {
          // nếu nhiều file bỏ ghi đề file đã ký
          arrayFilePT = arrayFilePT
              .where((element) => element != pathFile.value)
              .toList();
          arrayFilePT.add(filePath);
          String fileMoiPT =
              arrayFilePT.join(":").replaceAll(RegExp(r'null:'), '');
          pathFileTrinh = fileMoiPT;
        } else {
          // nếu 1 file thì lấy file đã ký luôn
          pathFileTrinh = filePath;
        }

        await vbdiProvider
            .auLuuFilePhieuTrinh(maCongVan.value, pathFileTrinh)
            .then((res) {
          FullScreenDialogLoader.cancleDialog();
          webViewController.removeJavaScriptChannel("flutterToWebView");
          cleanBienPhieuTring();
          Get.offNamed(Routers.DETAILSVBDI);
          controllerDetailsVbdi.loadChiTietVbdi();
          if (viewAfterUpdate == 1) {
            controllerDetailsVbdi.viewFilePdfAfterSign(filePath);
          }
        });
      } else {
        Get.lazyPut(() => ChiTietVbdiController());
        ChiTietVbdiController controllerDetailsVbdi = Get.find();
        await kysoProvider
            .auKySoVtCnFkPh(maCongVan.value, chuoiFileMoi)
            .then((value) {
          kysoProvider
              .auKySoUfSkk(maCongVan.value, _store.read(GetStorageKey.maCtcbKc),
                  filePath)
              .then((value) {
            FullScreenDialogLoader.cancleDialog();
            webViewController.removeJavaScriptChannel("flutterToWebView");
            cleanStore();
            Get.delete<KySoController>();

            Get.offNamed(Routers.DETAILSVBDI);
            controllerDetailsVbdi.loadChiTietVbdi();

            if (viewAfterUpdate == 1) {
              controllerDetailsVbdi.viewFilePdfAfterSign(filePath);
            }
          });
        });
      }
    } catch (exception) {
      cleanStore();
      cleanBienPhieuTring();
      FullScreenDialogLoader.cancleDialog();
      Get.delete<KySoController>();
      Get.offNamed(Routers.DETAILSVBDI);
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Có lỗi xảy ra! Vui lòng thử lại sau");
    }
  }

  void getListMauChuKy() async {
    await kysoProvider.auDsCKS().then((res) => {
          if (res.data!.isNotEmpty)
            {
              activeMauChuKy.value = res.data![0].linkCks,
              activetenKySo.value = res.data![0].tenKySo,
              res.data!.forEach((element) {
                listMauChuKy.value.add(element);
                if (element.trangThai == 1) {
                  activeMauChuKy.value = element.linkCks;
                  activetenKySo.value = element.tenKySo;
                }
              })
            }
        });
  }

  void signedKySoSim(String fileHinhAnh) async {
    try {
      fileNameKySo.value = MethodUntils.getFileName(pathFile.value);
      infoCa.value.fileHinhAnh = fileHinhAnh;
      infoCa.value.maCtcb = _store.read(GetStorageKey.maCtcbKc);
      infoCa.value.domainApi = _store.read(GetStorageKey.domainApi);
      infoCa.value.domainFile = _store.read(GetStorageKey.domainFile);
      infoCa.value.file = pathFile.value;
      infoCa.value.fileName = fileNameKySo.value;
      infoCa.value.maDonVi = _store.read(GetStorageKey.maDonVi);
      infoCa.value.soDienThoai = _store.read(GetStorageKey.diDongCanBo);
      infoCa.value.msspProvider = _store.read(GetStorageKey.loaiKySoSim);
      infoCa.value.chucNang = "vanbandi";
      infoCa.value.maCongVan = maCongVan.toInt();
      await kysoProvider.auKySoKSS(infoCa.value).then((response) {
        if (response is Map<String, dynamic>) {
          if (response['code'] != -1) {
            updateFile(response['data']);
            FullScreenDialogLoader.cancleDialog();
          } else {
            FullScreenDialogLoader.cancleDialog();
            CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Đã huỷ quá trình ký số!");
          }
        }
      });
    } catch (exception) {
      FullScreenDialogLoader.cancleDialog();
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Hết thời gian xử lý!");
    }
  }

  void signedKySoSimAuto(String tenChucDanh, String fileHinhAnh) async {
    try {
      if (_store.read(GetStorageKey.usMobilePkiSide) == "TOP") {
        tenChucDanh = _store.read(GetStorageKey.hoVaTen);
      }

      var mKySoSim = MKySoSim();
      var fileNameKySo = MethodUntils.getFileName(pathFile.value);
      mKySoSim.fileHinhAnh = fileHinhAnh;
      mKySoSim.maCtcb = _store.read(GetStorageKey.maCtcbKc);
      mKySoSim.domainApi = _store.read(GetStorageKey.domainApi);
      mKySoSim.domainFile = _store.read(GetStorageKey.domainFile);
      mKySoSim.file = pathFile.value;
      mKySoSim.fileName = fileNameKySo;
      mKySoSim.maDonVi = _store.read(GetStorageKey.maDonVi);
      mKySoSim.soDienThoai = _store.read(GetStorageKey.diDongCanBo);
      mKySoSim.msspProvider = _store.read(GetStorageKey.loaiKySoSim);
      mKySoSim.chucNang = "vanbandi";
      mKySoSim.tenChucDanh = tenChucDanh;
      mKySoSim.maCongVan = maCongVan.toInt();
      await kysoProvider.auKySoKSS(infoCa.value).then((response) {
        if (response.data.data != "-1") {
          updateFile(response.data.data);
          FullScreenDialogLoader.cancleDialog();
        } else {
          FullScreenDialogLoader.cancleDialog();
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Hết thời gian xử lý!");
        }
      });
    } catch (exception) {
      FullScreenDialogLoader.cancleDialog();
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Có lỗi xảy ra, vui lòng thử lại sau!");
    }
  }

  void showListMauCks(int isLoaiKySo) {
    List<Widget> arrItemIamge = [];
    var domain = _store.read(GetStorageKey.domainApi);
    if (listMauChuKy.isNotEmpty && listMauChuKy.length > 1) {
      listMauChuKy.value.forEach((element) {
        var item = Column(
          children: [
            GestureDetector(
              onTap: () {
                FullScreenDialogLoader.showDialog();
                isLoaiKySo == 0
                    ? signedKySoSim(element.linkCks.toString())
                    : signedKySoSimAuto(
                        element.tenKySo.toString(), element.linkCks.toString());
              },
              child: ListTile(
                leading: Text(element.tenKySo.toString()),
                title: Image.network(
                  '$domain/api/file-manage/read-advance?path=${element.linkCks}',
                  height: 90,
                ),
              ),
            ),
            const Divider(
              height: 0.5,
              color: Colors.black,
            )
          ],
        );
        arrItemIamge.add(item);
      });
      CustomModalCks.modalCks(context: Get.context, listItem: arrItemIamge);
    } else {
      FullScreenDialogLoader.showDialog();
      if (isLoaiKySo == 0) {
        signedKySoSim(activeMauChuKy.value.toString());
      } else {
        signedKySoSimAuto(
            activetenKySo.value!, activeMauChuKy.value.toString());
      }
    }
  }

  List<Widget> showListMauCksSmartCA() {
    List<Widget> arrItemIamge = [];
    var domain = _store.read(GetStorageKey.domainApi);
    if (listMauChuKy.isNotEmpty) {
      listMauChuKy.value.forEach((element) {
        var item = Column(
          children: [
            GestureDetector(
              onTap: () {
                onSmartCaLocation(infoCa.value, element.linkCks.toString());
              },
              child: ListTile(
                leading: Text(element.tenKySo.toString()),
                title: Image.network(
                  '$domain/api/file-manage/read-advance?path=${element.linkCks}',
                  height: 90,
                ),
              ),
            ),
            const Divider(
              height: 0.5,
              color: Colors.black,
            )
          ],
        );
        arrItemIamge.add(item);
      });
    }
    return arrItemIamge;
  }

  void showWebViewSmartCa() {
    accessTokenSmartCa.value =
        _store.read(GetStorageKey.smartCaAccessToken) ?? "";

    var smartCaRedirctUrl = _store.read(GetStorageKey.cksSmartCaCallback);
    late WebViewController controllerWebViewCa = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(smartCaRedirctUrl)) {
              completeLoginSmartCA();
              // chop phép truy cập
              return NavigationDecision.navigate;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(getSmartCAAuthUrl()));

    var widgetCustomSmartCa = WebViewWidget(controller: controllerWebViewCa);

    showModalBottomSheet(
      backgroundColor: Colors.grey[200],
      context: Get.context!,
      isScrollControlled: true,
      useRootNavigator: true,
      builder: (BuildContext context) {
        Size screenSize = MediaQuery.of(context).size;
        double screenHeight = screenSize.height - 200;
        return FractionallySizedBox(
          heightFactor: 0.95,
          child: Container(
            decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10))),
            margin: const EdgeInsets.all(10),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Text("Ký số",
                        style:
                            Theme.of(context).textTheme.titleMedium!.copyWith(
                                  color: AppColor.blackColor,
                                )),
                  ),
                  Obx(() => SingleChildScrollView(
                        child: Container(
                          height: screenHeight,
                          child: accessTokenSmartCa.value!.isEmpty
                              ? widgetCustomSmartCa
                              : Column(children: showListMauCksSmartCA()),
                        ),
                      )),
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          OutlinedButton.icon(
                              style: ButtonStyle(
                                side: MaterialStateProperty.all(
                                    const BorderSide(color: Colors.red)),
                                shape: MaterialStateProperty.all(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(30.0))),
                              ),
                              onPressed: () {
                                Get.back();
                              },
                              icon: const Icon(Icons.close, color: Colors.red),
                              label: const Text(
                                "Đóng",
                                style: TextStyle(color: Colors.red),
                              ))
                        ]),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void checkTokenSmartCa() {
    DateTime toDate = DateTime.now();
    if (_store.read(GetStorageKey.smartCaExpire) >
        toDate.millisecondsSinceEpoch) {
      //  print("LOINB: hết hạn token");
      accessTokenSmartCa.value = _store.read(GetStorageKey.smartCaAccessToken);
    } else {
      //print("LOINB: còn hạn token");
      accessTokenSmartCa.value = "";
      _store.write(GetStorageKey.smartCaAccessToken, null);
    }
  }

  Future<void> postMessage(String messsage) async {
    injectJavaData.value = '';
    switch (messsage) {
      case "pressAdd":
        injectJavaData.value = 'window.pressAdd(); true;';
        isHash.value = 1;
        break;
      case "pressDelete":
        injectJavaData.value = 'window.pressDelete(); true;';
        isHash.value = 0;
        break;
      case "pressSign":
        injectJavaData.value = 'window.pressSign(); true;';
        (isHash.value == 0)
            ? CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Vui lòng chọn vị trí")
            : null;
        break;
      default:
    }

    injectJavaData.value = injectJavaData.value!;
    await webViewController.runJavaScript(injectJavaData.value!);
    injectJavaData.value = '';
  }

  void onPressKySo() async {
    String javaExcecute =
        "setInterval(function(){let data = getPdfData(); window.flutterToWebView.postMessage(data)},500);";
    await webViewController.runJavaScript(javaExcecute);
  }

  void onPressActionKs(String message) async {
    onPressKySo();
    await onPressKySoSim().then((value) {
      postMessage(message);
    });
  }

  void onMessageChannelKs(JavaScriptMessage message) {
    if (message.message.isNotEmpty) {
      var arrayData = json.decode(message.message);
      for (int i = 0; i < arrayData.length; i++) {
        if (arrayData[i] is Map<String, dynamic>) {
          if (arrayData[i]['objects'].length > 0) {
            if (arrayData[i]['objects'].length > 0) {
              var objRec = arrayData[i]['objects'][0];
              int page = i + 1;
              int scale = 1;
              if (objRec is Map<String, dynamic>) {
                var backgroundImage = arrayData[i]['backgroundImage'];
                var pageWith = backgroundImage['width'] / scale;
                var pageHeight = backgroundImage['height'] / scale;
                var objectWidth = (objRec['width'] * objRec['scaleX']) / scale;
                var objectHeight =
                    (objRec['height'] * objRec['scaleY']) / scale;
                var objectTop = objRec['top'] / scale;
                var objectLeft = objRec['left'] / scale;
                infoCa.value.llx = objectLeft.toDouble();
                infoCa.value.lly =
                    (pageHeight - (objectTop + objectHeight)).toDouble();
                infoCa.value.urx = (infoCa.value.llx! + objectWidth).toDouble();
                infoCa.value.ury =
                    (infoCa.value.lly! + objectHeight).toDouble();
                infoCa.value.trangKy = page.toString();
                if (int.parse(isLoaiKySo.value) == 0) {
                  showListMauCks(0); // ký số tự dộng
                } else {
                  showWebViewSmartCa();
                }
              }
            }
          }
        }
      }
    }
  }

  Future<void> onPressKySoSim() async {
    var domainFile = _store.read(GetStorageKey.domainFile);
    injectJavaData.value = injectJavaData.value! +
        "setWidthHeightRec(${AppConfig.defaultUsMobilePkiWidthRec},${AppConfig.defaultUsMobilePkiHeightRec});";
    if (activeMauChuKy.value!.isNotEmpty) {
      injectJavaData.value = injectJavaData.value! +
          "setUrlImage('${MethodUntils.getUrlFile(activeMauChuKy.value!, domainFile)}');";
    }
    await webViewController.runJavaScript(injectJavaData.value!);
  }

  // Ký số smartCA

  String getSmartCAAuthUrl() {
    var domain = _store.read(GetStorageKey.cksSmartCaUrl);
    var smartCAclientId = _store.read(GetStorageKey.cksSmartCaClientId);
    var smartCaCallBack = _store.read(GetStorageKey.cksSmartCaCallback);
    int maCtcbKc = _store.read(GetStorageKey.maCtcbKc);
    return "$domain/auth/authorize?response_type=code&client_id=$smartCAclientId&redirect_uri=$smartCaCallBack&scope=sign offline_access&state=$maCtcbKc";
  }

  void onSmartCaLocationGetFile(String key, int count) async {
    await Future.delayed(const Duration(milliseconds: 1500));
    if (count > 20) {
      return null;
    } else {
      kysoProvider.auKySoSCGSF(key).then((value) {
        var domainApi = _store.read(GetStorageKey.domainApi);
        var fileSaukySo = MethodUntils.getViewFileDomain(domainApi) +
            MethodUntils.getUrlFile(value.data![0].pathFile!, domainApi);
        updateFile(value.data![0].pathFile!);
      });
    }
  }

  Future<dynamic> onSmartCaLocationReturnFile(String key, int count) async {
    await Future.delayed(const Duration(milliseconds: 1500));

    if (count > 20) {
      return {
        "code": -1,
        "message": "Không thể lấy file sau ký số, quá thời gian chờ!"
      };
    }

    try {
      final value = await kysoProvider.auKySoSCGSF(key);

      if (value.data != null && value.data!.isNotEmpty) {
        var domainApi = _store.read(GetStorageKey.domainApi);
        var fileSaukySo = MethodUntils.getViewFileDomain(domainApi) +
            MethodUntils.getUrlFile(value.data![0].pathFile!, domainApi);
        updateFile(value.data![0].pathFile!, 1);

        return {
          "code": 0,
          "message": "Lấy file sau ký số thành công",
          "data": fileSaukySo,
        };
      } else {
        return {"code": -1, "message": "Không có file sau ký số trả về"};
      }
    } catch (e) {
      return {
        "code": -1,
        "message": "Có lỗi xảy ra khi lấy file sau ký số: $e"
      };
    }
  }

  void onSmartCaLocation(MKySoSim infoCa, String fileHinhAnh) async {
    var mKySoSmartCa = new mKySoSmartCA();
    String newFile = pathFile.toString();
    if (newFile.toLowerCase().split(".").last == "doc" ||
        newFile.toLowerCase().split(".").last == "docx") {
      newFile = newFile.replaceAll(".docx", ",.pdf");
      newFile = newFile.replaceAll(".doc", ",.pdf");
      newFile = newFile.replaceAll(".DOC", ",.pdf");
      newFile = newFile.replaceAll(".DOCX", ",.pdf");
    }
    FullScreenDialogLoader.showDialog();
    try {
      await kysoProvider.auKySoGBFP(newFile).then((dataBase64) {
        if (dataBase64 != "" || dataBase64 != null) {
          kysoProvider.auKySoGBFP(fileHinhAnh).then((resultImage) {
            var Signatures = [];
            var obj = new mSignaTures();
            obj.rectangle =
                '${infoCa.llx!.round()},${infoCa.lly!.round()},${infoCa.urx!.round()},${infoCa.ury!.round()}';
            obj.page = int.parse(infoCa.trangKy!);
            Signatures.add(obj.toJson());
            if (dataBase64 is Map<String, dynamic>) {
              mKySoSmartCa.DataBase64 = dataBase64['base64'];
            }
            if (resultImage is Map<String, dynamic>) {
              mKySoSmartCa.Image = resultImage['base64'];
            }

            mKySoSmartCa.Signatures =
                base64.encode(utf8.encode(jsonEncode(Signatures)));
            if (_store.read(GetStorageKey.usMobilePkiMode) ==
                    "NAME_AND_DESCRIPTION" ||
                _store.read(GetStorageKey.usMobilePkiMode) == "DESCRIPTION") {
              mKySoSmartCa.VisibleType = 1;
            }
            if (_store.read(GetStorageKey.usMobilePkiMode) ==
                "GRAPHIC_AND_DESCRIPTION") {
              mKySoSmartCa.VisibleType = 2;
            }
            if (_store.read(GetStorageKey.usMobilePkiMode) == "GRAPHIC") {
              mKySoSmartCa.VisibleType = 3;
            }
            String key =
                (_store.read(GetStorageKey.maCtcbKc).toInt().toString() +
                        MethodUntils.getNowTime().toString()) ??
                    "";
            mKySoSmartCa.maCtCb = _store.read(GetStorageKey.maCtcbKc).toInt();
            mKySoSmartCa.accessToken = accessTokenSmartCa.value;
            mKySoSmartCa.filePath = newFile;

            kysoProvider
                .auKySoSCSHios(mKySoSmartCa, key)
                .then((dataAuKySoSCSHios) {
              FullScreenDialogLoader.cancleDialog();
              if (dataAuKySoSCSHios is Map<String, dynamic>) {
                if (dataAuKySoSCSHios['code'] != 0) {
                  FullScreenDialogLoader.cancleDialog();
                  CustomSnackBar.showWarningSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Ký số thất bại: ${dataAuKySoSCSHios['data']}",
                  );
                  return;
                }
              } else {
                FullScreenDialogLoader.cancleDialog();
                CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Có lỗi xảy ra khi ký số.",
                );
                return;
              }
              onSmartCaLocationReturnFile(key, 0).then((dataReturn) {
                if (dataReturn['code'] == 0) {
                  CustomSnackBar.showSuccessSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: "Ký số thành công!",
                  );
                } else {
                  CustomSnackBar.showWarningSnackBar(
                    context: Get.context,
                    title: "Thông báo",
                    message: dataReturn['message'],
                  );
                  return;
                }
              }).catchError((e) {
                FullScreenDialogLoader.cancleDialog();
                CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Có lỗi xảy ra khi lấy file sau ký số: $e",
                );
              });
            }).catchError((e) {
              FullScreenDialogLoader.cancleDialog();
              CustomSnackBar.showWarningSnackBar(
                context: Get.context,
                title: "Thông báo",
                message: "Có lỗi xảy ra khi ký số: $e",
              );
            });
          });
        }
      });
    } catch (exception) {
      FullScreenDialogLoader.cancleDialog();
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Có lỗi xảy ra vui lòng thử lại: $exception");
    }
  }

  void completeLoginSmartCA() async {
    await Future.delayed(const Duration(milliseconds: 1600));
    kysoProvider.auKySoSmartCaGATD().then((response) {
      if (response.message == "Lấy dữ liệu thành công") {
        var accessToken = response.data![0].smartcaAccessToken;
        Map<String, dynamic> decodedToken = JwtDecoder.decode(accessToken!);
        int exp = decodedToken['exp'];
        DateTime now = DateTime.now();
        Duration timeZoneOffset =
            Duration(minutes: now.timeZoneOffset.inMinutes);
        DateTime localTime = DateTime.fromMillisecondsSinceEpoch(
            exp * 1000 + timeZoneOffset.inMilliseconds);
        var timeSmartCa = localTime.millisecondsSinceEpoch;
        if (timeSmartCa > now.millisecondsSinceEpoch) {
          _store.write(GetStorageKey.smartCaAccessToken, accessToken);
          accessTokenSmartCa.value = accessToken;
          _store.write(GetStorageKey.smartCaExpire,
              now.microsecondsSinceEpoch + 3600000);
        } else {
          completeLoginSmartCA();
        }
      } else {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Vui lòng kiểm tra lại thông tin!");
      }
    });
  }
}
