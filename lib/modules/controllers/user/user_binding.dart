import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/danh_ba_controlller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/user_controller.dart';

class UserBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => UserController());
  }
}

class DanhBaBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DanhBaControlller());
  }
}
