import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vnpt_ioffice_camau/app/model/user/danh_ba_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/user/user_provider.dart';

class DanhBaControlller extends GetxController with StateMixin<List<LiAttr>> {
  var userProvider = UserProvider();
  TextEditingController searchDanhBa = TextEditingController();
  List<DetailDanhBa> listDanhBa = <DetailDanhBa>[];
  List<LiAttr> listCanBa = <LiAttr>[];

  void loadDanhBa() async {
    change(null, status: RxStatus.empty());
    try {
      await userProvider.getDanhDaTheoDonVi().then((user) {
        if (user.data!.isNotEmpty) {
          listDanhBa.addAll(user.data!);
          for (var element in listDanhBa) {
            if (element.id!.contains('CB')) {
              listCanBa.add(element.liAttr!);
            }
          }
          change(listCanBa, status: RxStatus.success());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.error(exception.toString()));
    }
  }

  void searchCanBo() {
    if (searchDanhBa.text.isEmpty) {
      change(listCanBa, status: RxStatus.success());
    } else {
      var resultSearch = listCanBa
          .where((item) => item.dataTenCanBo!
              .toLowerCase()
              .contains(searchDanhBa.text.toLowerCase()))
          .toList();
      change(resultSearch, status: RxStatus.success());
    }
  }

  void makePhoneCall(String url) async {
    if (await canLaunch(url)) {
      final call = Uri.parse(url);
      await launchUrl(call);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    print(123);
    loadDanhBa();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    searchDanhBa.dispose();
  }
}
