import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/chitiet_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/xuly_vbnb_controller.dart';

class VbnbBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => VanBanNoiBoController());
  }
}

class ChiTietVbnbBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ChiTietVbnbController());
  }
}

class XuLyVbnbBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => XuLyVbnbController());
  }
}
