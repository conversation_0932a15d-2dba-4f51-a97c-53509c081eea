import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_danhsach_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbnb/vbnb_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/empty_list.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/chitiet_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class VanBanNoiBoController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<DanhSachVBNB>> {
  var _store = GetStorage();
  late TabController tabController;
  var selectedVanBanNoiBo = 0.obs;
  int page = 1;
  int size = 10;
  int totalPage = 0;
  var indexTabGobal = 0.obs;
  var vbnbProvider = VbnbProvider();
  // danh sách đã nhận
  List<DanhSachVBNB> danhSachVbNbDaNhan = [];
  List<DanhSachVBNB> searchTrichYeu = [];
  List<DanhSachVBNB> searchSoHieu = [];
  // danh sách đã gửi
  List<DanhSachVBNB> danhsachVbnbDaGui = [];
  List<DanhSachVBNB> searchTrichYeuDaGui = [];
  List<DanhSachVBNB> searchSoHieuDaGui = [];
  var keyWord = "".obs;
  var ketWordDaGui = "".obs;

  ScrollController scrollerControllerDsVbnbDaNhan = ScrollController();
  ScrollController scrollerControllerDsVbnbDaGui = ScrollController();

  void setKeySearch(String? newKeyWord) {
    keyWord.value = newKeyWord!;
    loadDanhSachVbnbDaNhan();
  }

  void setKeySearchDaGui(String? newKeyWord) {
    ketWordDaGui.value = newKeyWord!;
    loadDanhSachVbnbDaGui();
  }

  void changeSelectedVbnb(int number) {
    selectedVanBanNoiBo.value = number;
    loadDanhSachVbnbDaNhan();
  }

  void changeIndexTab(int index) {
    indexTabGobal.value = index;
    loadDanhSachByIndexTab(index);
  }

  void loadDanhSachByIndexTab(int indexTab) {
    switch (indexTab) {
      case 0:
        loadDanhSachVbnbDaNhan();
        paginateVanBanNoiBo(0);
        break;
      case 1:
        loadDanhSachVbnbDaGui();
        paginateVanBanNoiBo(1);
        break;
      default:
    }
  }

  Future<void> loadDanhSachVbnbDaGui() async {
    page = 1;
    change(null, status: RxStatus.loading());
    try {
      if (ketWordDaGui.value.isNotEmpty) {
        await vbnbProvider
            .auVbnbDaGuiDaPhatHanh(ketWordDaGui.value, "", page, size)
            .then((value) {
          searchTrichYeuDaGui.clear();
          searchTrichYeuDaGui.addAll(value.data!);
          vbnbProvider
              .auVbnbDaGuiDaPhatHanh("", ketWordDaGui.value, page, size)
              .then((resSoHieu) {
            searchSoHieuDaGui.clear();
            searchSoHieuDaGui.addAll(resSoHieu.data!);
            Set<DanhSachVBNB> set_temp2 =
                Set<DanhSachVBNB>.from(searchTrichYeuDaGui);
            for (DanhSachVBNB element in searchTrichYeuDaGui) {
              if (!set_temp2.contains(element)) {
                searchSoHieuDaGui.add(element);
              }
            }
            if (searchSoHieuDaGui.isNotEmpty) {
              danhsachVbnbDaGui.clear();
              danhsachVbnbDaGui.addAll(searchSoHieu);
              change(searchSoHieuDaGui, status: RxStatus.success());
            } else {
              change(null, status: RxStatus.empty());
            }
          });
        });
      } else {
        await vbnbProvider
            .auVbnbDaGuiDaPhatHanh("", "", page, size)
            .then((value) {
          if (value.data!.isNotEmpty) {
            totalPage = value.totalPage!;
            danhsachVbnbDaGui.clear();
            danhsachVbnbDaGui.addAll(value.data!);
            change(danhsachVbnbDaGui, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
          }
        });
      }
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  Future<void> loadDanhSachVbnbDaNhan() async {
    page = 1;
    change(null, status: RxStatus.loading());
    try {
      if (keyWord.value.isNotEmpty) {
        await vbnbProvider
            .auVbnbDanhSachDaNhan(
                keyWord.value, "", page, size, selectedVanBanNoiBo.value)
            .then((value) {
          searchTrichYeu.clear();
          searchTrichYeu.addAll(value.data!);
          vbnbProvider
              .auVbnbDanhSachDaNhan(
                  keyWord.value, "", page, size, selectedVanBanNoiBo.value)
              .then((resSoHieu) {
            searchSoHieu.clear();
            searchSoHieu.addAll(resSoHieu.data!);
            Set<DanhSachVBNB> set_temp2 =
                Set<DanhSachVBNB>.from(searchTrichYeu);
            for (DanhSachVBNB element in searchTrichYeu) {
              if (!set_temp2.contains(element)) {
                searchSoHieu.add(element);
              }
            }
            if (searchSoHieu.isNotEmpty) {
              danhSachVbNbDaNhan.clear();
              danhSachVbNbDaNhan.addAll(searchSoHieu);
              change(searchSoHieu, status: RxStatus.success());
            } else {
              change(null, status: RxStatus.empty());
            }
          });
        });
      } else {
        await vbnbProvider
            .auVbnbDanhSachDaNhan("", "", page, size, selectedVanBanNoiBo.value)
            .then((value) {
          totalPage = value.totalPage!;
          danhSachVbNbDaNhan.clear();
          danhSachVbNbDaNhan.addAll(value.data!);
          if (danhSachVbNbDaNhan.isNotEmpty) {
            change(danhSachVbNbDaNhan, status: RxStatus.success());
          } else {
            change(null, status: RxStatus.empty());
          }
        });
      }
      // tìm kiếm trích yếu
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  void paginateVanBanNoiBo(int indexTab) {
    switch (indexTab) {
      case 0:
        scrollerControllerDsVbnbDaNhan.addListener(() {
          if (scrollerControllerDsVbnbDaNhan.position.maxScrollExtent ==
              scrollerControllerDsVbnbDaNhan.position.pixels) {
            if (page <= totalPage) {
              page++;
              loadMoreDs(indexTab);
            }
          }
        });
        break;
      case 1:
        scrollerControllerDsVbnbDaGui.addListener(() {
          if (scrollerControllerDsVbnbDaGui.position.maxScrollExtent ==
              scrollerControllerDsVbnbDaGui.position.pixels) {
            if (page <= totalPage) {
              page++;
              loadMoreDs(indexTab);
            }
          }
        });
        break;
      default:
    }
  }

  void loadMoreDs(int indexTab) async {
    try {
      Get.dialog(Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            size: 20,
            color: AppColor.blueAccentColor,
          )
        ],
      ));
      switch (indexTab) {
        case 0:
          await vbnbProvider
              .auVbnbDanhSachDaNhan(
                  "", "", page, size, selectedVanBanNoiBo.value)
              .then((value) {
            Get.back();
            danhSachVbNbDaNhan.addAll(value.data!);
            change(danhSachVbNbDaNhan, status: RxStatus.success());
          });
          break;
        case 1:
          await vbnbProvider
              .auVbnbDaGuiDaPhatHanh("", "", page, size)
              .then((value) {
            Get.back();
            if (value.data!.isNotEmpty) {
              danhsachVbnbDaGui.addAll(value.data!);
              change(danhsachVbnbDaGui, status: RxStatus.success());
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void onPressPageDetail(DanhSachVBNB item) {
    Get.delete<ChiTietVbnbController>();
    Get.toNamed(Routers.VBNBDETAILS, arguments: {
      "maVanBanNoiBoGuiKc": item.maVanBanNoiBoGuiKc,
      "maVbnbKc": item.maVbnbKc,
      "indexTab": indexTabGobal.value
    });
  }

  @override
  void onInit() {
    loadDanhSachByIndexTab(0);
    tabController = TabController(length: 2, initialIndex: 0, vsync: this);
  }

  @override
  void onReady() {
    loadDanhSachByIndexTab(0);
    update();
  }
}
