import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_chititet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_qtxl_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/kyso/kyso_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbnb/vbnb_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/xuly_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ChiTietVbnbController extends GetxController {
  final _getStorage = GetStorage();

  var fileProvide = FileProvider();
  var kysiProvider = KysoProvider();
  var vbnbProvider = VbnbProvider();
  late int maVbnbGui;
  late int maVbnbKc;
  late int indexTab;
  final item = DetaisVbnb().obs;
  var listQtxlVbnb = <QtxlVanBanNoiBo>[].obs;
  // file uploap
  Rx<bool> isLoadData = false.obs;
  Rx<File?> selectedFile = File("").obs;
  Rx<String?> selectedFileName = "".obs;
  Rx<String?> base64FileDinhKem = "".obs;

  @override
  void onInit() {
    super.onInit();
    maVbnbGui = Get.arguments["maVanBanNoiBoGuiKc"].toInt() ?? 0;
    maVbnbKc = Get.arguments["maVbnbKc"].toInt() ?? 0;
    indexTab = Get.arguments["indexTab"] ?? 0;
    loadChiTietVbnbDaNhan();
    upDateViewCv();
    loadQtxlVanBanNoiBo();
    update();
  }

  void loadChiTietVbnbDaNhan() async {
    if (indexTab == 0) {
      await vbnbProvider.auVbnbChiTietDaNhan(maVbnbGui).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          item.value = value.data![0];
          isLoadData.value = true;
        }
      });
    } else {
      await vbnbProvider.auVbnbChiTietDaGui(maVbnbGui).then((value) {
        if (value.message == "Lấy dữ liệu thành công") {
          item.value = value.data![0];
          isLoadData.value = true;
        }
      });
    }
  }

  // load quy trình xử lý văn bản nội bộ
  void loadQtxlVanBanNoiBo() async {
    await vbnbProvider.auVbnbQtxlVanBanNoiBo(maVbnbKc).then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        listQtxlVbnb.clear();
        listQtxlVbnb.addAll(value.data!);
      }
    });
  }

  void setStateFile() {
    selectedFile.value = null;
    selectedFileName.value = "";
    base64FileDinhKem.value = "";
  }

  //chuyển page sang ký số

  void convertToPdf(String pathFile, int isLoaiKySo) async {
    if (pathFile.toString().toLowerCase().split(".").last == "doc" ||
        pathFile.toString().toLowerCase().split(".").last == "doc") {
      var oldFile = pathFile;
      await fileProvide.auFileCdTTWP(pathFile).then((resp) {
        if (resp.length > 0) {
          if (resp.toString().split('|')[0] == "1") {
            String newFile =
                oldFile.toString().replaceAll(RegExp(r'.docx'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.doc'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.DOC'), '.pdf');
            newFile = newFile.replaceAll(RegExp(r'.DOCX'), '.pdf');
            Get.delete<KySoController>();
            Get.toNamed(Routers.KYSOKPI, arguments: {
              "pathFile": newFile,
              "maVanBanDi": maVbnbKc,
              "isLoaiKySo": isLoaiKySo,
              "srcFile": item.value.srcVanBan,
              "isVBNB": 1,
              "maXuLy": maVbnbGui
            });
          }
        } else {
          Get.delete<KySoController>();
          Get.toNamed(Routers.KYSOKPI, arguments: {
            "pathFile": oldFile,
            "maVanBanDi": maVbnbKc,
            "isLoaiKySo": isLoaiKySo,
            "srcFile": item.value.srcVanBan,
            "isVBNB": 1,
            "maXuLy": maVbnbGui
          });
        }
      });
    } else {
      Get.delete<KySoController>();
      Get.toNamed(Routers.KYSOKPI, arguments: {
        "pathFile": pathFile,
        "maVanBanDi": maVbnbKc,
        "srcFile": item.value.srcVanBan,
        "isLoaiKySo": isLoaiKySo,
        "isVBNB": 1,
        "maXuLy": maVbnbGui
      });
    }
  }

  void onChuyenPageKySo(String? pathFile, int isLoaiKySo) async {
    // convert file
    convertToPdf(pathFile!, isLoaiKySo);
  }

  List<Widget> renderNodeChildQtxl(QtxlVanBanNoiBo item) {
    List<Widget> result = [];
    var nodeChildren =
        listQtxlVbnb.where((el) => el.maVbnbGuiCha == item.maVanBanNoiBoGuiKc);
    if (nodeChildren.isNotEmpty) {
      nodeChildren.forEach((element) {
        result.add(Text(
            "${element.tenCanBoNhan} ${(element.ngayXem != null) ? "(Đã xem: " + DateFormat('dd/MM/yyyy hh:ss').format(element.ngayXem!) + ")" : ""} ${(element.yKienXuLy != null) ? "ý kiên: " + element.yKienXuLy : ""}"));
      });
    }
    return result;
  }

  // update view
  void upDateViewCv() async {
    await vbnbProvider.auVbnbXemVanBanNoiBo(maVbnbGui, 3).then((value) {
      // print(value);
    });
  }

  void onPressPageXuLy(DetaisVbnb itemDetaisl) {
    Get.delete<XuLyVbnbController>();
    Get.toNamed(Routers.VBNBCHUYENXL, arguments: {
      "trichYeu": itemDetaisl.trichYeu,
      "maVbnbKc": itemDetaisl.maVbnbKc ?? 0,
      "maVbnbGuiKc": itemDetaisl.maVanBanNoiBoGuiKc ?? 0,
      "maVbnbGuiCha": itemDetaisl.maVbnbKc ?? 0
    });
  }

  @override
  void onReady() {
    super.onReady();
    // Cập nhật giao diện với dữ liệu ban đầu
  }

  @override
  void onClose() {
    super.onClose();
  }
}
