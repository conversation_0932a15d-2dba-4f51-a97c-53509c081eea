import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_chititet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbnb/vbnb_qtxl_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/kyso/kyso_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbnb/vbnb_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/chitiet_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class XuLyVbnbController extends GetxController
    with SingleGetTickerProviderMixin {
  final _getStorage = GetStorage();
  XuLyVbnbController();
  HomeController homeController = Get.find();
  var vbnbProvider = VbnbProvider();
  var vbdeProvider = VbdeProvider();
  Rx<DateTime?> selectDateTime = Rx<DateTime?>(null);
  //khai báo

  var treeDsCbChuyenVbnb = TreeNodes(title: "", children: []).obs;

  List<String> selectedXDBArr = <String>[].obs;

  late String trichYeu;
  late int maVbnbKc;
  late int maVbnbGuiKc;
  late int maVbnbGuiCha;

  var arrayTreecvChuyen = <TreeDetail>[];
  var arrayNhomDVN = <TreeDetail>[];
  var treeNhomDVN = TreeNodes(title: "", children: []).obs;

  late TextEditingController yKienInputController;
  late TextEditingController yKienXDBinputController;
  RxBool isSms = false.obs;
  RxBool isHoanTatXDB = true.obs;
  late TabController tabTreeController;
  List<Tab> tabsTree = [
    const Tab(text: "Đơn vị/Cán bộ"),
    const Tab(text: "Nhóm cán bộ"),
  ];

  @override
  void onInit() {
    super.onInit();
    trichYeu = Get.arguments["trichYeu"].toString();
    maVbnbKc = Get.arguments["maVbnbKc"].toInt();
    maVbnbGuiKc = Get.arguments["maVbnbGuiKc"].toInt();
    maVbnbGuiCha = Get.arguments["maVbnbGuiCha"].toInt();
    yKienInputController = TextEditingController();
    yKienXDBinputController = TextEditingController();
    tabTreeController = TabController(length: tabsTree.length, vsync: this);
  }

  var trangThaiXuLy = "Đã hoàn tất".obs;
  void clearDateTime() {
    if (selectDateTime.value != null) {
      selectDateTime.value = null;
    }
  }

  void changeIsSms(bool value) {
    isSms.value = value;
  }

  void isChangeHoanTatXDB(bool value) {
    isHoanTatXDB.value = value;
  }

  String get hanxuly => selectDateTime.value == null
      ? "dd/mm/yyyy"
      : DateFormat('dd/MM/yyyy').format(selectDateTime.value!);

  // method chẹck chọn cây
  // Method

  void checkAllxdb(String id, bool value) {
    if (selectedXDBArr.contains(id)) {
      selectedXDBArr.remove(id);
    } else {
      if (value) {
        selectedXDBArr.add(id);
      } else {
        selectedXDBArr.remove(id);
      }
    }
    var arrayChildren =
        arrayTreecvChuyen.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedXDBArr.isNotEmpty) {
        check = selectedXDBArr.contains(item.id);
      }
      var children = arrayTreecvChuyen.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedXDBArr.contains(item.id)) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllxdb(item2.id!, value);
        }
      } else {
        if (check) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
      }
    }
  }

  void confirmchuyenXuLyVbnb(List<DetailCBTree> listCB, int maVbnbKc,
      int maVbnbGuiCha, String trichYeu) async {
    var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
    int isTrangThai = 2;
    String chuoiMaCanBoNhan = "";
    listCB.forEach((element) {
      chuoiMaCanBoNhan += element.ma!.replaceAll("CB", "") + ";";
    });
    chuoiMaCanBoNhan =
        chuoiMaCanBoNhan.substring(0, chuoiMaCanBoNhan.length - 1);
    await vbnbProvider
        .auVbnbCvBnb(
            maVbnbKc,
            maCtcbKc,
            chuoiMaCanBoNhan,
            yKienInputController.text,
            isTrangThai,
            (isSms.value ? 1 : 0),
            maVbnbGuiCha)
        .then((value) {
      if (isSms.value) {
        List<String> arrSDT = [];
        listCB.forEach((element) {
          element.diDongCanBo!.isNotEmpty
              ? arrSDT.add(element.diDongCanBo!)
              : null;
        });
        if (arrSDT.isNotEmpty) {
          var noiDung = trichYeu;
          var chuoiDiDong = arrSDT.join(',');
          vbdeProvider.SendSMS(noiDung, chuoiDiDong,
              _getStorage.read(GetStorageKey.maDonViQuanTri));
        }
      }
      yKienInputController.clear();
      HomeController homeController = Get.find();
      homeController.loadDSnv();
      VanBanNoiBoController vbnbController = Get.find();
      vbnbController.loadDanhSachByIndexTab(0);
      Get.delete<ChiTietVbnbController>();
      CustomSnackBar.showSuccessSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Thực hiện thành công!");
      Get.offNamed(Routers.VBNB);
    });
  }

  void loadTreeDsCbChuyenVbde() async {
    List<Map<String, String>> arrayDsCbChuyenVbde = [];
    arrayDsCbChuyenVbde.clear();
    await vbnbProvider.auDsCbNVnbn().then((element) => {
          if (element.data!.isNotEmpty)
            {
              element.data!.forEach((item) {
                arrayDsCbChuyenVbde.add({
                  'id': item.id.toString(),
                  'parent': item.parent.toString(),
                  'value': item.text.toString(),
                  'hoVaTen': item.liAttr!.dataTenCanBo.toString(),
                  'diDong': item.liAttr!.dataPhone.toString(),
                  'tenChucVu': item.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayTreecvChuyen.addAll(element.data!)
            }
        });
    arrayDsCbChuyenVbde.isNotEmpty
        ? treeDsCbChuyenVbnb.value =
            MethodUntils.listToTrees(arrayDsCbChuyenVbde)
        : null;
  }

  void loadTreeNhomCanBo() async {
    int maCanBo = _getStorage.read(GetStorageKey.maCanBo);
    List<Map<String, String>> arrayNhomCBN = [];
    arrayNhomCBN.clear();
    await vbdeProvider.getNhomCanBoNhan(maCanBo).then((item) => {
          if (item.data!.isNotEmpty)
            {
              item.data!.forEach((element) {
                arrayNhomCBN.add({
                  'id': element.id.toString(),
                  'parent': element.parent.toString(),
                  'value': element.text.toString(),
                  'hoVaTen': element.liAttr!.dataTenCanBo.toString(),
                  'diDong': element.liAttr!.dataPhone.toString(),
                  'tenChucVu': element.liAttr!.dataChucVu.toString(),
                });
              }),
              arrayNhomDVN.addAll(item.data!)
            }
        });
    arrayNhomCBN.isNotEmpty
        ? treeNhomDVN.value = MethodUntils.listToTrees(arrayNhomCBN)
        : null;
  }

  // danh sachs can bộ đã chọn
  List<DetailCBTree> getCBchoosedCv() {
    List<DetailCBTree> arrayResult = <DetailCBTree>[];
    List<TreeDetail> arrayDSCB = MethodUntils()
        .removeDuplicates([...arrayTreecvChuyen, ...arrayNhomDVN])
        .where((element) => element.id!.indexOf('CB') != -1)
        .toList();
    selectedXDBArr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '1',
            "Xem để biết"));
      }
    });
    return arrayResult;
  }

  @override
  void onReady() {
    loadTreeDsCbChuyenVbde();
    loadTreeNhomCanBo();
    yKienInputController.clear();
    super.onReady();
  }

  @override
  void dispose() {
    yKienInputController.dispose();
    tabTreeController.dispose();
    super.dispose();
  }

  @override
  void onClose() {
    yKienInputController.dispose();
    tabTreeController.dispose();
    super.onClose();
  }
}
