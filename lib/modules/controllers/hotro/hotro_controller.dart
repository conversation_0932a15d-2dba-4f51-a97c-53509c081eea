import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/jsTree_model.dart';
import 'package:vnpt_ioffice_camau/app/model/tracuu/tracuu_vbdi_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/home/<USER>';
import 'package:vnpt_ioffice_camau/app/provider/tracuu/tracuu_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class HoTroController extends GetxController {
  final TextEditingController searchController = TextEditingController();
  final TextEditingController unitSearchController = TextEditingController();
  final GetStorage _store = GetStorage();

  // Observable variables
  var selectedUnitId = 0.obs;
  var selectedUnitName = ''.obs;
  var documentList = <Map<String, dynamic>>[].obs;
  var isSearching = false.obs;
  var isRecalling = false.obs;
  var recallingDocId = 0.obs;
  var showUnitTreeDialog = false.obs;
  var searchQuery = ''.obs;
  var isLoadingUnits = false.obs;

  // Cây đơn vị
  late Rx<UnitTreeNode> unitTree;
  late Rx<UnitTreeNode> filteredUnitTree;

  @override
  void onInit() {
    super.onInit();
    _initializeUnitTree();
  }

  void _initializeUnitTree() {
    // Tạo cây đơn vị với cấu trúc cha con
    final rootUnit = UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      children: [
        UnitTreeNode(
          id: 1,
          name: 'Ban Giám đốc',
          parentId: 0,
          children: [
            UnitTreeNode(id: 11, name: 'Giám đốc', parentId: 1),
            UnitTreeNode(id: 12, name: 'Phó Giám đốc', parentId: 1),
          ],
        ),
        UnitTreeNode(
          id: 2,
          name: 'Phòng Công nghệ thông tin',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 21, name: 'Bộ phận Phát triển phần mềm', parentId: 2),
            UnitTreeNode(id: 22, name: 'Bộ phận Hạ tầng mạng', parentId: 2),
            UnitTreeNode(
                id: 23, name: 'Bộ phận Bảo mật thông tin', parentId: 2),
          ],
        ),
        UnitTreeNode(
          id: 3,
          name: 'Phòng Hành chính - Nhân sự',
          parentId: 0,
          children: [
            UnitTreeNode(id: 31, name: 'Bộ phận Nhân sự', parentId: 3),
            UnitTreeNode(id: 32, name: 'Bộ phận Hành chính', parentId: 3),
          ],
        ),
        UnitTreeNode(
          id: 4,
          name: 'Phòng Tài chính - Kế toán',
          parentId: 0,
          children: [
            UnitTreeNode(id: 41, name: 'Bộ phận Kế toán', parentId: 4),
            UnitTreeNode(id: 42, name: 'Bộ phận Tài chính', parentId: 4),
          ],
        ),
        UnitTreeNode(
          id: 5,
          name: 'Phòng Kinh doanh',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 51, name: 'Bộ phận Kinh doanh Doanh nghiệp', parentId: 5),
            UnitTreeNode(
                id: 52, name: 'Bộ phận Kinh doanh Cá nhân', parentId: 5),
          ],
        ),
        UnitTreeNode(
          id: 6,
          name: 'Phòng Kỹ thuật',
          parentId: 0,
          children: [
            UnitTreeNode(id: 61, name: 'Bộ phận Vận hành', parentId: 6),
            UnitTreeNode(id: 62, name: 'Bộ phận Bảo trì', parentId: 6),
          ],
        ),
      ],
    );

    unitTree = rootUnit.obs;
    filteredUnitTree = rootUnit.obs;
  }

  // Methods for unit tree
  void toggleUnitExpansion(UnitTreeNode node) {
    node.expanded.value = !node.expanded.value;
  }

  void selectUnit(int unitId, String unitName) {
    // Clear previous selection
    _clearAllSelections(unitTree.value);

    // Set new selection
    selectedUnitId.value = unitId;
    selectedUnitName.value = unitName;
    // Load staff list from API

    // Mark selected node
    _markSelectedNode(unitTree.value, unitId);

    // Close dialog
    showUnitTreeDialog.value = false;
  }

  void _clearAllSelections(UnitTreeNode node) {
    node.selected.value = false;
    for (var child in node.children) {
      _clearAllSelections(child);
    }
  }

  void _markSelectedNode(UnitTreeNode node, int selectedId) {
    if (node.id == selectedId) {
      node.selected.value = true;
    }
    for (var child in node.children) {
      _markSelectedNode(child, selectedId);
    }
  }

  void showUnitSelector() {
    showUnitTreeDialog.value = true;
    // Tự động load dữ liệu từ API khi mở dialog
    loadUnitsFromApi();
  }

  void hideUnitSelector() {
    showUnitTreeDialog.value = false;
    searchQuery.value = '';
    _resetFilter();
  }

  // Method để refresh dữ liệu đơn vị (có thể gọi từ ngoài nếu cần)
  Future<void> refreshUnits() async {
    await loadUnitsFromApi();
  }

  void searchUnits(String query) {
    searchQuery.value = query.toLowerCase();
    if (query.isEmpty) {
      _resetFilter();
    } else {
      _filterUnits();
    }
  }

  void _resetFilter() {
    filteredUnitTree.value = _copyUnitTree(unitTree.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  void _filterUnits() {
    filteredUnitTree.value = _filterUnitTree(unitTree.value, searchQuery.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  UnitTreeNode _copyUnitTree(UnitTreeNode original) {
    return UnitTreeNode(
      id: original.id,
      name: original.name,
      parentId: original.parentId,
      expanded: true,
      selected: original.selected.value,
      children: original.children.map((child) => _copyUnitTree(child)).toList(),
    );
  }

  UnitTreeNode _filterUnitTree(UnitTreeNode node, String query) {
    List<UnitTreeNode> filteredChildren = [];

    // Lọc các node con
    for (var child in node.children) {
      var filteredChild = _filterUnitTree(child, query);
      if (_shouldIncludeNode(filteredChild, query)) {
        filteredChildren.add(filteredChild);
      }
    }

    return UnitTreeNode(
      id: node.id,
      name: node.name,
      parentId: node.parentId,
      expanded: true,
      selected: node.selected.value,
      children: filteredChildren,
    );
  }

  bool _shouldIncludeNode(UnitTreeNode node, String query) {
    // Bao gồm node nếu tên chứa từ khóa tìm kiếm
    if (node.name.toLowerCase().contains(query)) {
      return true;
    }

    // Hoặc nếu có node con nào đó thỏa mãn điều kiện
    return node.children.isNotEmpty;
  }

  void _expandAllNodes(UnitTreeNode node) {
    node.expanded.value = true;
    for (var child in node.children) {
      _expandAllNodes(child);
    }
  }

  // Method để load dữ liệu từ API
  Future<void> loadUnitsFromApi() async {
    if (isLoadingUnits.value) return; // Tránh gọi API nhiều lần

    try {
      isLoadingUnits.value = true;

      // Gọi API thực tế
      final apiResponse = await HomeProvider().jsTreeDonViChuQuan(16);

      // Chuyển đổi dữ liệu API thành cây đơn vị
      if (apiResponse['success'] == true && apiResponse['data'] != null) {
        final flatList = apiResponse['data'] as List;
        final rootNode = _buildTreeFromFlatList(flatList);

        unitTree.value = rootNode;
        filteredUnitTree.value = _copyUnitTree(rootNode);
      }
    } catch (e) {
      // Hiển thị lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể tải dữ liệu từ API: $e',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );

      // Fallback to default data
      _initializeUnitTree();
    } finally {
      isLoadingUnits.value = false;
    }
  }

  // Method để chuyển đổi danh sách phẳng thành cây
  UnitTreeNode _buildTreeFromFlatList(List<dynamic> flatList) {
    Map<String, UnitTreeNode> nodeMap = {};
    List<UnitTreeNode> rootNodes = [];

    // Tạo tất cả các node trước
    for (var item in flatList) {
      final node = UnitTreeNode.fromApiResponse(item);
      nodeMap[node.id.toString()] = node;

      if (item['parent'] == '#') {
        rootNodes.add(node);
      }
    }

    // Xây dựng cấu trúc cây
    for (var item in flatList) {
      final nodeId = item['id'].toString();
      final parentId = item['parent'].toString();

      if (parentId != '#' && nodeMap.containsKey(parentId)) {
        final parentNode = nodeMap[parentId]!;
        final childNode = nodeMap[nodeId]!;

        // Tạo node cha mới với children được cập nhật
        final updatedChildren = List<UnitTreeNode>.from(parentNode.children)
          ..add(childNode);
        final updatedParent = parentNode.copyWith(children: updatedChildren);
        nodeMap[parentId] = updatedParent;

        // Cập nhật lại trong map
        nodeMap[parentId] = updatedParent;
      }
    }

    // Nếu có nhiều root nodes, tạo một root node tổng hợp
    if (rootNodes.length > 1) {
      return UnitTreeNode(
        id: 0,
        name: 'Tất cả đơn vị',
        expanded: true,
        children:
            rootNodes.map((root) => nodeMap[root.id.toString()]!).toList(),
      );
    } else if (rootNodes.isNotEmpty) {
      return nodeMap[rootNodes.first.id.toString()]!;
    }

    return _createDefaultRootNode();
  }

  UnitTreeNode _createDefaultRootNode() {
    return UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      expanded: true,
    );
  }

// Methods

  // Method để clear search
  @override
  void dispose() {
    searchController.dispose();
    unitSearchController.dispose();
    super.dispose();
  }

  Future<void> searchDocuments() async {
    if (selectedUnitId.value == 0) {
      Get.snackbar(
        'Thông báo',
        'Vui lòng chọn đơn vị trước khi tìm kiếm!',
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    String searchTerm = searchController.text.trim();
    if (searchTerm.isEmpty) {
      Get.snackbar(
        'Thông báo',
        'Vui lòng nhập ký hiệu văn bản để tìm kiếm!',
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    try {
      isSearching.value = true;

      // Lấy năm hiện tại
      int currentYear = DateTime.now().year;

      // Gọi API tìm kiếm văn bản
      final result = await TraCuuProvider().auVbdiTraCuu(
        1, // page
        10, // pageSize
        'vt', // vaiTro
        '', // trichYeu
        searchTerm, // soKyHieu
        currentYear, // nam
        maDonviQuanTri: selectedUnitId.value,
      );

      if (result.success) {
        // Convert TraCuuVanBanDi to Map<String, dynamic>
        List<Map<String, dynamic>> convertedData = result.data
            .map((item) => {
                  'id': item.maVanBanDiKc?.toInt() ?? 0,
                  'kyHieu': item.soKyHieu ?? '',
                  'tieuDe': item.trichYeu ?? '',
                  'ngayBanHanh': item.ngayBanHanh != null
                      ? DateFormat('dd/MM/yyyy').format(item.ngayBanHanh!)
                      : '',
                  'loaiVanBan': item.tenLoaiVanBan ?? '',
                  'maVanBan': item.maVanBanKc?.toInt() ?? 0,
                  'trangThai': 'Đã ban hành',
                  'nguoiKy': item.nguoiKy ?? '',
                  'fileVanBan': item.fileVanBan ?? '',
                })
            .toList();

        documentList.value = convertedData;

        if (documentList.isEmpty) {
          Get.snackbar(
            'Thông báo',
            'Không tìm thấy văn bản với ký hiệu "$searchTerm"',
            backgroundColor: Colors.blue[600],
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
          );
        } else {
          Get.snackbar(
            'Thành công',
            'Tìm thấy ${documentList.length} văn bản',
            backgroundColor: Colors.green[600],
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP,
            duration: const Duration(seconds: 2),
          );
        }
      } else {
        documentList.clear();
        Get.snackbar(
          'Lỗi',
          result.message.isNotEmpty
              ? result.message
              : 'Không thể tìm kiếm văn bản. Vui lòng thử lại!',
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      documentList.clear();
      Get.snackbar(
        'Lỗi',
        'Có lỗi xảy ra khi tìm kiếm: $e',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isSearching.value = false;
    }
  }

  Future<void> recallDocument(Map<String, dynamic> document) async {
    isRecalling.value = true;
    recallingDocId.value = document['id'];

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện thu hồi văn bản ở đây
      // await apiService.recallDocument(document['id']);
      final response = await HomeProvider()
          .quanTriThuHoiVb(16, 1, document['maVanBan'], 0, document['id']);
      if (response['message'] == 'Thực thi thành công') {
        // Hiển thị thông báo thành công
        Get.snackbar(
          'Thành công',
          'Đã thu hồi văn bản "${document['kyHieu']}" thành công!',
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 3),
          icon: const Icon(Icons.check_circle, color: Colors.white),
        );
        // Thu hồi thành công - xóa dữ liệu tìm kiếm và danh sách
        searchController.clear();
        documentList.clear();
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể thu hồi văn bản "${document['kyHieu']}". Vui lòng thử lại!',
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 3),
          icon: const Icon(Icons.error, color: Colors.white),
        );
        // Thu hồi thành công - xóa dữ liệu tìm kiếm và danh sách
        searchController.clear();
        documentList.clear();
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể thu hồi văn bản "${document['kyHieu']}". Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isRecalling.value = false;
      recallingDocId.value = 0;
    }
  }
}
