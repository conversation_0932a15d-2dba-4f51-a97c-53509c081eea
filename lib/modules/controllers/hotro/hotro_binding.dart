import 'package:get/instance_manager.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/hotro_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';

class HoTroBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => HoTroController(), fenix: true);
  }
}

class ResetMkNguoiDungBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ResetMkNguoiDungController(), fenix: true);
  }
}
