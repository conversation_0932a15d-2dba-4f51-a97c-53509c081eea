import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/jsTree_model.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/staff_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/home/<USER>';

class ResetMkNguoiDungController extends GetxController {
  // Observable variables
  var selectedUnitId = 0.obs;
  var selectedUnitName = ''.obs;
  var staffList = <StaffModel>[].obs;
  var filteredStaffList = <StaffModel>[].obs;
  var isLoadingStaff = false.obs;
  var staffSearchQuery = ''.obs;
  var isResetting = false.obs;
  var resettingStaffId = 0.obs;
  var showUnitTreeDialog = false.obs;
  var searchQuery = ''.obs;
  var isLoadingUnits = false.obs;

  // Cây đơn vị
  late Rx<UnitTreeNode> unitTree;
  late Rx<UnitTreeNode> filteredUnitTree;

  @override
  void onInit() {
    super.onInit();
    _initializeUnitTree();
  }

  void _initializeUnitTree() {
    // Tạo cây đơn vị với cấu trúc cha con
    final rootUnit = UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      children: [
        UnitTreeNode(
          id: 1,
          name: 'Ban Giám đốc',
          parentId: 0,
          children: [
            UnitTreeNode(id: 11, name: 'Giám đốc', parentId: 1),
            UnitTreeNode(id: 12, name: 'Phó Giám đốc', parentId: 1),
          ],
        ),
        UnitTreeNode(
          id: 2,
          name: 'Phòng Công nghệ thông tin',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 21, name: 'Bộ phận Phát triển phần mềm', parentId: 2),
            UnitTreeNode(id: 22, name: 'Bộ phận Hạ tầng mạng', parentId: 2),
            UnitTreeNode(
                id: 23, name: 'Bộ phận Bảo mật thông tin', parentId: 2),
          ],
        ),
        UnitTreeNode(
          id: 3,
          name: 'Phòng Hành chính - Nhân sự',
          parentId: 0,
          children: [
            UnitTreeNode(id: 31, name: 'Bộ phận Nhân sự', parentId: 3),
            UnitTreeNode(id: 32, name: 'Bộ phận Hành chính', parentId: 3),
          ],
        ),
        UnitTreeNode(
          id: 4,
          name: 'Phòng Tài chính - Kế toán',
          parentId: 0,
          children: [
            UnitTreeNode(id: 41, name: 'Bộ phận Kế toán', parentId: 4),
            UnitTreeNode(id: 42, name: 'Bộ phận Tài chính', parentId: 4),
          ],
        ),
        UnitTreeNode(
          id: 5,
          name: 'Phòng Kinh doanh',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 51, name: 'Bộ phận Kinh doanh Doanh nghiệp', parentId: 5),
            UnitTreeNode(
                id: 52, name: 'Bộ phận Kinh doanh Cá nhân', parentId: 5),
          ],
        ),
        UnitTreeNode(
          id: 6,
          name: 'Phòng Kỹ thuật',
          parentId: 0,
          children: [
            UnitTreeNode(id: 61, name: 'Bộ phận Vận hành', parentId: 6),
            UnitTreeNode(id: 62, name: 'Bộ phận Bảo trì', parentId: 6),
          ],
        ),
      ],
    );

    unitTree = rootUnit.obs;
    filteredUnitTree = rootUnit.obs;
  }

  // Methods for unit tree
  void toggleUnitExpansion(UnitTreeNode node) {
    node.expanded.value = !node.expanded.value;
  }

  void selectUnit(int unitId, String unitName) {
    // Clear previous selection
    _clearAllSelections(unitTree.value);

    // Set new selection
    selectedUnitId.value = unitId;
    selectedUnitName.value = unitName;
    // Load staff list from API
    loadStaffFromApi(unitId);

    // Mark selected node
    _markSelectedNode(unitTree.value, unitId);

    // Close dialog
    showUnitTreeDialog.value = false;
  }

  void _clearAllSelections(UnitTreeNode node) {
    node.selected.value = false;
    for (var child in node.children) {
      _clearAllSelections(child);
    }
  }

  void _markSelectedNode(UnitTreeNode node, int selectedId) {
    if (node.id == selectedId) {
      node.selected.value = true;
    }
    for (var child in node.children) {
      _markSelectedNode(child, selectedId);
    }
  }

  void showUnitSelector() {
    showUnitTreeDialog.value = true;
    // Tự động load dữ liệu từ API khi mở dialog
    loadUnitsFromApi();
  }

  void hideUnitSelector() {
    showUnitTreeDialog.value = false;
    searchQuery.value = '';
    _resetFilter();
  }

  // Method để refresh dữ liệu đơn vị (có thể gọi từ ngoài nếu cần)
  Future<void> refreshUnits() async {
    await loadUnitsFromApi();
  }

  // Method để load danh sách cán bộ từ API
  Future<void> loadStaffFromApi(int unitId) async {
    if (isLoadingStaff.value) return;

    try {
      isLoadingStaff.value = true;
      staffList.clear();

      // Gọi API lấy danh sách cán bộ theo đơn vị
      // Tạm thời sử dụng dữ liệu demo, sau này thay bằng API thực
      await Future.delayed(const Duration(milliseconds: 500));

      // Demo data theo format mới
      final demoApiResponse = await HomeProvider().getStaffbyUnit(unitId);

      if (demoApiResponse['success'] == true &&
          demoApiResponse['data'] != null) {
        final staffApiResponse = StaffApiResponse.fromJson(demoApiResponse);
        staffList.value = staffApiResponse.data;
        filteredStaffList.value = staffApiResponse.data;
        staffSearchQuery.value = '';
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách cán bộ: $e',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoadingStaff.value = false;
    }
  }

  void searchUnits(String query) {
    searchQuery.value = query.toLowerCase();
    if (query.isEmpty) {
      _resetFilter();
    } else {
      _filterUnits();
    }
  }

  void _resetFilter() {
    filteredUnitTree.value = _copyUnitTree(unitTree.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  void _filterUnits() {
    filteredUnitTree.value = _filterUnitTree(unitTree.value, searchQuery.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  UnitTreeNode _copyUnitTree(UnitTreeNode original) {
    return UnitTreeNode(
      id: original.id,
      name: original.name,
      parentId: original.parentId,
      expanded: true,
      selected: original.selected.value,
      children: original.children.map((child) => _copyUnitTree(child)).toList(),
    );
  }

  UnitTreeNode _filterUnitTree(UnitTreeNode node, String query) {
    List<UnitTreeNode> filteredChildren = [];

    // Lọc các node con
    for (var child in node.children) {
      var filteredChild = _filterUnitTree(child, query);
      if (_shouldIncludeNode(filteredChild, query)) {
        filteredChildren.add(filteredChild);
      }
    }

    return UnitTreeNode(
      id: node.id,
      name: node.name,
      parentId: node.parentId,
      expanded: true,
      selected: node.selected.value,
      children: filteredChildren,
    );
  }

  bool _shouldIncludeNode(UnitTreeNode node, String query) {
    // Bao gồm node nếu tên chứa từ khóa tìm kiếm
    if (node.name.toLowerCase().contains(query)) {
      return true;
    }

    // Hoặc nếu có node con nào đó thỏa mãn điều kiện
    return node.children.isNotEmpty;
  }

  void _expandAllNodes(UnitTreeNode node) {
    node.expanded.value = true;
    for (var child in node.children) {
      _expandAllNodes(child);
    }
  }

  // Method để load dữ liệu từ API
  Future<void> loadUnitsFromApi() async {
    if (isLoadingUnits.value) return; // Tránh gọi API nhiều lần

    try {
      isLoadingUnits.value = true;

      // Gọi API thực tế
      final apiResponse = await HomeProvider().jsTreeDonViChuQuan(16);

      // Chuyển đổi dữ liệu API thành cây đơn vị
      if (apiResponse['success'] == true && apiResponse['data'] != null) {
        final flatList = apiResponse['data'] as List;
        final rootNode = _buildTreeFromFlatList(flatList);

        unitTree.value = rootNode;
        filteredUnitTree.value = _copyUnitTree(rootNode);

        // Hiển thị thông báo thành công (chỉ khi không phải lần đầu load)
      }
    } catch (e) {
      // Hiển thị lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể tải dữ liệu từ API: $e',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );

      // Fallback to default data
      _initializeUnitTree();
    } finally {
      isLoadingUnits.value = false;
    }
  }

  // Method để chuyển đổi danh sách phẳng thành cây
  UnitTreeNode _buildTreeFromFlatList(List<dynamic> flatList) {
    Map<String, UnitTreeNode> nodeMap = {};
    List<UnitTreeNode> rootNodes = [];

    // Tạo tất cả các node trước
    for (var item in flatList) {
      final node = UnitTreeNode.fromApiResponse(item);
      nodeMap[node.id.toString()] = node;

      if (item['parent'] == '#') {
        rootNodes.add(node);
      }
    }

    // Xây dựng cấu trúc cây
    for (var item in flatList) {
      final nodeId = item['id'].toString();
      final parentId = item['parent'].toString();

      if (parentId != '#' && nodeMap.containsKey(parentId)) {
        final parentNode = nodeMap[parentId]!;
        final childNode = nodeMap[nodeId]!;

        // Tạo node cha mới với children được cập nhật
        final updatedChildren = List<UnitTreeNode>.from(parentNode.children)
          ..add(childNode);
        final updatedParent = parentNode.copyWith(children: updatedChildren);
        nodeMap[parentId] = updatedParent;

        // Cập nhật lại trong map
        nodeMap[parentId] = updatedParent;
      }
    }

    // Nếu có nhiều root nodes, tạo một root node tổng hợp
    if (rootNodes.length > 1) {
      return UnitTreeNode(
        id: 0,
        name: 'Tất cả đơn vị',
        expanded: true,
        children:
            rootNodes.map((root) => nodeMap[root.id.toString()]!).toList(),
      );
    } else if (rootNodes.isNotEmpty) {
      return nodeMap[rootNodes.first.id.toString()]!;
    }

    return _createDefaultRootNode();
  }

  UnitTreeNode _createDefaultRootNode() {
    return UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      expanded: true,
    );
  }

// Methods

  // Method để tìm kiếm cán bộ
  void searchStaff(String query) {
    staffSearchQuery.value = query.toLowerCase();

    if (query.isEmpty) {
      filteredStaffList.value = staffList;
    } else {
      filteredStaffList.value = staffList.where((staff) {
        return staff.hoVaTenCanBo.toLowerCase().contains(query.toLowerCase()) ||
            staff.username.toLowerCase().contains(query.toLowerCase()) ||
            staff.tenChucVu.toLowerCase().contains(query.toLowerCase()) ||
            staff.getPhoneNumber().contains(query);
      }).toList();
    }
  }

  // Method để clear search
  void clearStaffSearch() {
    staffSearchQuery.value = '';
    filteredStaffList.value = staffList;
  }

  Future<void> resetPassword(StaffModel staff) async {
    try {
      isResetting.value = true;
      resettingStaffId.value = staff.maCtcbKc.toInt();

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện reset password ở đây
      // await apiService.resetPassword(staff.maCtcbKc);

      final apiResponse =
          await HomeProvider().resetPassStaff(staff.maCtcbKc.toInt());

      if (apiResponse['message'] == 'Thực thi thành công') {
        Get.snackbar(
          'Thành công',
          'Đã reset mật khẩu thành công cho ${staff.hoVaTenCanBo}!',
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 3),
          icon: const Icon(Icons.check_circle, color: Colors.white),
        );
      } else {
        // Hiển thị lỗi
        Get.snackbar(
          'Lỗi',
          'Không thể reset mật khẩu cho ${staff.hoVaTenCanBo}. Vui lòng thử lại!',
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
          duration: const Duration(seconds: 3),
          icon: const Icon(Icons.error, color: Colors.white),
        );
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể reset mật khẩu cho ${staff.hoVaTenCanBo}. Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isResetting.value = false;
      resettingStaffId.value = 0;
    }
  }
}
