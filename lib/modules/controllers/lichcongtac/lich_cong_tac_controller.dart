import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/access_token.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/auth_model.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/dsdvchuquan_model.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_coquan.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_cot_ld.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_don_vi.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_dscb.dart';
import 'package:vnpt_ioffice_camau/app/model/user/ctcb_kiemnhiem.dart';
import 'package:vnpt_ioffice_camau/app/provider/lct/lct_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/login/login_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/utils/week_range.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class LichCongTacController extends GetxController
    with SingleGetTickerProviderMixin {
  var lctProvider = LctProvider();

  var tenDonVi = "".obs;
  var listDanhSachDonvi = <DetailDsDonViLCT>[];
  var listDanhSachCanBo = <DetailLctDsCb>[];
  var selectedCaNhan = DetailLctDsCb().obs;
  var selectedDonVi = DetailDsDonViLCT().obs;
  var selectedCoQuan = DetailDsCoQuanLct().obs;
  var dsLctDonVi = <DetailDsLctDonVi>[].obs;
  var dsCoQuanLct = <DetailDsCoQuanLct>[].obs;
  var listWeekByMoth = <WeekRange>[].obs;
  // lct lãnh đạo

  var indexTabGolbal = 0.obs;
  var moths = DateTime.now().month.obs;

  final List<int> years =
      List<int>.generate(3, (index) => (DateTime.now().year + 1) - index);
  var selectYear = DateTime.now().year.obs;

  void subMothds() {
    if (moths > 1) {
      moths.value--;
    }
    getWeekInMoth(moths.value);
  }

  void addMothds() {
    if (moths < 12) {
      moths.value++;
    }
    getWeekInMoth(moths.value);
  }

  void loadDsDonVi() async {
    await lctProvider.LayDachDonVi().then((value) {
      listDanhSachDonvi = value.data!;
      selectedDonVi.value = value.data!.first;
      loadDsLctDonVi();
    });
  }

  void loadDanhSachCanBo() async {
    await lctProvider.getLctDsCb().then((value) {
      if (value.data!.isNotEmpty) {
        listDanhSachCanBo = value.data!;
        selectedCaNhan.value = value.data!.first;
        loadLichCongTacCaNhan();
      }
    });
  }

  void loadLichCongTacCaNhan() async {
    await lctProvider
        .getDetailsLctCaNhan(
            selectedCaNhan.value.maCtcbKc!.toInt(), selectYear.value)
        .then((value) {
      dsLctDonVi.value = value.data!;
      getWeekNow();
    });
  }

  void onChangeTab(int indexTab) {
    indexTabGolbal.value = indexTab;
    loadDataByindexTab();
  }

  void loadDsLctDonVi() async {
    await lctProvider
        .getDsLctDonVi(selectedDonVi.value.maDonViKc!.toInt(), selectYear.value)
        .then((value) {
      dsLctDonVi.value = value.data!;
      getWeekNow();
    });
  }

  void loadDsLctCoQuan() async {
    await lctProvider
        .getDsLctDonViQuanTri(
            selectedCoQuan.value.maDonViKc!.toInt(), selectYear.value)
        .then((value) {
      dsLctDonVi.value = value.data!;
      getWeekNow();
    });
  }

  void getWeekInMoth(int moth) {
    List<WeekRange> weeksInYear = MethodUntils.getWeeksInYear(selectYear.value);
    List<WeekRange> result = [];
    for (var week in weeksInYear) {
      if (week.start.month == moth || week.end.month == moth) {
        result.add(week);
      }
    }
    listWeekByMoth.value = result;
  }

  int getWeekNow() {
    var num = DateTime.now();
    List<WeekRange> weeksInYear =
        MethodUntils.getWeeksInYear(DateTime.now().year);
    List<WeekRange> result = [];
    for (var week in weeksInYear) {
      if (week.start.month == DateTime.now().month ||
          week.end.month == DateTime.now().month) {
        result.add(week);
      }
    }
    var weekNow =
        result.where((w) => num.isAfter(w.start) && num.isBefore(w.end));

    return weekNow.first.tuan;
  }

  void loadDataByindexTab() {
    switch (indexTabGolbal.value) {
      case 0:
        loadDsCoQuanLct();
        moths.value = DateTime.now().month;
        getWeekInMoth(moths.value);
        break;
      case 1:
        loadDsCoQuanLct();
        moths.value = DateTime.now().month;
        getWeekInMoth(moths.value);
        break;
      case 2:
        loadDsDonVi();
        moths.value = DateTime.now().month;
        getWeekInMoth(moths.value);
        break;
      case 3:
        loadDanhSachCanBo();
        moths.value = DateTime.now().month;
        getWeekInMoth(moths.value);
        break;
      default:
    }
  }

  late TabController tabController;
  late TextEditingController searchController;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 4, initialIndex: 0, vsync: this);
    searchController = TextEditingController();
  }

  void loadDataInt() {
    getWeekNow();
    moths.value = DateTime.now().month;
    getWeekInMoth(moths.value);
  }

  void changeDonViLCt(DetailDsDonViLCT item) {
    selectedDonVi.value = item;
    loadDataInt();
    loadDsLctDonVi();
    Get.back();
  }

  void changeCaNhanLct(DetailLctDsCb item) {
    selectedCaNhan.value = item;
    loadDataInt();
    loadLichCongTacCaNhan();
    Get.back();
  }
  // lịch công tác cơ quan

  void loadDsCoQuanLct() async {
    await lctProvider.getDsCoQuanLct().then((value) {
      if (value.data!.isNotEmpty) {
        dsCoQuanLct.value = value.data!;
        selectedCoQuan.value = value.data!.first;
      }
    });
  }

  void changeCoQuanLct(DetailDsCoQuanLct item) {
    selectedCoQuan.value = item;
    loadDsLctCoQuan();
    loadDataInt();
    Get.back();
  }

  void changeCoQuanLctLd(DetailDsCoQuanLct item) {
    selectedCoQuan.value = item;
    loadDsLctDonVi();
    loadDataInt();
    Get.back();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    loadDataByindexTab();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
