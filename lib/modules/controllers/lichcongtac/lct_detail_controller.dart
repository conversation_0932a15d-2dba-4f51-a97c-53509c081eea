import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_detail_dv.dart';
import 'package:vnpt_ioffice_camau/app/provider/lct/lct_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';

class LctDetailController extends GetxController
    with StateMixin<List<ShowDetalThu?>> {
  var maLctKc = 0.obs;
  var lctProvider = LctProvider();
  RxList<ShowDetalThu> lctDetailThu = <ShowDetalThu>[].obs;
  void getDetailLctDonVi() async {
    change(null, status: RxStatus.loading());
    await lctProvider.getDetailsLctDonVi(maLctKc.value).then((value) {
      if (value.data!.isNotEmpty) {
        var result =
            MethodUntils.groupBy(value.data!, (item) => item.ngayThucHien);
        result.forEach((key, value) {
          lctDetailThu.add(ShowDetalThu(ngayThucHien: key, data: value));
        });
        change(lctDetailThu, status: RxStatus.success());
      } else {
        change(null, status: RxStatus.empty());
      }
    });
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    try {
      maLctKc.value = Get.arguments['malct'].toInt();
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }

    getDetailLctDonVi();
  }
}
