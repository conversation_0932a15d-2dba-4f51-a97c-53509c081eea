import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/mode_lct_xct_ld.dart';

import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_cot_ld.dart';
import 'package:vnpt_ioffice_camau/app/provider/lct/lct_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';

class LctDetailLdController extends GetxController
    with StateMixin<List<ShowDetalThuLd>> {
  var lctProvider = LctProvider();
  var nam = DateTime.now().year.obs;
  var tuan = 0.obs;
  var maDonViQuantri = 0.obs;
  var listLanhDao = <DetailLctLanhDaoByCot>[].obs;
  var listXemLctLd = <DetailXemLctLanhDao>[];
  var lctDetailThuLd = <ShowDetalThuLd>[];
  var listCotLanhDao = <SoCotLanhDao>[];

  void getXemLichCongTacLanhDao() async {
    change(null, status: RxStatus.loading());
    await lctProvider
        .lctXemLichCongTacLd(nam.value, tuan.value, maDonViQuantri.value)
        .then((value) {
      if (value.data!.isNotEmpty) {
        listXemLctLd = value.data!;
        var result =
            MethodUntils.groupBy(value.data!, (item) => item.ngayThucHien);
        result.forEach((key, value) {
          lctDetailThuLd.add(ShowDetalThuLd(ngayThucHien: key, data: value));
        });
        change(lctDetailThuLd, status: RxStatus.success());
      } else {
        change(null, status: RxStatus.empty());
      }
    });
  }

  void loadLctLanhDaoByCot() async {
    await lctProvider.getLctLanhDaoByCot(maDonViQuantri.value).then((value) {
      if (value.data!.isNotEmpty) {
        listLanhDao.value = value.data!;
        listLanhDao.asMap().forEach((index, value) {
          listCotLanhDao.add(
              SoCotLanhDao(maCot: 'cb$index', tenLanhDao: value.hoVaTenCanBo!));
        });
      }
    });
  }

  String getNameLanhDao(int index) {
    return listLanhDao[index].hoVaTenCanBo ?? "";
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    nam.value = Get.arguments['nam'];
    tuan.value = int.parse(Get.arguments['tuan']);
    maDonViQuantri.value = Get.arguments['maDonViQuanTri'];
    loadLctLanhDaoByCot();
    getXemLichCongTacLanhDao();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
