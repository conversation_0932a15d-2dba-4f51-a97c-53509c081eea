import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_butpheld_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_chitietvt_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbdi/vbdi_chitiet_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbde/vbde_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class TraCuuDetailController extends GetxController {
  final _getStorage = GetStorage();
  var vbdiProvider = VbdiProvider();
  var vbdeProvider = VbdeProvider();
  var maVanBanDi = Get.arguments["maVanBanDiKc"] ?? 0;
  var maVanBanDen = Get.arguments["maVanBanDen"] ?? 0;
  var maXuLyDen = Get.arguments["maXuLyDen"] ?? 0;
  var isLoadData = false.obs;
  var isLoaiVanBan = Get.arguments["isLoaiVanBan"] ?? 0;
  var dataDetail = VbdiChiTiet().obs;
  var dataDetailVbde = VbdeDetail().obs;
  var dsQtxlVbdi = <QtxlVbdi>[].obs;
  var dsButphe = <DsButPheLanhDao>[].obs;

  void loadDetailVanBanDi() async {
    await vbdiProvider.auVbdiChiTiet(maVanBanDi).then((element) {
      isLoadData.value = true;
      if (element.message == 'Lấy dữ liệu thành công') {
        dataDetail.value = element;
      }
    });
  }

  void loadDetailVanBanDen() async {
    int maCtcbkc = _getStorage.read(GetStorageKey.maCtcbKc);
    await vbdeProvider
        .getChiTietVanBanDen(maXuLyDen, maVanBanDen, maCtcbkc)
        .then((value) {
      isLoadData.value = true;
      if (value.message == 'Lấy dữ liệu thành công') {
        dataDetailVbde.value = value.data!;
        maXuLyDen = value.data!.maXuLyDen?.toInt();
        getButPheLanhDao(value.data!.maVanBanDenKc!.toInt(), maXuLyDen);
      }
    });
  }

  void loadQuaTrinhXuLy() async {
    await vbdiProvider.auVbdiQtxl(maVanBanDi).then((value) {
      if (value.message == "Lấy dữ liệu thành công") {
        dsQtxlVbdi.clear();
        dsQtxlVbdi.addAll(value.data!);
      }
    });
  }

  void getButPheLanhDao(int maVanBanDenKc, int maXuLyDenKc) async {
    await vbdeProvider
        .getButPheLanhDao(maVanBanDenKc, maXuLyDenKc)
        .then((dsButPhe) {
      dsButphe.clear();
      dsButphe.addAll(dsButPhe);
    });
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadDetailVanBanDi();
    loadDetailVanBanDen();
    loadQuaTrinhXuLy();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
