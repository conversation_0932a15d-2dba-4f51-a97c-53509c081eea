import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/tracuu/tracuu_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/tracuu/tracuu_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class TraCuuVbdeController extends GetxController
    with StateMixin<List<TraCuuVanBanDen>> {
  final _getStorage = GetStorage();
  var traCuuProvider = TraCuuProvider();
  late TextEditingController inputTrichYeuVbde;
  int size = 20;
  int page = 1;
  int totalPage = 0;
  List<TraCuuVanBanDen> dsVanBanDen = [];
  final List<int> years =
      List<int>.generate(5, (index) => DateTime.now().year - index);
  ScrollController scrollerTraCuuVanBanDen = ScrollController();
  var selectYear = DateTime.now().year.obs;

  void onSwitchPage(int maVanBanDen, int maXulyDen) {
    Get.toNamed(Routers.TRACUUDETAILVBDE, arguments: {
      'maVanBanDen': maVanBanDen,
      'maXuLyDen': maXulyDen,
      'isLoaiVanBan': 0
    });
  }

  void loadDanhSachVanBanDen() async {
    page = 1;
    // số ký hiệu
    print(selectYear.value);
    change(null, status: RxStatus.loading());
    await traCuuProvider
        .auVbdeTraCuu(size, page, inputTrichYeuVbde.text, "", selectYear.value)
        .then((value) {
      totalPage = value.totalPage;
      if (value.data.isNotEmpty) {
        dsVanBanDen.clear();
        dsVanBanDen.addAll(value.data);
      }
      traCuuProvider
          .auVbdeTraCuu(
              size, page, "", inputTrichYeuVbde.text, selectYear.value)
          .then((dsVbdeSoKyHieu) {
        if (dsVbdeSoKyHieu.data.isNotEmpty) {
          dsVanBanDen.addAll(dsVbdeSoKyHieu.data);
        }
        if (dsVanBanDen.isEmpty) {
          change(null, status: RxStatus.empty());
        } else {
          change(dsVanBanDen, status: RxStatus.success());
        }
      });
    });
  }

  void loadMore() {
    try {
      scrollerTraCuuVanBanDen.addListener(() {
        if (scrollerTraCuuVanBanDen.position.maxScrollExtent ==
            scrollerTraCuuVanBanDen.position.pixels) {
          if (page <= totalPage) {
            page++;
            getMoreItem();
          }
        }
      });
    } catch (exception) {}
  }

  void getMoreItem() async {
    Get.dialog(Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SpinKitCircle(
          size: 50,
          color: AppColor.blueAccentColor,
        )
      ],
    ));
    await traCuuProvider
        .auVbdeTraCuu(size, page, inputTrichYeuVbde.text, "", selectYear.value)
        .then((value) {
      Get.back();
      if (value.data.isNotEmpty) {
        dsVanBanDen.addAll(value.data);
        change(dsVanBanDen, status: RxStatus.success());
      }
    });
  }

  @override
  void onInit() {
    super.onInit();
    inputTrichYeuVbde = TextEditingController();
  }

  @override
  void onReady() {
    super.onReady();
    dsVanBanDen.clear();
    change(null, status: RxStatus.empty());
    loadMore();
  }

  @override
  void onClose() {
    super.onClose();
    inputTrichYeuVbde.clear();
    dsVanBanDen.clear();
  }

  @override
  void dispose() {
    super.dispose();
    inputTrichYeuVbde.dispose();
  }
}
