import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/tracuu/tracuu_vbdi_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/tracuu/tracuu_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/vbdi/vbdi_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class TraCuuVbdiController extends GetxController
    with StateMixin<List<TraCuuVanBanDi>> {
  final _getStorage = GetStorage();
  var traCuuVbdi = TraCuuProvider();
  late TextEditingController inputSoKyHieu;
  late TextEditingController inputTrichYeu;

  final List<int> years =
      List<int>.generate(5, (index) => DateTime.now().year - index);
  ScrollController scrollerTraCuuVanBanDen = ScrollController();
  var selectYear = DateTime.now().year.obs;

  var isLoadData = false.obs;
  int size = 20;
  int page = 1;
  int totalPage = 0;
  ScrollController scrollerTraCuuVanBanDi = ScrollController();

  List<TraCuuVanBanDi> dsVanBanDi = [];

  void onSwitchPage(int maVanBanDi) {
    Get.toNamed(Routers.TRACUUDETAILVBDI,
        arguments: {'maVanBanDiKc': maVanBanDi, 'isLoaiVanBan': 1});
  }

  void searchVanBanDi() async {
    try {
      page = 1;
      dsVanBanDi.clear();
      change(null, status: RxStatus.loading());
      await traCuuVbdi
          .auVbdiTraCuu(
              page, size, 'vt', inputTrichYeu.text, "", selectYear.value)
          .then((data) {
        if (data.message == "Lấy dữ liệu thành công") {
          totalPage = data.totalPage;
          if (data.data.isNotEmpty) {
            dsVanBanDi.clear();
            dsVanBanDi.addAll(data.data);
          }
          traCuuVbdi
              .auVbdiTraCuu(
                  page, size, 'vt', "", inputTrichYeu.text, selectYear.value)
              .then(
            (value) {
              dsVanBanDi.addAll(data.data);
              if (dsVanBanDi.isNotEmpty) {
                change(dsVanBanDi, status: RxStatus.success());
              } else {
                change(null, status: RxStatus.empty());
              }
            },
          );
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.empty());
    }
  }

  void loadMore() {
    try {
      scrollerTraCuuVanBanDi.addListener(() {
        if (scrollerTraCuuVanBanDi.position.maxScrollExtent ==
            scrollerTraCuuVanBanDi.position.pixels) {
          if (page <= totalPage) {
            page++;
            getMoreItem();
          }
        }
      });
    } catch (exception) {}
  }

  void getMoreItem() async {
    Get.dialog(Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SpinKitCircle(
          size: 50,
          color: AppColor.blueAccentColor,
        )
      ],
    ));
    await traCuuVbdi
        .auVbdiTraCuu(page, size, 'vt', inputTrichYeu.text, inputSoKyHieu.text,
            selectYear.value)
        .then((value) {
      Get.back();
      if (value.data.isNotEmpty) {
        dsVanBanDi.addAll(value.data);
        change(dsVanBanDi, status: RxStatus.success());
      }
    });
  }

  @override
  void onInit() {
    super.onInit();
    change(null, status: RxStatus.empty());
    inputSoKyHieu = TextEditingController();
    inputTrichYeu = TextEditingController();
  }

  @override
  void onReady() {
    super.onReady();
    change(null, status: RxStatus.empty());
    dsVanBanDi.clear();
    loadMore();
  }

  @override
  void onClose() {
    super.onClose();
    change(null, status: RxStatus.empty());
    isLoadData.value = false;
    dsVanBanDi.clear();
  }

  @override
  void dispose() {
    super.dispose();
    inputTrichYeu.dispose();
  }
}
