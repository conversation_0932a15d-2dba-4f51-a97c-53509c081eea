import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_detail_controller.dart';

class TraCuuBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TraCuuVbdiController());
  }
}

class TraCuuVbdeBingding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TraCuuVbdeController());
  }
}

class TraCuuDetailBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TraCuuDetailController());
  }
}
