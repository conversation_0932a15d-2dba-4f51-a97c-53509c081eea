import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_detail_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_xuly_controller.dart';

class ThongTinDieuHanhBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ThongTinDieuHanhController());
  }
}

class TtdhetailBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TTDHDetailController());
  }
}

class TttdXuLyBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TtdhXuLyController());
  }
}
