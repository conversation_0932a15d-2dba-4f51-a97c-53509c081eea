import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_detail.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_model.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_xuly.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/tree_cb_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/ttdh/ttdh_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/utils/modal_bottom.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/global_widget/tree_mode.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class TtdhXuLyController extends GetxController
    with SingleGetTickerProviderMixin {
  var ttdhProvider = TtdhProvider();
  var _getStorage = GetStorage();
  var fileProvide = FileProvider();
  late TextEditingController tieuDe;
  late TextEditingController noiDung;
  late TextEditingController noiDungChuyenTiep;
  late TextEditingController loaiThongDiep;
  var thongDiepSelect = LoaiThongDiep().obs;
  var srcFileTtdhDinhKem = "".obs;
  var listDanhSachChiTiet = Get.arguments['listDanhSachChiTiet'];
  List<DsCanBoTtdh> listNguoiNhan = <DsCanBoTtdh>[];
  var isLoaiTTDH = Get.arguments['isLoaiTTDH'] ?? 0;
  ThongTinDieuHanhNhan item =
      Get.arguments['itemTtdh'] ?? ThongTinDieuHanhNhan();
  Rx<bool> isSms = false.obs;
  var dsLoaiThongDiep = <LoaiThongDiep>[].obs;
  Rx<File?> selectedFile = File("").obs;
  Rx<String?> selectedFileName = "".obs;
  Rx<String?> base64FileDinhKem = "".obs;

  var arrayTreecvChuyen = <TreeDetail>[];
  List<String> selectedXDBArr = <String>[].obs;
  var arrayNhomDVN = <TreeDetail>[];
  var treeNhomDVN = TreeNodes(title: "", children: []).obs;
  late TabController tabTreeController;
  List<Tab> tabsTree = [
    const Tab(text: "Cán bộ"),
    const Tab(text: "Nhóm cán bộ"),
  ];

  var treeDsCbChuyenVbde = TreeNodes(title: "", children: []).obs;
  void changeIsSms(bool value) {
    isSms.value = value;
  }

  void getDanhSachNguoiNhan() async {
    await ttdhProvider
        .auTTDHdsCanBoNhan(item.maTtdhKc!.toInt())
        .then((dsCanBoNhan) {
      if (dsCanBoNhan.data!.isNotEmpty) {
        listNguoiNhan.addAll(dsCanBoNhan.data!);
      }
    });
  }

  void changeSelectLoaiThongDiep(LoaiThongDiep item) {
    thongDiepSelect.value = item;
    loaiThongDiep.text = item.tenLoaiTtdh.toString();
    Get.back();
  }

  void loadDanhSachThongDiep() async {
    await ttdhProvider.auTTDHlayDsLoaiThongDiep().then((value) {
      dsLoaiThongDiep.value = value.data!;
      thongDiepSelect.value = value.data![0];
      loaiThongDiep.text = thongDiepSelect.value.tenLoaiTtdh ?? "";
    });
  }

  void loadDsCanBoNhan() async {
    List<Map<String, String>> arrayDsCbChuyenTTDH = [];
    arrayDsCbChuyenTTDH.clear();
    await ttdhProvider.auTTDHDSCBN().then((value) {
      if (value.data!.isNotEmpty) {
        value.data!.forEach((item) {
          arrayDsCbChuyenTTDH.add({
            'id': item.id.toString(),
            'parent': item.parent.toString(),
            'value': item.text.toString(),
            'hoVaTen':
                item.liAttr == null ? "" : item.liAttr!.dataTenCanBo.toString(),
            'diDong':
                item.liAttr == null ? "" : item.liAttr!.dataPhone.toString(),
            'tenChucVu':
                item.liAttr == null ? "" : item.liAttr!.dataChucVu.toString(),
          });
        });
        arrayTreecvChuyen.addAll(value.data!);
      }
    });
    arrayDsCbChuyenTTDH.isNotEmpty
        ? treeDsCbChuyenVbde.value =
            MethodUntils.listToTrees(arrayDsCbChuyenTTDH)
        : null;
  }

  void loadDsNhomCanBoNhan() async {
    int maCanBo = _getStorage.read(GetStorageKey.maCanBo);
    List<Map<String, String>> arrayNhomCBN = [];
    arrayNhomCBN.clear();
    await ttdhProvider.auTTDHDSNCBN().then((value) {
      if (value.data!.isNotEmpty) {
        value.data!.forEach((element) {
          arrayNhomCBN.add({
            'id': element.id.toString(),
            'parent': element.parent.toString(),
            'value': element.text.toString(),
            'hoVaTen': element.liAttr!.dataTenCanBo.toString(),
            'diDong': element.liAttr!.dataPhone.toString(),
            'tenChucVu': element.liAttr!.dataChucVu.toString(),
          });
        });
        arrayNhomDVN.addAll(value.data!);
      }
      arrayNhomCBN.isNotEmpty
          ? treeNhomDVN.value = MethodUntils.listToTrees(arrayNhomCBN)
          : null;
    });
  }

  void checkAllxdb(String id, bool value) {
    if (selectedXDBArr.contains(id)) {
      selectedXDBArr.remove(id);
    } else {
      if (value) {
        selectedXDBArr.add(id);
      } else {
        selectedXDBArr.remove(id);
      }
    }
    var arrayChildren =
        arrayTreecvChuyen.where((element) => element.parent == id);
    for (var item in arrayChildren) {
      bool check = false;
      if (selectedXDBArr.isNotEmpty) {
        check = selectedXDBArr.contains(item.id);
      }
      var children = arrayTreecvChuyen.where((el) => el.parent == item.id);
      if (children.isNotEmpty) {
        if (selectedXDBArr.contains(item.id)) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
        for (var item2 in children) {
          checkAllxdb(item2.id!, value);
        }
      } else {
        if (check) {
          selectedXDBArr.remove(item.id);
        } else {
          if (value) {
            selectedXDBArr.add(item.id!);
          } else {
            selectedXDBArr.remove(item.id);
          }
        }
      }
    }
  }

  // danh sachs can bộ đã chọn
  List<DetailCBTree> getCBchoosedCv() {
    List<DetailCBTree> arrayResult = <DetailCBTree>[];
    List<TreeDetail> arrayDSCB = MethodUntils()
        .removeDuplicates([...arrayTreecvChuyen, ...arrayNhomDVN])
        .where((element) => element.id!.indexOf('CB') != -1)
        .toList();

    selectedXDBArr.forEach((item) {
      var detalCbph = arrayDSCB.firstWhere(
          (element) => element.id == item && item!.indexOf('CB') != -1,
          orElse: () => TreeDetail());
      if (detalCbph.id != null) {
        arrayResult.add(DetailCBTree(
            detalCbph.id,
            detalCbph.liAttr!.dataTenCanBo,
            detalCbph.liAttr!.dataChucVu,
            detalCbph.liAttr!.dataPhone,
            '2',
            "Xử lý chính"));
      }
    });
    return arrayResult;
  }

  // single file
  void pickFile() async {
    final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'png',
          'jpg',
          'odt',
          'xlsx',
          'xls'
        ]);
    if (result == null) return null;
    selectedFile.value = File(result.files.first.path!);
    selectedFileName.value = result.files.first.name;
    var bytesFile = await selectedFile.value!.readAsBytes();
    base64FileDinhKem.value = base64Encode(bytesFile);
  }

  void setStateFile() {
    selectedFile = File("").obs;
    selectedFileName.value = "";
    base64FileDinhKem.value = "";
  }

  void upLoadFileDinhKem() async {
    if (selectedFile.value!.path == "") {
      Get.defaultDialog(
        title: "Thông báo",
        middleText: "Bạn chưa chọn tập tin!",
        textCancel: "Ok",
        confirmTextColor: Colors.white,
        onCancel: () {
          Get.back();
        },
      );
    } else {
      var maCtcbKc = _getStorage.read(GetStorageKey.maCtcbKc);
      String chucNang = "thongtindieuhanh";
      await fileProvide.auFileCDKF(selectedFile.value!, chucNang).then((value) {
        if (value.data!.duocLuu == 1.0) {
          fileProvide
              .auFileTL(selectedFile.value!, chucNang, selectedFileName.value!)
              .then((item) {
            if (srcFileTtdhDinhKem.value == "") {
              srcFileTtdhDinhKem.value = item;
            } else {
              srcFileTtdhDinhKem.value = srcFileTtdhDinhKem.value + ":" + item;
            }
            selectedFile = File("").obs;
            selectedFileName.value = "";
            Get.back();
          });
        } else {
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Không đủ không gian lưu trữ!");
        }
      });
      // gọi hàm upload file
    }
  }

  void delFileDinhKem(String file) {
    srcFileTtdhDinhKem.value = srcFileTtdhDinhKem.value.replaceAll(file, "");
  }

  void onPressReply() {
    if (tieuDe.text == "" || noiDung.text == "") {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Vui lòng nhập đầy đủ thông tin!");
    } else {
      List<Widget> listWidgetCanBoGui = [];
      List<dynamic> listCanBoGui = [];

      listWidgetCanBoGui.add(Container(
        decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
        child: ListTile(
          title: Text(
            item.tenNguoiGui.toString(),
            style: const TextStyle(color: Colors.black),
          ),
        ),
      ));

      listCanBoGui.add({
        'maCtcbGui': item.maCtcbTao,
        'tenCanBo': item.tenNguoiGui,
        'diDongCanBo': ""
      });
      CustomModalButton.modalButton(
          context: Get.context,
          listItem: listWidgetCanBoGui,
          sendMethod: () {
            onSendReply(listCanBoGui);
          },
          titleBtnSend: "Gửi",
          titleBtnCancel: "Đóng",
          title: "Danh sách cán bộ đã nhận");
    }
  }

  void onSendReply(dynamic listCanBoGui) async {
    // chuyển phản hồi
    String chuoiMaCtcbNhan = listCanBoGui[0]['maCtcbGui'].toInt().toString();
    String? chuoiMaVblq = "";
    String? chuoiSdtNhan = listCanBoGui[0]['diDongCanBo'];
    int maCtcbTao = _getStorage.read(GetStorageKey.maCtcbKc);
    int maLoaiTtdh = thongDiepSelect.value.maLoaiTtdhKc?.toInt() ?? 1;
    int maTtdhGoc = item.maTtdhGoc!.toInt();
    String paramNoiDung = noiDung.text;
    String? srcVanbanLienQuan = "";
    String? srcFileTtdh = srcFileTtdhDinhKem.value;
    String paramTieuDe = tieuDe.text;
    int traLoiChoTtdh = item.maTtdhKc?.toInt() ?? 0;
    await ttdhProvider
        .auTTDHGuiThongDiep(
            chuoiMaCtcbNhan,
            chuoiMaVblq,
            chuoiSdtNhan,
            maCtcbTao,
            maLoaiTtdh,
            maTtdhGoc,
            paramNoiDung,
            srcVanbanLienQuan,
            srcFileTtdh,
            paramTieuDe,
            traLoiChoTtdh,
            null,
            null)
        .then((value) {
      if (value.id! > 0) {
        CustomSnackBar.showSuccessSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Phản hồi thành công!");
        tieuDe.clear();
        noiDung.clear();
        Get.offNamed(Routers.TTDH);
      }
    });
  }

  void onPressReplyAll() async {
    if (tieuDe.text == "" || noiDung.text == "") {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Vui lòng nhập đầy đủ thông tin!");
    } else {
      List<Widget> listWidgetCanBoGui = [];
      List<dynamic> listCanBoGui = [];
      bool flag = true;

      for (var element in listNguoiNhan) {
        listCanBoGui.add({
          'maCtcbGui': element.maCtcbKc,
          'tenCanBo': element.hoVaTenCanBo,
          'diDongCanBo': element.diDongCanBo
        });
        if (element.maCtcbKc == item.maCtcbTao) {
          flag = false;
        }
      }
      if (flag) {
        listCanBoGui.add({
          'maCtcbGui': item.maCtcbTao,
          'tenCanBo': item.tenNguoiGui,
          'diDongCanBo': "",
        });
      }
      var listCanBoGui1 = listCanBoGui.where((element) =>
          element['maCtcbGui'] != _getStorage.read(GetStorageKey.maCtcbKc));

      for (var element in listCanBoGui1) {
        listWidgetCanBoGui.add(Container(
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
          child: ListTile(
            title: Text(
              element['tenCanBo'].toString(),
              style: const TextStyle(color: Colors.black),
            ),
          ),
        ));
      }
      CustomModalButton.modalButton(
          context: Get.context,
          listItem: listWidgetCanBoGui,
          sendMethod: () {
            onSendReplyAll(listCanBoGui1);
          },
          titleBtnSend: "Gửi",
          titleBtnCancel: "Đóng",
          title: "Danh sách cán bộ đã nhận");
    }
  }

  void onSendReplyAll(dynamic listCanBoGui) async {
    // chuyển phản hồi
    String chuoiMaCtcbNhan = "";

    String? chuoiMaVblq = "";
    String? chuoiSdtNhan = "";
    for (var element in listCanBoGui) {
      chuoiMaCtcbNhan =
          chuoiMaCtcbNhan! + element['maCtcbGui'].toInt().toString() + ";";
      chuoiSdtNhan = chuoiSdtNhan! + element['diDongCanBo'].toString() + ";";
    }
    int maCtcbTao = _getStorage.read(GetStorageKey.maCtcbKc);
    int maLoaiTtdh = thongDiepSelect.value.maLoaiTtdhKc?.toInt() ?? 1;
    int maTtdhGoc = item.maTtdhGoc!.toInt();
    String paramNoiDung = noiDung.text;
    String? srcVanbanLienQuan = "";
    String? srcFileTtdh = srcFileTtdhDinhKem.value;
    String paramTieuDe = tieuDe.text;
    int traLoiChoTtdh = item.maTtdhKc?.toInt() ?? 0;
    await ttdhProvider
        .auTTDHGuiThongDiep(
            chuoiMaCtcbNhan,
            chuoiMaVblq,
            chuoiSdtNhan,
            maCtcbTao,
            maLoaiTtdh,
            maTtdhGoc,
            paramNoiDung,
            srcVanbanLienQuan,
            srcFileTtdh,
            paramTieuDe,
            traLoiChoTtdh,
            null,
            null)
        .then((value) {
      if (value.id! > 0) {
        CustomSnackBar.showSuccessSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Phản hồi thành công!");
        tieuDe.clear();
        noiDung.clear();
        Get.offNamed(Routers.TTDH);
      }
    });
  }

  void onPressForward() async {
    if (tieuDe.text == "" ||
        noiDung.text == "" ||
        noiDungChuyenTiep.text == "") {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo!",
          message: "Vui lòng nhập nội dung!");
    } else {
      List<DetailCBTree> arrayCB = getCBchoosedCv();
      List<Widget> listCBdaChon = <Widget>[];
      for (var item in arrayCB) {
        listCBdaChon.add(Container(
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
          child: ListTile(
            title: Text(item.ten.toString()),
            subtitle: Text("Chức vụ: ${item.tenChucVu}"),
          ),
        ));
      }
      var isCheck = arrayCB.isNotEmpty;
      if (isCheck) {
        CustomModalButton.modalButton(
            context: Get.context,
            listItem: listCBdaChon,
            sendMethod: () {
              onSendForward();
            },
            title: "Danh sách cán bộ đã chọn");
      } else {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo!",
            message: "Vui lòng chọn cán bộ!");
      }
    }
  }

  void onSendForward() async {
    selectedXDBArr =
        selectedXDBArr.where((element) => element.contains('CB')).toList();
    var chuoiMaCtcbNhan =
        selectedXDBArr.map((e) => e.replaceAll(r'CB', '')).join(';');
    String noiDungEnCode = MethodUntils.htmlEncode(noiDungChuyenTiep.text +
        "<br><br>- Chuyển tiếp từ: <br>" +
        noiDung.text);
    int maTTDHkc = item.maTtdhKc!.toInt();
    String? chuoiMaVblq = "";
    int maCtcbTao = _getStorage.read(GetStorageKey.maCtcbKc);
    int maLoaiTtdh = thongDiepSelect.value.maLoaiTtdhKc?.toInt() ?? 1;
    int maTtdhGoc = item.maTtdhGoc!.toInt();
    String? srcVanbanLienQuan = "";
    String? srcFileTtdh = srcFileTtdhDinhKem.value;
    String paramTieuDe = tieuDe.text;
    int traLoiChoTtdh =
        item.traLoiChoTtdh == null ? 0 : item.traLoiChoTtdh!.toInt();
    int chuyenTiepTuTtdh =
        item.chuyenTiepTuTtdh == null ? 0 : item.chuyenTiepTuTtdh!.toInt();
    int sms = isSms.value ? 1 : 0;
    await ttdhProvider
        .auTTDHGuiThongDiep(
            chuoiMaCtcbNhan,
            chuoiMaVblq,
            null,
            maCtcbTao,
            maLoaiTtdh,
            maTtdhGoc,
            noiDungEnCode,
            srcVanbanLienQuan,
            srcFileTtdh,
            paramTieuDe,
            traLoiChoTtdh,
            maTTDHkc,
            chuyenTiepTuTtdh)
        .then((value) {
      if (value.id! > 0) {
        CustomSnackBar.showSuccessSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Phản hồi thành công!");
        tieuDe.clear();
        noiDung.clear();
        srcFileTtdh = "";
        srcVanbanLienQuan = "";
        Get.off(Routers.THONGTINDIEUHANHDETAIL);
        Get.offNamed(Routers.TTDH);
      }
    });
  }

  void onPressAddd() async {
    if (tieuDe.text == "" || noiDung.text == "") {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo!",
          message: "Vui lòng nhập nội dung!");
    } else {
      List<DetailCBTree> arrayCB = getCBchoosedCv();
      List<Widget> listCBdaChon = <Widget>[];
      for (var item in arrayCB) {
        listCBdaChon.add(Container(
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
          child: ListTile(
            title: Text(item.ten.toString()),
            subtitle: Text("Chức vụ: ${item.tenChucVu}"),
          ),
        ));
      }
      var isCheck = arrayCB.isNotEmpty;
      if (isCheck) {
        CustomModalButton.modalButton(
            context: Get.context,
            listItem: listCBdaChon,
            sendMethod: () {
              onSendAdd();
            },
            title: "Danh sách cán bộ đã chọn");
      } else {
        CustomSnackBar.showWarningSnackBar(
            context: Get.context,
            title: "Thông báo!",
            message: "Vui lòng chọn cán bộ!");
      }
    }
  }

  void onSendAdd() async {
    selectedXDBArr =
        selectedXDBArr.where((element) => element.contains('CB')).toList();
    var chuoiMaCtcbNhan =
        selectedXDBArr.map((e) => e.replaceAll(r'CB', '')).join(';');
    String noiDungEnCode = noiDung.text;

    String? chuoiMaVblq = "";
    int maCtcbTao = _getStorage.read(GetStorageKey.maCtcbKc);
    int maLoaiTtdh = thongDiepSelect.value.maLoaiTtdhKc?.toInt() ?? 1;

    String? srcVanbanLienQuan = "";
    String? srcFileTtdh = srcFileTtdhDinhKem.value;
    String paramTieuDe = tieuDe.text;
    int traLoiChoTtdh =
        item.traLoiChoTtdh == null ? 0 : item.traLoiChoTtdh!.toInt();
    int chuyenTiepTuTtdh =
        item.chuyenTiepTuTtdh == null ? 0 : item.chuyenTiepTuTtdh!.toInt();
    int sms = isSms.value ? 1 : 0;
    await ttdhProvider
        .auTTDHGuiThongDiep(
            chuoiMaCtcbNhan,
            chuoiMaVblq,
            null,
            maCtcbTao,
            maLoaiTtdh,
            null,
            noiDungEnCode,
            srcVanbanLienQuan,
            srcFileTtdh,
            paramTieuDe,
            traLoiChoTtdh,
            null,
            chuyenTiepTuTtdh)
        .then((value) {
      if (value.id! > 0) {
        CustomSnackBar.showSuccessSnackBar(
            context: Get.context,
            title: "Thông báo",
            message: "Thêm mới thành công!");
        tieuDe.clear();
        noiDung.clear();
        srcFileTtdh = "";
        srcVanbanLienQuan = "";

        Get.offNamed(Routers.TTDH);
      }
    });
  }

  @override
  void onInit() {
    super.onInit();
    tieuDe = TextEditingController();
    noiDung = TextEditingController();
    loaiThongDiep = TextEditingController();
    noiDungChuyenTiep = TextEditingController();
    tabTreeController = TabController(length: tabsTree.length, vsync: this);
  }

  @override
  void onReady() {
    super.onReady();
    loadDanhSachThongDiep();

    tieuDe.text =
        listDanhSachChiTiet != null ? listDanhSachChiTiet[0].tieuDe : "";
    if (isLoaiTTDH == 2) {
      noiDung.text = MethodUntils.removeNoiDungTTDH(item.noiDung.toString());
      getDanhSachNguoiNhan();
    }

    /// isLoaiTTDH :0 phản hồi,
    /// isLoaiTTDH: 1: phản hồi tất cả,
    /// isLoaiTTDH: 2: chuyển tiếp
    /// isLoaiTTDH 3: thêm mới
    loadDsCanBoNhan();
    loadDsNhomCanBoNhan();
  }

  @override
  void onClose() {
    super.onClose();
    tabTreeController.dispose();
    loaiThongDiep.dispose();
    noiDung.dispose();
    tieuDe.dispose();
  }

  @override
  void dispose() {
    tabTreeController.dispose();
    loaiThongDiep.dispose();
    noiDung.dispose();
    tieuDe.dispose();
  }
}
