import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_detail.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/ttdh/ttdh_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/modal_showItem.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class TTDHDetailController extends GetxController {
  var ttdhProvider = TtdhProvider();
  var maTtdhguiKc = Get.arguments["maTtdhguiKc"] ?? 0;
  var maTtdhKc = Get.arguments["maTtdhKc"] ?? 0;
  var indexTab = Get.arguments["indexTab"] ?? 0;
  ThongTinDieuHanhNhan item = Get.arguments["item"] ?? ThongTinDieuHanhNhan();
  var store = GetStorage();
  var nguoiNhan = DsCanBoTtdh().obs;
  var listNguoiNhan = <DsCanBoTtdh>[];
  var listDanhSachChiTiet = <TTDHChiTiet>[].obs;

  void loadThongTtdhByIndex() {
    if (indexTab == 0) {
      loadChiTietTtdhNhan(maTtdhguiKc);
    } else {
      getDanhSachNguoiNhan();
    }
  }

  void getDanhSachNguoiNhan() async {
    await ttdhProvider.auTTDHdsCanBoNhan(maTtdhKc).then((dsCanBoNhan) {
      if (dsCanBoNhan.data!.isNotEmpty) {
        nguoiNhan.value = dsCanBoNhan.data!.first;
        loadChiTietTtdh(dsCanBoNhan.data![0].maCtcbKc!.toInt(),
            dsCanBoNhan.data![0].maCtcbGui!.toInt());
        listNguoiNhan.addAll(dsCanBoNhan.data!);
      }
    });
  }

  void loadChiTietTtdh(int maCtcbGui, int maCtcbNhan) async {
    await ttdhProvider
        .auTDTTDHChiTiet(maTtdhKc, maCtcbGui, maCtcbNhan)
        .then((value) {
      listDanhSachChiTiet.value = value.data!;
    });
  }

  void loadChiTietTtdhNhan(int maTtdhGuiKc) async {
    int maCanBo = store.read(GetStorageKey.maCanBo);
    await ttdhProvider.auTTTTDHChiTietNhan(maTtdhGuiKc, maCanBo).then((value) {
      listDanhSachChiTiet.value = value.data!;
    });
  }

  Future<Widget> getNguoiNhan(int maTTDHKc) async {
    Widget result = RichText(
      text: const TextSpan(text: ""),
    );
    await ttdhProvider.auTTDHdsCanBoNhan(maTTDHKc).then((dsCanBoNhan) {
      if (dsCanBoNhan.data!.isNotEmpty) {
        int dataCount = dsCanBoNhan.data!.length;
        int numbershow = 20;
        if (dataCount <= numbershow) {
          result = RichText(
              text: TextSpan(
                  children: dsCanBoNhan.data!
                      .map((e) => TextSpan(
                          text: "${e.hoVaTenCanBo},",
                          style: const TextStyle(color: Colors.red)))
                      .toList()));
        } else {
          List<TextSpan> itemList = [];
          for (var element in dsCanBoNhan.data!) {
            int index = dsCanBoNhan.data!.indexOf(element);
            itemList.add(TextSpan(
                text: "${element.hoVaTenCanBo},",
                style: const TextStyle(color: Colors.red)));
            if (index == (numbershow)) {
              itemList.add(TextSpan(
                  text: "+${dataCount - numbershow}",
                  style: const TextStyle(color: Colors.grey)));
              break;
            }
          }
          List<Widget> listCBdanhan = <Widget>[];
          for (var element2 in dsCanBoNhan.data!) {
            listCBdanhan.add(Container(
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(color: Colors.grey, width: 1.0))),
              child: ListTile(
                title: Text(
                  element2.hoVaTenCanBo.toString(),
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ));
          }
          result = GestureDetector(
            child: RichText(text: TextSpan(children: itemList)),
            onTap: () {
              CustomModalShowItem.modalShowItem(
                  context: Get.context,
                  listItem: listCBdanhan,
                  title: "Danh sách cán bộ đã nhận");
            },
          );
        }
      }
    });
    return result;
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadThongTtdhByIndex();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
