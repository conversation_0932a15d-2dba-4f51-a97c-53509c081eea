import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/app/model/ttdh/ttdh_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/ttdh/ttdh_provider.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ThongTinDieuHanhController extends GetxController
    with SingleGetTickerProviderMixin, StateMixin<List<ThongTinDieuHanhNhan>> {
  late TabController tabController;
  var ttdhProvider = TtdhProvider();
  int page = 1;
  int size = 20;
  int totalPage = 0;
  var isLoadData = false.obs;
  var indexTab = 0.obs;
  List<ThongTinDieuHanhNhan> listTtdhDaNhan = [];
  List<ThongTinDieuHanh<PERSON>han> listTtdhDaGui = [];
  ScrollController scrollerTTDHdaNhan = ScrollController();
  ScrollController scrollerTTDHdaGui = ScrollController();
  var searchKey = "".obs;

  void searchThongTinDieuHanh(String keyWork) {
    searchKey.value = keyWork;
    switch (indexTab.value) {
      case 0:
        loadDsTtdhDaNhan();
        loadMoreDs();
        break;
      case 1:
        loadDsTtdhDaGui();
        loadMoreDs();
        break;
      default:
    }
  }

  void changeIndexTab(int index) {
    indexTab.value = index;
    searchThongTinDieuHanh(searchKey.value);
  }

  void loadDsTtdhDaNhan() async {
    listTtdhDaNhan.clear();
    change(null, status: RxStatus.empty());
    await ttdhProvider.auTtdhDaNhan(page, size, searchKey.value).then((value) {
      if (value.data.isNotEmpty) {
        listTtdhDaNhan.addAll(value.data);
        change(listTtdhDaNhan, status: RxStatus.success());
        isLoadData.value = true;
        // lấy số lương row, để tính số lượng page
        ttdhProvider.auTtdhDaNhan(0, size, searchKey.value).then((total) {
          double totalrow = (total.data![0].totalRow!.toInt() ?? 1.0) / page;
          totalPage = totalrow.floor();
        });
      }
    });
  }

  void loadDsTtdhDaGui() async {
    listTtdhDaGui.clear();
    change(null, status: RxStatus.empty());
    await ttdhProvider
        .auThongTinDHdaGui(page, size, searchKey.value, "")
        .then((dsTTDHdaGui) {
      if (dsTTDHdaGui.data.isNotEmpty) {
        listTtdhDaGui.addAll(dsTTDHdaGui.data);
        change(listTtdhDaGui, status: RxStatus.success());
        totalPage = dsTTDHdaGui.totalPage!.toInt();
        isLoadData.value = true;
      }
    });
  }

  void loadMoreDs() async {
    try {
      switch (indexTab.value) {
        case 0:
          scrollerTTDHdaNhan.addListener(() {
            if (scrollerTTDHdaNhan.position.maxScrollExtent ==
                scrollerTTDHdaNhan.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDsLdDuyet(indexTab.value);
              }
            }
          });
          break;
        case 1:
          scrollerTTDHdaGui.addListener(() {
            if (scrollerTTDHdaGui.position.maxScrollExtent ==
                scrollerTTDHdaGui.position.pixels) {
              if (page <= totalPage) {
                page++;
                getMoreDsLdDuyet(indexTab.value);
              }
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void getMoreDsLdDuyet(int indexTab) async {
    try {
      Get.dialog(Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            size: 20,
            color: AppColor.blueAccentColor,
          )
        ],
      ));
      switch (indexTab) {
        case 0:
          await ttdhProvider
              .auTtdhDaNhan(page, size, searchKey.value)
              .then((value) {
            Get.back();
            if (value.data.isNotEmpty) {
              listTtdhDaNhan.addAll(value.data);
              change(listTtdhDaNhan, status: RxStatus.success());
              isLoadData.value = true;
              // lấy số lương row, để tính số lượng page
            }
          });
          break;
        case 1:
          await ttdhProvider
              .auThongTinDHdaGui(page, size, searchKey.value, "")
              .then((dsTTDHdaGui) {
            Get.back();
            if (dsTTDHdaGui.data.isNotEmpty) {
              listTtdhDaGui.addAll(dsTTDHdaGui.data);
              change(listTtdhDaGui, status: RxStatus.success());
              isLoadData.value = true;
            }
          });
          break;
        default:
      }
    } catch (exception) {}
  }

  void onPressToPageDetail(int? maTtdhKc, int? maTtdhGuikc, int? indexTab,
      ThongTinDieuHanhNhan item) {
    Get.toNamed(Routers.THONGTINDIEUHANHDETAIL, arguments: {
      'maTtdhKc': maTtdhKc,
      'maTtdhguiKc': maTtdhGuikc,
      'indexTab': indexTab,
      'item': item
    });
  }

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, initialIndex: 0, vsync: this);
  }

  @override
  void onReady() {
    super.onReady();
    searchThongTinDieuHanh("");
  }

  @override
  void onClose() {
    super.onClose();
  }
}
