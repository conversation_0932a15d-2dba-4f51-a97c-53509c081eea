import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class SettingsController extends GetxController {
  final GetStorage _storage = GetStorage();
  
  // Key để lưu cỡ chữ trong storage
  static const String _fontSizeKey = 'app_font_size';
  
  // Các cỡ chữ có sẵn
  final Map<String, double> fontSizes = {
    'Nhỏ': 12.0,
    'Vừa': 14.0,
    'Lớn': 16.0,
    'Rất lớn': 18.0,
  };
  
  // Observable cho cỡ chữ hiện tại
  var currentFontSize = 14.0.obs;
  var currentFontSizeName = 'Vừa'.obs;
  
  @override
  void onInit() {
    super.onInit();
    _loadFontSize();
  }
  
  // Load cỡ chữ từ storage
  void _loadFontSize() {
    double savedFontSize = _storage.read(_fontSizeKey) ?? 14.0;
    currentFontSize.value = savedFontSize;
    
    // Tì<PERSON> tên cỡ chữ tương ứng
    String fontSizeName = 'Vừa';
    fontSizes.forEach((name, size) {
      if (size == savedFontSize) {
        fontSizeName = name;
      }
    });
    currentFontSizeName.value = fontSizeName;
  }
  
  // Thay đổi cỡ chữ
  void changeFontSize(String fontSizeName) {
    double fontSize = fontSizes[fontSizeName] ?? 14.0;
    currentFontSize.value = fontSize;
    currentFontSizeName.value = fontSizeName;
    
    // Lưu vào storage
    _storage.write(_fontSizeKey, fontSize);
    
    // Hiển thị thông báo
    Get.snackbar(
      'Thành công',
      'Đã thay đổi cỡ chữ thành "$fontSizeName"',
      backgroundColor: Colors.green[600],
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 2),
    );
  }
  
  // Reset về cỡ chữ mặc định
  void resetFontSize() {
    changeFontSize('Vừa');
  }
  
  // Lấy TextStyle với cỡ chữ hiện tại
  TextStyle getTextStyle({
    FontWeight? fontWeight,
    Color? color,
    double? customSize,
  }) {
    return TextStyle(
      fontSize: customSize ?? currentFontSize.value,
      fontWeight: fontWeight,
      color: color,
    );
  }
  
  // Lấy cỡ chữ cho tiêu đề (lớn hơn 2px)
  double get titleFontSize => currentFontSize.value + 2;
  
  // Lấy cỡ chữ cho subtitle (nhỏ hơn 2px)
  double get subtitleFontSize => currentFontSize.value - 2;
  
  // Lấy cỡ chữ cho caption (nhỏ hơn 4px)
  double get captionFontSize => currentFontSize.value - 4;
}
