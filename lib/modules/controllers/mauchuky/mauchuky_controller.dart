import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/app/model/mauchuky/mau_chu_ky_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/file/file_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/mauchuky/mauchuky_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class MauChuKyController extends GetxController
    with StateMixin<List<DetailMauChuKy?>> {
  var mauChuKyProvider = MauChuKyProvider();
  var fileProvider = FileProvider();
  Rx<File?> selectedFileCks = File("").obs;
  Rx<String?> selectedFileName = "".obs;
  Rx<String?> base64FileDinhKem = "".obs;
  var store = GetStorage();
  Rx<File?> selectedChoseFileCks = File("").obs;
  Rx<String?> selectedChoseFileName = "".obs;
  TextEditingController tenChuKyEditingController = TextEditingController();

  void pickFile() async {
    final result = await FilePicker.platform.pickFiles(type: FileType.image);
    if (result == null) return null;
    selectedFileCks.value = File(result.files.first.path!);
    selectedFileName.value = result.files.first.name;
    var bytesFile = await selectedFileCks.value!.readAsBytes();
    base64FileDinhKem.value = base64Encode(bytesFile);
  }

  void getDanhSachMauChuky() async {
    change(null, status: RxStatus.loading());
    try {
      await mauChuKyProvider.getDanhSachMauCks().then((value) {
        if (value.data!.isNotEmpty) {
          change(value.data, status: RxStatus.success());
        } else {
          change(null, status: RxStatus.empty());
        }
      });
    } catch (exception) {
      change(null, status: RxStatus.error());
    }
    update();
  }

  void xacNhanFileCks() {
    selectedChoseFileCks.value = selectedFileCks.value;
    selectedChoseFileName.value = selectedFileName.value;
  }

  void xoaFileAnhCks() {
    selectedChoseFileCks.value = File("");
    selectedChoseFileName.value = "";
    selectedFileCks.value = File("");
    selectedFileName.value = "";
    base64FileDinhKem.value = "";
  }

  void xoaMauChuky(int idKySo) {
    Get.defaultDialog(
        title: "Xác nhận",
        content: Text("Bạn chất muốn xoá mẫu chữ ký!",
            style: GoogleFonts.roboto(fontSize: 16)),
        onConfirm: () async {
          await mauChuKyProvider.xoaMauChuKy(idKySo).then((value) {
            Map<String, dynamic> data = value;
            if (data['message'] == "Thực thi thành công") {
              Get.back();
              getDanhSachMauChuky();
              CustomSnackBar.showSuccessSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Xoá mẫu chữ ký thành công!");
            } else {
              Get.back();
              CustomSnackBar.showWarningSnackBar(
                  context: Get.context,
                  title: "Thông báo",
                  message: "Xoá mẫu chữ ký thất bại!");
            }
          });
        },
        textConfirm: "Xoá",
        textCancel: "Đóng");
  }

  void capNhatMauCks(int idKySo) async {
    await mauChuKyProvider.capNhatCks(idKySo).then((value) {
      Map<String, dynamic> result = value;
      if (result.isNotEmpty) {
        if (result["message"] == "Thực thi thành công") {
          getDanhSachMauChuky();
        }
      }
    });
  }

  void onThemMoiChuKy() async {
    await fileProvider
        .uploadFile(
            selectedChoseFileCks.value!, 'avatar', selectedChoseFileName.value!)
        .then((value) {
      print(value);
      mauChuKyProvider
          .taoMauCksMoi(value.data!.first.path!, tenChuKyEditingController.text)
          .then((result) {
        getDanhSachMauChuky();
        xoaFileAnhCks();
        tenChuKyEditingController.clear();
      });
    });
  }

  @override
  void onInit() {
    // TODO: implement onInit

    super.onInit();
    print("onInit");
  }

  @override
  void onReady() {
    // TODO: implement onReady

    super.onReady();
    getDanhSachMauChuky();
    print("onReady");
  }

  @override
  void onClose() {
    // TODO: implement onClose

    super.onClose();
    tenChuKyEditingController.clear();
  }

  @override
  void dispose() {
    // TODO: implement dispose

    super.dispose();
    tenChuKyEditingController.dispose();
  }
}
