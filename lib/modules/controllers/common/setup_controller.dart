import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/user/ctcb_kiemnhiem.dart';
import 'package:vnpt_ioffice_camau/app/model/vbde/vbde_xuly_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/login/login_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/modal_bottom.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/firebase_api.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/mauchuky/mauchuky_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/network/network_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_xuly_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/chitiet_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dscv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dsvt_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulycv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dsld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/xulyld_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/xuly_vbnb_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/dsxuly_cv_vbdi.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class SetupController extends GetxController {
  final GetStorage storage = GetStorage();
  NetworkController networkController = Get.find();
  var loginProvider = LoginProvider();
  var progressDowloadFile = 0.0.obs;
  void logOut() async {
    final SharedPreferences _prefs = await SharedPreferences.getInstance();
    await _prefs.clear();
    // if (storage.read(GetStorageKey.domainSSO) != null) {
    //   loginProvider
    //       .logOutSSO(storage.read(GetStorageKey.domainSSO))
    //       .then((value) {
    //     print(value);
    //   });
    // }
    storage.erase();
    Get.offAllNamed(Routers.LOGIN);
  }

  void hoanThanhXuLy() {
    Get.lazyPut(() => XuLyLdVbdeController());
    XuLyLdVbdeController xulyVbdeController = Get.find();
    xulyVbdeController.ldHoanTatVbden();
  }

  void chuyenLdk() {
    Get.lazyPut(() => XuLyLdVbdeController());
    XuLyLdVbdeController xulyVbdeController = Get.find();
    xulyVbdeController.onChuyenldK();
  }

  void switchScreen(String code) async {
    switch (code) {
      case "vbdeDuyet":
        Get.toNamed(Routers.VBDEN);
        break;
      case "vbdeXuLy":
        Get.toNamed(Routers.XULYCVVBDE);
        break;
      case "vbdeVanThu":
        Get.toNamed(Routers.SOVBDENVT);
        break;
      case "vbdiDuyet":
        Get.toNamed(Routers.DUYETVBDI);
        break;
      case "vbdiXuLy":
        Get.toNamed(Routers.DSCVVBDI);
        break;
      case "VBNB":
        Get.toNamed(Routers.VBNB);
        //CustomSnackBar.showDialogGrowing();
        break;
      case "TTDH":
        Get.toNamed(Routers.TTDH);
        break;
      case "LCT":
        Get.toNamed(Routers.LICHCONGTAC);
        break;
      case "vbdeTheoDoi":
        Get.toNamed(Routers.THEODOIVBDE);
        break;
      default:
        break;
    }
  }

  // open File
  Future openFile(
      {required String url, String? fileName, int? indexFile}) async {
    //final file = await pickFile();
    try {
      Get.dialog(
        Center(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(fileName!),
                  SizedBox(height: 20),
                  Obx(
                    () => LinearProgressIndicator(
                      value: progressDowloadFile.value / 100,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      backgroundColor: Colors.grey[300],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      final file = await downloadFile(url, fileName!);
      if (file == null) return;
      Get.back();
      OpenFilex.open(file.path);
    } catch (exception) {}
  }

// multiple files
  Future<List<File?>> pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'png',
          'jpg',
          'odt',
          'xlsx',
          'xls'
        ]);
    List<File?> fileList = [];
    if (result != null) {
      List<File> files = result.paths.map((path) => File(path!)).toList();
      fileList.addAll(files);
      return fileList;
    } else {
      return fileList;
    }
  }

  //update
  void updateProgressFile(double value) {
    progressDowloadFile.value = value;
  }

  //  download file
  Future<File?> downloadFile(String url, String name) async {
    try {
      final appStorage = await getTemporaryDirectory();
      final file = File('${appStorage.path}/$name');
      if (await file.exists()) {
        return file;
      } else {
        final response = await Dio().download(url, file.path,
            onReceiveProgress: ((count, total) {
          if (total != -1) {
            double progress = (count / total * 100);
            updateProgressFile(progress);
          }
        }));

        return file;
      }
    } catch (exception) {
      print(exception);
    }
  }

  // chia sẽ file
  Future<void> shareFile(String filePath, String name) async {
    final file = await downloadFile(filePath, name);
    if (await file!.exists()) {
      await Share.shareXFiles([XFile(file!.path)]);
    } else {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "File không tồn tại");
    }
  }

  // download file vào thư mục download
  Future<void> downloadFileDivice(String url, String name) async {
    Directory? directory;
    if (Platform.isAndroid) {
      directory = Directory('/storage/emulated/0/Download'); // Thư mục Download
    } else if (Platform.isIOS) {
      directory = await getApplicationDocumentsDirectory();
    }

    final filePath = '${directory!.path}/$name';
    final file = File(filePath);
    if (await file.exists()) {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "File đã tồn tại trong máy của bạn");
    } else {
      Get.dialog(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: 300,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Tiêu đề
                const Text(
                  "Đang tải file...",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),

                // Icon loading
                const CircularProgressIndicator(),
                const SizedBox(height: 20),

                // Thanh tiến trình và phần trăm
                Obx(() => Column(
                      children: [
                        LinearProgressIndicator(
                          value: progressDowloadFile.value,
                          minHeight: 6,
                          backgroundColor: Colors.grey.shade300,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          "${(progressDowloadFile.value * 100).toStringAsFixed(0)}%",
                          style: TextStyle(fontSize: 16),
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ),
        barrierDismissible: false,
      );
      // Tự đóng dialog khi xong
      final response = await Dio().download(url, file.path,
          onReceiveProgress: ((count, total) {
        if (total != -1) {
          double progress = (count / total);
          progressDowloadFile.value = progress;
        }
      }));
      Get.back();
    }
  }

  void ldChuyenXuly() {
    Get.lazyPut(() => XuLyLdVbdeController());
    XuLyLdVbdeController xulyVbdeController = Get.find();
    List<DetailCBTree> arrayCB = xulyVbdeController.getCBchoosed();
    List<Widget> listCBdaChon = <Widget>[];
    for (var item in arrayCB) {
      listCBdaChon.add(Container(
        decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
        child: ListTile(
          title: Text(item.ten.toString()),
          subtitle: RichText(
              text: TextSpan(
                  children: <TextSpan>[
                TextSpan(text: 'Chức vụ: ${item.tenChucVu}'),
                const TextSpan(text: " \n"),
                TextSpan(text: 'vai trò: ${item.vaiTroChuyen}'),
              ],
                  style: item.maYeuCau == '2'
                      ? const TextStyle(color: Colors.red)
                      : (item.maYeuCau == '3'
                          ? const TextStyle(color: Colors.blue)
                          : const TextStyle(color: Colors.black)))),
          textColor: item.maYeuCau == '2'
              ? Colors.red
              : (item.maYeuCau == '3' ? Colors.blue : Colors.black),
        ),
      ));
    }
    var isCheck = arrayCB.isNotEmpty;
    if (isCheck) {
      CustomModalButton.modalButton(
          context: Get.context,
          listItem: listCBdaChon,
          sendMethod: () => xulyVbdeController.confirmLDchuyenXuLy(
              arrayCB,
              storage.read(GetStorageKey.maVanBanDenKc).toInt(),
              storage.read(GetStorageKey.maXulyDenKc).toInt(),
              storage.read(GetStorageKey.trichYeu),
              0),
          title: "Danh sách cán bộ đã chọn");
    } else {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo!",
          message: "Vui lòng chọn cán bộ!");
    }
  }

  void chuyenXuLyVbnb() {
    Get.lazyPut(() => XuLyVbnbController());
    XuLyVbnbController xlVbnbController = Get.find();
    List<DetailCBTree> arrayCB = xlVbnbController.getCBchoosedCv();
    List<Widget> listCBdaChon = <Widget>[];
    for (var item in arrayCB) {
      listCBdaChon.add(Container(
        decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
        child: ListTile(
          title: Text(item.ten.toString()),
          subtitle: RichText(
              text: TextSpan(
                  children: <TextSpan>[
                TextSpan(text: 'Chức vụ: ${item.tenChucVu}'),
              ],
                  style: item.maYeuCau == '2'
                      ? const TextStyle(color: Colors.red)
                      : (item.maYeuCau == '3'
                          ? const TextStyle(color: Colors.blue)
                          : const TextStyle(color: Colors.black)))),
          textColor: item.maYeuCau == '2'
              ? Colors.red
              : (item.maYeuCau == '3' ? Colors.blue : Colors.black),
        ),
      ));
    }
    var isCheck = arrayCB.isNotEmpty;
    if (isCheck) {
      CustomModalButton.modalButton(
          context: Get.context,
          listItem: listCBdaChon,
          sendMethod: () {
            xlVbnbController.confirmchuyenXuLyVbnb(
                arrayCB,
                xlVbnbController.maVbnbKc,
                xlVbnbController.maVbnbGuiKc,
                xlVbnbController.trichYeu);
          },
          title: "Danh sách cán bộ đã chọn");
    } else {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo!",
          message: "Vui lòng chọn cán bộ!");
    }
  }

  void cvChuyenXuLy() {
    Get.lazyPut(() => XuLyCvVbdeController());
    XuLyCvVbdeController xulyCvVbdeController = Get.find();
    List<DetailCBTree> arrayCB = xulyCvVbdeController.getCBchoosedCv();
    List<Widget> listCBdaChon = <Widget>[];
    for (var item in arrayCB) {
      listCBdaChon.add(Container(
        decoration: const BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0))),
        child: ListTile(
          title: Text(item.ten.toString()),
          subtitle: RichText(
              text: TextSpan(
                  children: <TextSpan>[
                TextSpan(text: 'Chức vụ: ${item.tenChucVu}'),
                const TextSpan(text: " \n"),
                TextSpan(text: 'vai trò: ${item.vaiTroChuyen}'),
              ],
                  style: item.maYeuCau == '2'
                      ? const TextStyle(color: Colors.red)
                      : (item.maYeuCau == '3'
                          ? const TextStyle(color: Colors.blue)
                          : const TextStyle(color: Colors.black)))),
          textColor: item.maYeuCau == '2'
              ? Colors.red
              : (item.maYeuCau == '3' ? Colors.blue : Colors.black),
        ),
      ));
    }
    var isCheck = arrayCB.isNotEmpty;
    if (isCheck) {
      CustomModalButton.modalButton(
          context: Get.context,
          listItem: listCBdaChon,
          sendMethod: () => xulyCvVbdeController.confirmCvchuyenXuLy(
              arrayCB,
              storage.read(GetStorageKey.maVanBanDenKc).toInt(),
              storage.read(GetStorageKey.maXulyDenKc).toInt(),
              storage.read(GetStorageKey.trichYeu),
              0),
          title: "Danh sách cán bộ đã chọn");
    } else {
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo!",
          message: "Vui lòng chọn cán bộ!");
    }
  }

  void cvNhapYkien() {
    Get.lazyPut(() => XuLyCvVbdeController());
    XuLyCvVbdeController xulyCvVbdeController = Get.find();
    xulyCvVbdeController.cvNhapYKien();
  }

  // Module văn bản đi
  /// chuyển văn thư
  void onPressVt() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onSendVanThu();
  }

  /// chuyển lãnh đạo khác
  void onPressLdk() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onSendLdkVbdi();
  }
  // chuyển chuyển viên

  void onPressCv() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onSendCvVbdi();
  }

  // Chuyên viên chuyển xử lý
  void onPressCvChuyenLd() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onCvChuyenLanhDao();
  }

  void onPressCvChuyenCv() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onCvChuyenCv();
  }

  void onPressCvChuyenVt() {
    Get.lazyPut(() => XulyLdVbdiController());
    XulyLdVbdiController xulyldVbdiController = Get.find();
    xulyldVbdiController.onCvChuyenVt();
  }

  Icon showIconAppBar(String pathScreen) {
    Icon resultIcon = const Icon(Icons.check);
    switch (pathScreen) {
      case Routers.TTDH:
        resultIcon = const Icon(Icons.note_add_outlined);
        break;
      case Routers.TTDHPHANHOI:
        resultIcon = const Icon(Icons.send);
        break;
      default:
    }
    return resultIcon;
  }

  // module thông tin điều hành
  void onPressReplys() {
    Get.lazyPut(() => TtdhXuLyController());
    TtdhXuLyController ttdhXulyController = Get.find();
    if (ttdhXulyController.isLoaiTTDH == 0) {
      ttdhXulyController.onPressReply();
    } else if (ttdhXulyController.isLoaiTTDH == 1) {
      ttdhXulyController.onPressReplyAll();
    } else if (ttdhXulyController.isLoaiTTDH == 2) {
      ttdhXulyController.onPressForward();
    } else {
      ttdhXulyController.onPressAddd();
    }
  }

  void isCheckXuLy(String pathScreen) {
    switch (pathScreen) {
      case Routers.HOATTATVBDE:
        hoanThanhXuLy();
        break;
      case Routers.DUYETXULYVBDE:
        ldChuyenXuly();
        break;
      case Routers.CHUYENLDKHAC:
        chuyenLdk();
        break;
      case Routers.CVCHUYENXL:
        cvChuyenXuLy();
        break;
      case Routers.CVNHAPYKIEN:
        cvNhapYkien();
        break;
      case Routers.CHUYENVTVBDI:
        onPressVt();
        break;
      case Routers.CHUYENLDKVBDI:
        onPressLdk();
        break;
      case Routers.CHUYENCVVBDI:
        onPressCv();
        break;
      case Routers.CVCHUYENLD:
        onPressCvChuyenLd();
        break;
      case Routers.CVCHUYENCV:
        onPressCvChuyenCv();
        break;
      case Routers.CVCHUYENVT:
        onPressCvChuyenVt();
        break;
      case Routers.VBNBCHUYENXL:
        chuyenXuLyVbnb();
        break;
      case Routers.TTDH:
        Get.toNamed(Routers.THEMTTDH,
            arguments: {"maTtdhguikc": 0, "isLoaiTTDH": 3});
        break;
      case Routers.TTDHPHANHOI:
        onPressReplys();
        break;
      case Routers.THEMTTDH:
        onPressReplys();
        break;

      default:
        break;
    }
  }

  void backScreenVbdi() {
    if (Get.routing.previous == Routers.DUYETVBDI) {
      Get.lazyPut(() => DuyetLDVbdiController());
      DuyetLDVbdiController duyetLDVbdiController = Get.find();
      duyetLDVbdiController
          .getDsVbdiByIndex(duyetLDVbdiController.indexTabGobal.value);
      Get.offNamed(Routers.DUYETVBDI);
    } else if (Get.routing.previous == Routers.DSCVVBDI) {
      Get.lazyPut(() => DsCvVbdiController());
      DsCvVbdiController dsXuLyCvVbdiController = Get.find();
      dsXuLyCvVbdiController.loadDataByIndexTab(Get.arguments['indexTab']);
      Get.offNamed(Routers.DSCVVBDI,
          arguments: {"index": Get.arguments['indexTab']});
    } else if (Get.routing.current == Routers.DETAILSVBDI &&
        Get.routing.previous == "null") {
      Get.lazyPut(() => DuyetLDVbdiController());
      DuyetLDVbdiController duyetLDVbdiController = Get.find();
      duyetLDVbdiController
          .getDsVbdiByIndex(duyetLDVbdiController.indexTabGobal.value);
      Get.offNamed(Routers.DUYETVBDI);
    } else {
      Get.back();
    }
  }

  void backScreeenVbde() {
    if (Get.routing.previous == Routers.DUYETXULYVBDE) {
      Get.lazyPut(() => DuyetVbdeController());
      DuyetVbdeController duyetVbdeController = Get.find();
      duyetVbdeController.getDsLdByIndex(Get.arguments['indexTab']);
      Get.offNamed(Routers.DUYETXULYVBDE);
    } else if (Get.routing.previous == Routers.XULYCVVBDE) {
      Get.lazyPut(() => DsCvVbdeController());
      DsCvVbdeController dsCvVbdeController = Get.find();
      dsCvVbdeController.getDsCvByIndexTab(Get.arguments['indexTab']);
      Get.offNamed(Routers.XULYCVVBDE,
          arguments: {"indexTab": Get.arguments['indexTab']});
    } else if (Get.routing.previous == Routers.SOVBDENVT) {
      Get.lazyPut(() => VanThuController());
      VanThuController vanThuController = Get.find();
      vanThuController.getDsVanBanByIndexTab(Get.arguments['indexTab']);
      Get.offNamed(Routers.SOVBDENVT,
          arguments: {"indexTab": Get.arguments['indexTab']});
    } else {
      Get.back();
    }
  }

  void isCheckButtonBack(String pathScreen) {
    switch (pathScreen) {
      case Routers.DETAILSVBDI:
        backScreenVbdi();
        break;
      case Routers.DETAILVBDE:
        backScreeenVbde();
        break;
      case Routers.THEMMOIVBDEVT:
        Get.lazyPut(() => VanThuController());
        VanThuController vanThuController = Get.find();
        vanThuController.getDsVanBanByIndexTab(Get.arguments['indexTab']);
        Get.offNamed(Routers.SOVBDENVT,
            arguments: {"indexTab": Get.arguments['indexTab']});
        break;
      default:
        Get.back();
        break;
    }
  }

  // seting User
  void setUserSettings() async {
    loginProvider.auGTSM().then((res) {
      if (res.data!.isNotEmpty) {}
    });
  }

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void onRead() async {
    super.onReady();
  }
}
