import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/access_token.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/auth_model.dart';
import 'package:vnpt_ioffice_camau/app/model/auth/dsdvchuquan_model.dart';
import 'package:vnpt_ioffice_camau/app/model/login/model_version.dart';
import 'package:vnpt_ioffice_camau/app/model/user/ctcb_kiemnhiem.dart';
import 'package:vnpt_ioffice_camau/app/provider/login/login_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/utils/full_screen_dialog_loader.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';
import 'package:uni_links/uni_links.dart';

class LoginController extends GetxController {
  final loginProvider = LoginProvider();
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();
  TextEditingController usernameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  FlutterAppAuth appAuth = FlutterAppAuth();
  StreamSubscription? _sub;
  var username = '';
  var password = '';
  var nameDomain = "Chọn đơn vị".obs;
  var isShowLoginSso = false.obs;
  var ssoClientId = ''.obs;
  var ssoClientSecret = ''.obs;
  var ssoLoginUri = ''.obs;
  var ssoRedirectUri = ''.obs;

  Rx<bool> isRememberLogin = false.obs;
  String accessToken = '';
  Rx<int> currenIndexCtcb = 0.obs;
  var storage = GetStorage();
  var isLoaiDangNhan =
      Get.arguments != null ? Get.arguments["loaiDangNhap"] : "default";
  LoginController();
  @override
  void onInit() async {
    super.onInit();
    _initDeepLinkListener();
    final SharedPreferences _pref = await SharedPreferences.getInstance();
    String? domainAPi = _pref.getString("apiDomain");
    String? _prefUserName = _pref.getString(GetStorageKey.userName);
    String? _prefPassWord = _pref.getString(GetStorageKey.passWord);
    if (isLoaiDangNhan == 'doikiemnhiem') {
      autoLogin(storage.read(GetStorageKey.userName),
          storage.read(GetStorageKey.passWord)!, isLoaiDangNhan);
    } else {
      if (domainAPi != null && _prefPassWord != null && _prefUserName != null) {
        autoLogin(_prefUserName!, _prefPassWord!, isLoaiDangNhan);
      } else {}
    }
  }

  void _initDeepLinkListener() async {
    _sub = await uriLinkStream.listen((Uri? uri) {
      if (uri != null) {
        print(uri.toString());
        handleOpenURL(uri.toString());
        // Extract the path and navigate using GetX
      }
    }, onError: (err) {
      print('Failed to receive deep link: $err');
    });
  }

  Future<void> handleOpenURL(String url) async {
    String deeplink = url.split('://')[0];
    if (deeplink == 'ioffice') {
      String path = url.split('://')[1];
      List<String> arrayParam = path.split('/');
      String authen = arrayParam[0];
      String username = arrayParam[1];
      String key = '';

      for (String item in arrayParam) {
        if (item == authen || item == username) continue;
        key += '$item/';
      }

      key = key.substring(0, key.length - 1);
      final SharedPreferences _pref = await SharedPreferences.getInstance();
      loginProvider
          .getDanhSachCTCBSSO(username, key, 'iOfficeCM@20@@')
          .then((value) => {
                if (value.data.isNotEmpty &&
                    value.data[0].refreshToken.isNotEmpty)
                  {
                    loginProvider
                        .getAccessToken(value.data[0].refreshToken)
                        .then((result) => {
                              storage.write(GetStorageKey.userName, username),
                              storage.write(GetStorageKey.passWord, password),
                              // ham lấy tham số theo cán bộ
                              _pref.setString(
                                  "accessToken", result.data.accessToken),
                              _pref.setString(
                                  "refreshToken", result.data.refreshToken),
                              _pref.setInt("expiredTime", result.data.expire),
                              _pref.setString(
                                  "dateLogin", DateTime.now().toString()),
                              storage.write(GetStorageKey.refreshToken,
                                  result.data.refreshToken),
                              storage.write(GetStorageKey.expiredTimeToken,
                                  result.data.expire),
                              storage.write(GetStorageKey.hoVaTen,
                                  value.data[0].hoVaTenCanBo),
                              storage.write(GetStorageKey.chucVu,
                                  value.data[0].tenChucVu),
                              storage.write(GetStorageKey.maCtcbKc,
                                  value.data[0].maCtcbKc.toInt()),
                              storage.write(GetStorageKey.maDonVi,
                                  value.data[0].maDonVi!.toInt()),
                              storage.write(GetStorageKey.diDongCanBo,
                                  value.data[0].diDongCanBo),
                              storage.write(GetStorageKey.maCanBo,
                                  value.data[0].maCanBo!.toInt()),

                              storage.write(GetStorageKey.loaiKySoSim,
                                  value.data[0].loaiKySoSim),
                              loginProvider
                                  .getDsThamSo(
                                      value.data[0].maCtcbKc.toString())
                                  .then((ts) {
                                // ghi log đăng nhập
                                loginProvider.ghiLogDn(
                                    value.data[0].maCtcbKc.toInt(), username);
                                // lấy danh sách đơn vị
                                loginProvider
                                    .getDsDvCQ(value.data[0].maCtcbKc.toInt())
                                    .then((donvi) {
                                  var result = donvi.firstWhere((element) =>
                                      element.laDvDinhDanh!.toInt() == 1);
                                  storage.write(GetStorageKey.maDonViQuanTri,
                                      result.maDonViKc!.toInt());
                                  storage.write(GetStorageKey.tenDonViQuanTri,
                                      result.tenDonVi);
                                  storage.write(GetStorageKey.maDinhDanhQuanTri,
                                      result.maDinhDanh);
                                  var domainFile = result.domainFile != null
                                      ? result.domainFile
                                      : "|";
                                  storage.write(GetStorageKey.domainFile,
                                      domainFile!.split('|')[0]);
                                  storage.write(GetStorageKey.filePartition,
                                      domainFile!.split('|')[1]);
                                });
                                // get token and save token with mactcb
                                _pref.setString(GetStorageKey.isLogin, "1");
                                loginProvider
                                    .auKTQCVLDVTCCCB(
                                        value.data[0].maCtcbKc.toInt())
                                    .then((isQuyen) => {
                                          storage.write(GetStorageKey.isLanhDao,
                                              isQuyen.lanhDao),
                                          storage.write(GetStorageKey.isVanThu,
                                              isQuyen.vanThu),
                                          storage.write(
                                              GetStorageKey.isChuyenVien,
                                              isQuyen.chuyenVien),
                                          Get.offNamed(Routers.HOME)
                                        });
                              }),
                            }),
                  }
                else
                  {
                    CustomSnackBar.showWarningSnackBar(
                        context: Get.context,
                        title: "Thông báo",
                        message: "Đăng nhập thất bại"),
                  }
              });
    }
  }

  List<String> listDomain = [
    "https://camau-api.vnptioffice.vn",
    "https://camauv4-api.vnptioffice.vn",
  ];

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void dispose() {
    super.dispose();
    usernameController.dispose();
    passwordController.dispose();
  }

  void changeRememberlogin(bool isRemember) {
    isRememberLogin.value = isRemember;
  }

  String? validateUserName(String value) {
    if (value.isEmpty) {
      return 'Vui lòng nhập tài khoản';
    }
    return null;
  }

  String? validatePassword(String value) {
    if (value.isEmpty) {
      return 'Vui lòng nhập mật khẩu';
    }
    return null;
  }

  void changeDomain(int index) async {
    final SharedPreferences _pref = await SharedPreferences.getInstance();
    if (index == 0) {
      nameDomain.value = "Cà Mau";
      isShowLoginSso.value = true;
    } else if ((index == 1)) {
      nameDomain.value = "Cà Mau 1";
      isShowLoginSso.value = false;
    }
    if (_pref.getString("apiDomian") != null) {
      _pref.remove("apiDomain");
    }
    _pref.setString('apiDomain', listDomain[index]);
    storage.write(GetStorageKey.domainApi, listDomain[index]);
    Get.back();
  }

  void autoLogin(
      String ioUserName, String ioPassWord, String isLoaiDangNhap) async {
    final SharedPreferences _pref = await SharedPreferences.getInstance();
    FullScreenDialogLoader.showDialog();
    await loginProvider.getDanhSachCTCB(ioUserName, ioPassWord).then((value) {
      if (isLoaiDangNhan == 'doikiemnhiem') {
        var itemKiemNhiem = value.data.firstWhere((element) =>
            element.maCtcbKc.toInt().toString() ==
            storage.read(GetStorageKey.maCtcbKcStore).toString());
        int idx = value.data.indexOf(itemKiemNhiem);
        currenIndexCtcb.value = idx == -1 ? 0 : idx;
      }

      if (value.data.isEmpty) {
        FullScreenDialogLoader.cancleDialog();
        CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: AppString.errorLogin,
        );
        _pref.clear();

        Get.toNamed(Routers.LOGIN);
      } else {
        if (value.data[currenIndexCtcb.value].refreshToken.isNotEmpty) {
          loginProvider
              .getAccessToken(value.data[currenIndexCtcb.value].refreshToken)
              .then((result) => {
                    // ham lấy tham số theo cán bộ
                    _pref.setString("accessToken", result.data.accessToken),
                    _pref.setString("refreshToken", result.data.refreshToken),
                    _pref.setInt("expiredTime", result.data.expire),
                    _pref.setString("dateLogin", DateTime.now().toString()),
                    storage.write(
                        GetStorageKey.refreshToken, result.data.refreshToken),
                    storage.write(GetStorageKey.hoVaTen,
                        value.data[currenIndexCtcb.value].hoVaTenCanBo),
                    storage.write(
                        GetStorageKey.chucVu, value.data[0].tenChucVu),
                    storage.write(GetStorageKey.maCtcbKc,
                        value.data[currenIndexCtcb.value].maCtcbKc.toInt()),
                    storage.write(GetStorageKey.maDonVi,
                        value.data[currenIndexCtcb.value].maDonVi!.toInt()),
                    storage.write(GetStorageKey.diDongCanBo,
                        value.data[currenIndexCtcb.value].diDongCanBo),
                    storage.write(GetStorageKey.maCanBo,
                        value.data[currenIndexCtcb.value].maCanBo!.toInt()),

                    storage.write(GetStorageKey.loaiKySoSim,
                        value.data[currenIndexCtcb.value].loaiKySoSim),
                    loginProvider
                        .getDsThamSo(value.data[currenIndexCtcb.value].maCtcbKc
                            .toString())
                        .then((ts) {
                      // ghi log đăng nhập
                      loginProvider.ghiLogDn(
                          value.data[currenIndexCtcb.value].maCtcbKc.toInt(),
                          username);

                      loginProvider
                          .getDsDvCQ(value.data[currenIndexCtcb.value].maCtcbKc
                              .toInt())
                          .then((donvi) {
                        var result = donvi.firstWhere(
                            (element) => element.laDvDinhDanh!.toInt() == 1);
                        storage.write(GetStorageKey.maDonViQuanTri,
                            result.maDonViKc!.toInt());
                        storage.write(
                            GetStorageKey.tenDonViQuanTri, result.tenDonVi);
                        storage.write(
                            GetStorageKey.maDinhDanhQuanTri, result.maDinhDanh);
                        var domainFile =
                            result.domainFile != null ? result.domainFile : "|";
                        storage.write(GetStorageKey.domainFile,
                            domainFile!.split('|')[0]);
                        storage.write(GetStorageKey.filePartition,
                            domainFile!.split('|')[1]);
                      });
                      FullScreenDialogLoader.cancleDialog();
                      loginProvider
                          .auKTQCVLDVTCCCB(value
                              .data[currenIndexCtcb.value].maCtcbKc
                              .toInt())
                          .then((isQuyen) => {
                                storage.write(
                                    GetStorageKey.isLanhDao, isQuyen.lanhDao),
                                storage.write(
                                    GetStorageKey.isVanThu, isQuyen.vanThu),
                                storage.write(GetStorageKey.isChuyenVien,
                                    isQuyen.chuyenVien),
                                Get.offNamed(Routers.HOME)
                              });
                    }),
                  });
        } else {
          _pref.clear();
          FullScreenDialogLoader.cancleDialog();
          CustomSnackBar.showWarningSnackBar(
              context: Get.context,
              title: "Thông báo",
              message: "Đăng nhập thất bại");
          Get.toNamed(Routers.LOGIN);
        }
      }
    });
  }

  void getDanhsachcanbo() async {
    final SharedPreferences _pref = await SharedPreferences.getInstance();
    try {
      if (loginFormKey.currentState!.validate()) {
        loginFormKey.currentState!.save();
        // check auto login
        if (isRememberLogin.value) {
          _pref.setString(GetStorageKey.userName, username);
          _pref.setString(GetStorageKey.passWord, password);
        }

        await loginProvider.getDanhSachCTCB(username, password).then((value) =>
            {
              if (value.data.isNotEmpty &&
                  value.data[0].refreshToken.isNotEmpty)
                {
                  loginProvider
                      .getAccessToken(value.data[0].refreshToken)
                      .then((result) => {
                            storage.write(GetStorageKey.userName, username),
                            storage.write(GetStorageKey.passWord, password),
                            // ham lấy tham số theo cán bộ
                            _pref.setString(
                                "accessToken", result.data.accessToken),
                            _pref.setString(
                                "refreshToken", result.data.refreshToken),
                            _pref.setInt("expiredTime", result.data.expire),
                            _pref.setString(
                                "dateLogin", DateTime.now().toString()),
                            storage.write(GetStorageKey.refreshToken,
                                result.data.refreshToken),
                            storage.write(GetStorageKey.expiredTimeToken,
                                result.data.expire),
                            storage.write(GetStorageKey.hoVaTen,
                                value.data[0].hoVaTenCanBo),
                            storage.write(
                                GetStorageKey.chucVu, value.data[0].tenChucVu),
                            storage.write(GetStorageKey.maCtcbKc,
                                value.data[0].maCtcbKc.toInt()),
                            storage.write(GetStorageKey.maDonVi,
                                value.data[0].maDonVi!.toInt()),
                            storage.write(GetStorageKey.diDongCanBo,
                                value.data[0].diDongCanBo),
                            storage.write(GetStorageKey.maCanBo,
                                value.data[0].maCanBo!.toInt()),

                            storage.write(GetStorageKey.loaiKySoSim,
                                value.data[0].loaiKySoSim),
                            loginProvider
                                .getDsThamSo(value.data[0].maCtcbKc.toString())
                                .then((ts) {
                              // ghi log đăng nhập
                              loginProvider.ghiLogDn(
                                  value.data[0].maCtcbKc.toInt(), username);
                              // lấy danh sách đơn vị
                              loginProvider
                                  .getDsDvCQ(value.data[0].maCtcbKc.toInt())
                                  .then((donvi) {
                                var result = donvi.firstWhere((element) =>
                                    element.laDvDinhDanh!.toInt() == 1);
                                storage.write(GetStorageKey.maDonViQuanTri,
                                    result.maDonViKc!.toInt());
                                storage.write(GetStorageKey.tenDonViQuanTri,
                                    result.tenDonVi);
                                storage.write(GetStorageKey.maDinhDanhQuanTri,
                                    result.maDinhDanh);
                                var domainFile = result.domainFile != null
                                    ? result.domainFile
                                    : "|";
                                storage.write(GetStorageKey.domainFile,
                                    domainFile!.split('|')[0]);
                                storage.write(GetStorageKey.filePartition,
                                    domainFile!.split('|')[1]);
                              });
                              // get token and save token with mactcb
                              _pref.setString(GetStorageKey.isLogin, "1");
                              loginProvider
                                  .auKTQCVLDVTCCCB(
                                      value.data[0].maCtcbKc.toInt())
                                  .then((isQuyen) => {
                                        storage.write(GetStorageKey.isLanhDao,
                                            isQuyen.lanhDao),
                                        storage.write(GetStorageKey.isVanThu,
                                            isQuyen.vanThu),
                                        storage.write(
                                            GetStorageKey.isChuyenVien,
                                            isQuyen.chuyenVien),
                                        Get.offNamed(Routers.HOME)
                                      });
                            }),
                          }),
                }
              else
                {
                  CustomSnackBar.showWarningSnackBar(
                      context: Get.context,
                      title: "Thông báo",
                      message: "Đăng nhập thất bại"),
                }
            });
      }
    } catch (exception) {
      Get.back();
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: exception.toString());
      _pref.clear();
      Get.toNamed(Routers.LOGIN);
    }
  }

  void LoginSSO() async {
    await loginProvider.getThamSoSSO().then((value) {
      ssoClientId.value = value.data![0].giaTriMacDinh!;
      ssoClientSecret.value = value.data![1].giaTriMacDinh!;
      ssoLoginUri.value = value.data![2].giaTriMacDinh!.split('|')[0];
      ssoRedirectUri.value = value.data![2].giaTriMacDinh!.split('|')[1];
      redictDomainSso(
          value.data![0].giaTriMacDinh!,
          value.data![1].giaTriMacDinh!,
          value.data![2].giaTriMacDinh!.split('|')[0],
          value.data![2].giaTriMacDinh!.split('|')[1]);
    });
  }

  void redictDomainSso(String ssoClientId, String ssoClientSecret,
      String ssoLoginUri, String ssoRedirectUri) async {
    String authorizationEndpoint = '$ssoLoginUri/oauth2/authorize';
    String tokenEndpoint = '$ssoLoginUri/oauth2/token';
    String logout = '$ssoLoginUri/oidc/logout';
    final SharedPreferences _pref = await SharedPreferences.getInstance();
    final AuthorizationTokenResponse? result =
        await appAuth.authorizeAndExchangeCode(
      AuthorizationTokenRequest(ssoClientId, 'ioffice://com.vnpt.camau.ioffice',
          clientSecret: ssoClientSecret,
          serviceConfiguration: AuthorizationServiceConfiguration(
              authorizationEndpoint: authorizationEndpoint,
              tokenEndpoint: tokenEndpoint,
              endSessionEndpoint: logout),
          scopes: ["openid"]),
    );
    storage.write(GetStorageKey.idTokenSSO, result!.idToken);
    storage.write(GetStorageKey.domainSSO, ssoLoginUri);
    if (result != null) {
      FullScreenDialogLoader.showDialog();
      await loginProvider
          .getDanhSachCTCB('', '',
              accessTokenSSO: result!.accessToken, domainSSO: ssoLoginUri)
          .then((value) => {
                if (value.data.isNotEmpty &&
                    value.data[0].refreshToken.isNotEmpty)
                  {
                    loginProvider
                        .getAccessToken(value.data[0].refreshToken)
                        .then((result) => {
                              storage.write(GetStorageKey.userName, username),
                              storage.write(GetStorageKey.passWord, password),
                              // ham lấy tham số theo cán bộ
                              _pref.setString(
                                  "accessToken", result.data.accessToken),
                              _pref.setString(
                                  "refreshToken", result.data.refreshToken),
                              _pref.setInt("expiredTime", result.data.expire),
                              _pref.setString(
                                  "dateLogin", DateTime.now().toString()),
                              storage.write(GetStorageKey.refreshToken,
                                  result.data.refreshToken),
                              storage.write(GetStorageKey.expiredTimeToken,
                                  result.data.expire),
                              storage.write(GetStorageKey.hoVaTen,
                                  value.data[0].hoVaTenCanBo),
                              storage.write(GetStorageKey.chucVu,
                                  value.data[0].tenChucVu),
                              storage.write(GetStorageKey.maCtcbKc,
                                  value.data[0].maCtcbKc.toInt()),
                              storage.write(GetStorageKey.maDonVi,
                                  value.data[0].maDonVi!.toInt()),
                              storage.write(GetStorageKey.diDongCanBo,
                                  value.data[0].diDongCanBo),
                              storage.write(GetStorageKey.maCanBo,
                                  value.data[0].maCanBo!.toInt()),

                              storage.write(GetStorageKey.loaiKySoSim,
                                  value.data[0].loaiKySoSim),
                              loginProvider
                                  .getDsThamSo(
                                      value.data[0].maCtcbKc.toString())
                                  .then((ts) {
                                // ghi log đăng nhập
                                loginProvider.ghiLogDn(
                                    value.data[0].maCtcbKc.toInt(), username);
                                // lấy danh sách đơn vị
                                loginProvider
                                    .getDsDvCQ(value.data[0].maCtcbKc.toInt())
                                    .then((donvi) {
                                  var result = donvi.firstWhere((element) =>
                                      element.laDvDinhDanh!.toInt() == 1);
                                  storage.write(GetStorageKey.maDonViQuanTri,
                                      result.maDonViKc!.toInt());
                                  storage.write(GetStorageKey.tenDonViQuanTri,
                                      result.tenDonVi);
                                  storage.write(GetStorageKey.maDinhDanhQuanTri,
                                      result.maDinhDanh);
                                  var domainFile = result.domainFile != null
                                      ? result.domainFile
                                      : "|";
                                  storage.write(GetStorageKey.domainFile,
                                      domainFile!.split('|')[0]);
                                  storage.write(GetStorageKey.filePartition,
                                      domainFile!.split('|')[1]);
                                });
                                // get token and save token with mactcb
                                _pref.setString(GetStorageKey.isLogin, "1");
                                loginProvider
                                    .auKTQCVLDVTCCCB(
                                        value.data[0].maCtcbKc.toInt())
                                    .then((isQuyen) => {
                                          storage.write(GetStorageKey.isLanhDao,
                                              isQuyen.lanhDao),
                                          storage.write(GetStorageKey.isVanThu,
                                              isQuyen.vanThu),
                                          storage.write(
                                              GetStorageKey.isChuyenVien,
                                              isQuyen.chuyenVien),
                                          FullScreenDialogLoader.cancleDialog(),
                                          Get.offNamed(Routers.HOME)
                                        });
                              }),
                            }),
                  }
                else
                  {
                    CustomSnackBar.showWarningSnackBar(
                        context: Get.context,
                        title: "Thông báo",
                        message: "Đăng nhập thất bại"),
                  }
              });
    } else {
      FullScreenDialogLoader.cancleDialog();
      CustomSnackBar.showWarningSnackBar(
          context: Get.context,
          title: "Thông báo",
          message: "Đăng nhập thất bại");
    }
  }

  // check version update
  // void isVersionUpdate() async {
  //   PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //   String currentVersion = packageInfo.version;
  //   String contents = await rootBundle.loadString('data/version.json');

  //   // Parse JSON
  //   final data = jsonDecode(contents);
  //   // Parse JSON thành Map
  //   ModelVersion oldVersion = ModelVersion.fromJson(data);
  //   print("jsonverion${oldVersion.versions!.last.ios}");
  //   print(currentVersion);
  // }
}
