PODS:
  - AppAuth (1.7.4):
    - AppAuth/Core (= 1.7.4)
    - AppAuth/ExternalUserAgent (= 1.7.4)
  - AppAuth/Core (1.7.4)
  - AppAuth/ExternalUserAgent (1.7.4):
    - AppAuth/Core
  - Firebase/CoreOnly (10.15.0):
    - FirebaseCore (= 10.15.0)
  - Firebase/Messaging (10.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.15.0)
  - firebase_core (2.19.0):
    - Firebase/CoreOnly (~> 10.15.0)
    - FlutterMacOS
  - firebase_messaging (14.7.1):
    - Firebase/CoreOnly (~> 10.15.0)
    - Firebase/Messaging (~> 10.15.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (10.15.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.16.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.16.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.15.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - flutter_appauth (0.0.1):
    - AppAuth (= 1.7.4)
    - FlutterMacOS
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_appauth (from `Flutter/ephemeral/.symlinks/plugins/flutter_appauth/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_appauth:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_appauth/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  AppAuth: 182c5b88630569df5acb672720534756c29b3358
  Firebase: 66043bd4579e5b73811f96829c694c7af8d67435
  firebase_core: 3d8c7ecf2bfe5c96034ee3d8f3e6194fbac34797
  firebase_messaging: 01e5393e10b53d15c1897bed88be2872ba941006
  FirebaseCore: 2cec518b43635f96afe7ac3a9c513e47558abd2e
  FirebaseCoreInternal: 26233f705cc4531236818a07ac84d20c333e505a
  FirebaseInstallations: b822f91a61f7d1ba763e5ccc9d4f2e6f2ed3b3ee
  FirebaseMessaging: 0c0ae1eb722ef0c07f7801e5ded8dccd1357d6d4
  flutter_appauth: 712a012ba667acb296423285ae6425465bafe34a
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  share_plus: 1fa619de8392a4398bfaf176d441853922614e89
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.15.2
